package com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement.dto.norate;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 具体数据
 * <AUTHOR>
 * @Date 2020/9/11 11:48
 */
@Getter
@Setter
@ToString
public class CardSettlementDetail {

    //渠道
    private String channel;
    //交易码
    private String transactionCode;
    //交易描述
    private String transactionCodeDesc;
    //入账日期
    private String postDate;
    //清算金额
    private BigDecimal settlementAmount;
    //清算币种
    private String settlementCurrencyCode;
    //清算笔数
    private Integer settlementCount;
}
