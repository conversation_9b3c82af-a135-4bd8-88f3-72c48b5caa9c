package com.anytech.anytxn.accounting.batch.job.report.step.dayinstallfree;

import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.dayinstallfree.dto.InstallFeeReport;
import com.anytech.anytxn.accounting.batch.job.report.step.dayinstallfree.model.InstallFreeDetail;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallProductInfo;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoSelfMapper;
import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 每日分期手续费
 * <AUTHOR>
 * @date 2020/9/15
 */
public class DayInstallFreeReportTasklet extends AbstractReportTasklet<InstallOrder, InstallFeeReport> {

    @Autowired
    private InstallOrderSelfMapper installOrderSelfMapper;
    @Autowired
    private InstallProductInfoSelfMapper installProductInfoSelfMapper;

    public DayInstallFreeReportTasklet(BaseConfigurer baseConfigurer) {
        super(baseConfigurer);
    }

    @Override
    protected List<InstallOrder> loadModels() {
        if (reportConfigurer.isAbleReport()) {
            return installOrderSelfMapper.selectAllFree();
        }
        return null;

    }

    @Override
    protected List<InstallFeeReport> modelToReport(List<InstallOrder> models) {

        List<InstallFreeDetail> result = Lists.newArrayList();
        for (InstallOrder temple : models) {
            InstallFreeDetail detail = new InstallFreeDetail();
            detail.setProductNum(temple.getProductCode());
            detail.setCurrCode(temple.getInstallmentCcy());
            InstallProductInfo productInfo = installProductInfoSelfMapper.selectByIndex(temple.getOrganizationNumber(), temple.getProductCode());
            detail.setDescription(productInfo.getProductDesc());

            // 未入账金额
            detail.setNoFreeAmount(temple.getUnpostedFeeAmount());
            // 已入账金额
            detail.setFreeAmount(temple.getTotalFeeAmount());

            result.add(detail);
        }

        return Lists.newArrayList(InstallFeeReport.resetReportData(result));
    }
}
