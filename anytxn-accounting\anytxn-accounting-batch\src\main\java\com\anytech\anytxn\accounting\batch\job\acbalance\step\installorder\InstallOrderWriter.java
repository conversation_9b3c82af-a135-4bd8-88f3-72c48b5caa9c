package com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder;

import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlinsbalSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlinsbal;
import com.google.common.collect.Lists;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlinsbalDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3
 *
 * 分期余额写入
 */
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class InstallOrderWriter implements ItemWriter<AccountantGlinsbalDTO> {

    @Autowired
    private AccountantGlinsbalSelfMapper tAmsGlinsbalSelfMapper;

    @Override
    public void write(Chunk<? extends AccountantGlinsbalDTO> chunk) throws Exception {
        List<? extends AccountantGlinsbalDTO> items = chunk.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            tAmsGlinsbalSelfMapper.insertBatch(BeanMapping.copyList(Lists.newArrayList(items), AccountantGlinsbal.class));
        }
    }

}
