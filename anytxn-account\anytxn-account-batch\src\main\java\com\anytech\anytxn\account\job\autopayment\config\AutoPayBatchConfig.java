package com.anytech.anytxn.account.job.autopayment.config;

import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.account.job.autopayment.step.AutoPayProcessor;
import com.anytech.anytxn.account.job.autopayment.step.AutoPayReader;
import com.anytech.anytxn.account.job.autopayment.step.AutoPayWriter;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
/**
 *
 * <AUTHOR>
 * @date 2019-09-02
 **/

@Configuration
public class AutoPayBatchConfig {

    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;

    @Value("${anytxn.batch.stmtProcessJob.stmtStep.chunk-limit:1000}")
    private Integer chunkLimit;

    @Value("${anytxn.batch.stmtProcessJob.stmtStep.page-size:1000}")
    private Integer pageSize;

    /**
     * 约定扣款处理 Job配置
     * @param step
     * @return
     */
    @Bean
    public Job autoPaymentJob(@Qualifier("autoPayStep") Step step) {
        return jobs.get("autoPaymentJob")
                .start(step)
                .build();
    }

    /**
     * 约定扣款处理 slaveStep
     * @param autoPayReader
     * @param autoPayProcess
     * @param autoPayWriter
     * @return
     */
    @Bean
    public Step autoPayStep(
            @Qualifier("autoPayReader") AutoPayReader autoPayReader,
            @Qualifier("autoPayProcess") AutoPayProcessor autoPayProcess,
            @Qualifier("autoPayWriter") AutoPayWriter autoPayWriter) {
        return steps.get("autoPayStep")
                .<AutoEbitSignUpInforDTO, AutoPaymentBO>chunk(chunkLimit)
                .reader(autoPayReader).processor(autoPayProcess)
                .writer(autoPayWriter).build();
    }

    @Bean
    @StepScope
    public AutoPayReader autoPayReader(@Qualifier("businessDataSource") DataSource dataSource) {
        AutoPayReader reader = new AutoPayReader(dataSource);
        reader.setDataSource(dataSource);
        reader.setPageSize(pageSize);
        //记录状态信息和断点
        reader.setSaveState(true);
        return reader;
    }

    @Bean
    @StepScope
    public AutoPayProcessor autoPayProcess() {
        return new AutoPayProcessor();
    }

    @Bean
    @StepScope
    public AutoPayWriter autoPayWriter() {
        return new AutoPayWriter();
    }
}
