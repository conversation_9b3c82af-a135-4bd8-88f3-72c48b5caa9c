package com.anytech.anytxn.account.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 还款文件配置
 * <AUTHOR>
 * @date 2021/1/13
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.account.re.payment")
public class RePaymentFileConfigurer extends AutoPaymentFileConfigurer {

    private int errorSli = 0;
}
