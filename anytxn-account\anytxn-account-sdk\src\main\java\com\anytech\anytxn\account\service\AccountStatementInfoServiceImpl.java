package com.anytech.anytxn.account.service;


import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.base.service.IAccountStatementInfoService;
import com.anytech.anytxn.business.base.transaction.domain.dto.CardStatementDTO2;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.LiabilityEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.ModifyPaymentDueDateDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateTopDownReferenceSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CorporateTopDownReference;

import com.anytech.anytxn.business.base.transaction.domain.dto.CardStatementDTO;
import com.anytech.anytxn.business.dao.transaction.mapper.DisputedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCurrencyInfo;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.ICardProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;

import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-09-30 11:45
 */
@Service
public class AccountStatementInfoServiceImpl implements IAccountStatementInfoService {

    private static Logger log = LoggerFactory.getLogger(AccountStatementInfoServiceImpl.class);

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;
    @Autowired
    private AccountStatementInfoMapper accountStatementInfoMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private ICardProductInfoService cardProductInfoService;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private DisputedTransactionSelfMapper disputedTransactionSelfMapper;
    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;
    @Autowired
    private CorporateTopDownReferenceSelfMapper corporateTopDownReferenceSelfMapper;
    @Autowired
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;
    @Autowired
    private ParmCardCurrencyInfoSelfMapper parmCardCurrencyInfoSelfMapper;
    @Autowired
    private ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper;

    @Override
    public List<AccountStatementInfoDTO> findLastedStatementInfo(String accountManageInfoId, String cardNumber) {
        log.info("根据管理账户id查询最近24期账单,管理账户id：{},卡号：{}", accountManageInfoId, cardNumber);
        if (StringUtils.isAllBlank(accountManageInfoId, cardNumber)) {
            log.error("查询账单信息，管理账户号或卡号均为空！");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL);
        }
        if (StringUtils.isBlank(accountManageInfoId)) {
            //通过卡找账
            accountManageInfoId = findMidByCardNumber(cardNumber);
        }
        List<AccountStatementInfoDTO> resultList = null;

        try {
            List<AccountStatementInfo> infos = accountStatementInfoSelfMapper.selectLastedStatementInfoByAccountManagementIdAndDate(
                    accountManageInfoId);

            if (!CollectionUtils.isEmpty(infos)) {
                resultList = new ArrayList<>();
                BeanCopier copierAccountStatementModelToDTO = BeanCopier.create(AccountStatementInfo.class, AccountStatementInfoDTO.class, false);
                for (AccountStatementInfo info : infos) {
                    AccountStatementInfoDTO dto = new AccountStatementInfoDTO();
                    copierAccountStatementModelToDTO.copy(info, dto, null);
                    //给争议金额和次数赋值
                    LocalDate statementDate = dto.getStatementDate();
                    String oriNum = dto.getOrganizationNumber();
//                    log.info("statementDate:{} oriNum:{}", statementDate, oriNum);
                    dto.setDisputeAmount(disputedTransactionSelfMapper.sumDisputeAmount(accountManageInfoId, statementDate, oriNum));
                    dto.setDisputeCount(disputedTransactionSelfMapper.countDisputeTransaction(accountManageInfoId, statementDate, oriNum));
//                    log.info("disputed amount:{} disputed count:{}", dto.getDisputeAmount(), dto.getDisputeCount());
                    resultList.add(dto);
                }
            }

        } catch (Exception e) {
            log.error("AccountStatementInfoServiceImpl findLastedStatementInfo error", e);
            /*throw new AnyTXNBusRuntimeException(
                    TransactionEnum.DATABASE_ERROR.getCode(), TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
        if (resultList == null || resultList.isEmpty()) {
            resultList = new ArrayList<>();
        }
        return resultList;
    }

    private String findMidByCardNumber(String cardNumber) {
        String orgNumber = OrgNumberUtils.getOrg();
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNumber, orgNumber);
        if (cardAuthorizationInfo == null) {
            log.error("账单信息查询，根据卡号查询卡片信息为空！cardNumber：{}", cardNumber);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
        }
        List<ParmCardCurrencyInfo> parmCardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(orgNumber, cardAuthorizationInfo.getProductNumber());
        ParmCardCurrencyInfo parmCardCurrencyInfo = parmCardCurrencyInfoList.get(0);
        ParmCardProductInfo parmCardProductInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(orgNumber, cardAuthorizationInfo.getProductNumber());
        String accountCustomerId = LiabilityEnum.CORPORATE.getCode().equals(parmCardProductInfo.getLiability()) ? cardAuthorizationInfo.getCorporateCustomerId() : cardAuthorizationInfo.getPrimaryCustomerId();
        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectByCusIdProNumAndCurr(accountCustomerId, parmCardProductInfo.getAccountProductNumber(), parmCardCurrencyInfo.getCurrencyCode());
        if (accountManagementInfo == null) {
            log.error("账单信息查询，根据卡号查询管理账户信息为空！cardNumber:{}, accountCustomerId:{}, accountProductNumber:{}, currency:{}", cardNumber, accountCustomerId, parmCardProductInfo.getAccountProductNumber(), parmCardCurrencyInfo.getCurrencyCode());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
        }
        return accountManagementInfo.getAccountManagementId();
    }

    @Override
    public PageResultDTO<CardStatementDTO> findCardStatementInfos(CardStatementDTO2 cardStatementBO) {
        String organizationNumber = cardStatementBO.getOrganizationNumber();
        String orgNumber = OrgNumberUtils.getOrg(organizationNumber);
        String corporateCustomerId = cardStatementBO.getCorporateCustomerId();
        String accountManagementId = cardStatementBO.getAccountManagementId();
        log.info("查询公司底下满足条件的入账信息：{}", corporateCustomerId);

        List<PostedTransaction> transactionsList = null;
        List<CardStatementDTO> resultList = new ArrayList<>();
        List<PostedTransaction> postedTransactions;
        List<CardStatementDTO2> cardStatementBOList = new ArrayList<>();

        try {
            List<CorporateTopDownReference> topDownReferences = corporateTopDownReferenceSelfMapper.selectByParentId(corporateCustomerId);
            List<String> customerIds = topDownReferences.stream().map(CorporateTopDownReference::getCorporateChildId)
                                                                 .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            customerIds.add(corporateCustomerId);
            for (String customerId : customerIds) {
                cardStatementBO.setCorporateCustomerId(customerId);
                cardStatementBOList.add(cardStatementBO);
            }
            List<PostedTransaction> postedTransactionList = BeanMapping.copyList(cardStatementBOList, PostedTransaction.class);

            transactionsList = postedTransactionSelfMapper.selectTransByCorCustomerId(postedTransactionList);

            if (CollectionUtils.isEmpty(transactionsList)) {
                // 公司卡结构中 虚拟账户的兼容
                AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.getByOrgNumAndMid(orgNumber, accountManagementId);
                if (accountManagementInfo != null) {
                    String productNumber = accountManagementInfo.getProductNumber();
                    ParmAcctProductMainInfo parmAcctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(orgNumber, productNumber);
                    if (parmAcctProductMainInfo != null) {
                        if (parmAcctProductMainInfo.getAttribute().equals("V") || parmAcctProductMainInfo.getAttribute().equals("T")) {
                            transactionsList = postedTransactionSelfMapper.selectTransByCustomerId(postedTransactionList);
                        }
                    }
                }
            }

            postedTransactions = new ArrayList<>(transactionsList);

        } catch (Exception e) {
            log.error("查询公司底下满足条件的入账信息error", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, e);
        }
        if (!CollectionUtils.isEmpty(postedTransactions)) {
            for (PostedTransaction postedTransaction : postedTransactions) {
                if (StringUtils.isBlank(postedTransaction.getCardNumber())) {
                    postedTransaction.setCardNumber("");
                }
            }
            Map<String, List<PostedTransaction>> transInfoGroup = postedTransactions.stream().collect(Collectors.groupingBy(u -> u.getCardNumber().trim() + "_" + u.getTransactionCurrencyCode().trim()));
            for (String key : transInfoGroup.keySet()) {
                String[] strs = key.split("_");
                String cardNumber = strs[0];
                String currencyCode = strs[1];
                CardStatementDTO cardStatementDTO = new CardStatementDTO();
                cardStatementDTO.setCardNumber(cardNumber);// 卡号
                cardStatementDTO.setCurrencyCode(currencyCode);// 币种
                CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNumber, "101");
                if (cardAuthorizationInfo == null) {
                    cardStatementDTO.setEmbossName("");
                } else {
                    cardStatementDTO.setEmbossName(cardAuthorizationInfo.getEmbossName());
                }
                // 刻印名
                BigDecimal debitAmount = transInfoGroup.get(key).stream().filter(f -> StringUtils.isNotEmpty(f.getDebitCreditIndcator()) && "D".equals(f.getDebitCreditIndcator())).map(PostedTransaction::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal creditAmount = transInfoGroup.get(key).stream().filter(f -> StringUtils.isNotEmpty(f.getDebitCreditIndcator()) && "C".equals(f.getDebitCreditIndcator())).map(PostedTransaction::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                cardStatementDTO.setTotalAmount(debitAmount.subtract(creditAmount));// 交易金额汇总

                BigDecimal debitPostingAmount = transInfoGroup.get(key).stream().filter(f -> StringUtils.isNotEmpty(f.getDebitCreditIndcator()) && "D".equals(f.getDebitCreditIndcator())).map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal creditPostingAmount = transInfoGroup.get(key).stream().filter(f -> StringUtils.isNotEmpty(f.getDebitCreditIndcator()) && "C".equals(f.getDebitCreditIndcator())).map(PostedTransaction::getPostingAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                cardStatementDTO.setPostingAmount(debitPostingAmount.subtract(creditPostingAmount));// 入账金额汇总
                resultList.add(cardStatementDTO);
            }
        }
        Integer page = cardStatementBO.getPage();
        Integer rows = cardStatementBO.getRows();
        List<CardStatementDTO> resultDtos = new ArrayList<>();
        int start = (page - 1) * rows;
        int end = Math.min(resultList.size(), (page * rows));
        for (int i = start; i < end; i++) {
            resultDtos.add(resultList.get(i));
        }
        return new PageResultDTO<>(page, rows, resultList.size(), resultList.size() / rows + ((resultList.size() % rows) > 0 ? 1 : 0),
                resultDtos.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public int modifyAccountStatementInfo(String accountStatementId, BigDecimal balance) {
        return 0;
    }

    /**
     * wangdaojian ******** 修改延迟还款日
     *
     * @param req
     */
    @Override
    public void modifyPaymentDueDate(ModifyPaymentDueDateDTO req) {
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(req.getCardNumber(), req.getOrganizationNumber());
        if (cardAuthorizationInfo == null) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.CE);
        }
        OrganizationInfoResDTO parameterConfig = organizationInfoService.findOrganizationInfo(cardAuthorizationInfo.getOrganizationNumber());
        if (parameterConfig == null) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.OE);
        }
//todo 获取管理账户信息表 lc
        CardProductInfoResDTO cardProductInfoResDto = cardProductInfoService.findByOrgAndProductNum(cardAuthorizationInfo.getOrganizationNumber(), cardAuthorizationInfo.getProductNumber());
        AccountManagementInfo account = null;
        if (cardProductInfoResDto != null) {
            account = accountManagementInfoSelfMapper.selectVaildByOrgProNumCurrAndCusId(cardProductInfoResDto.getOrganizationNumber(), cardProductInfoResDto.getAccountProductNumber(), req.getCurrencyType(), cardAuthorizationInfo.getPrimaryCustomerId());
            if (account == null) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.AE);
            }
        }
        if (account.getLastStatementDate() == null) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.BE);
        }
        AccountStatementInfo accountStatementInfo = accountStatementInfoMapper.selectByAccIdAndOrgAndCurrencyAndDate(
                account.getAccountManagementId(),
                cardAuthorizationInfo.getOrganizationNumber(),
                req.getCurrencyType(),
                account.getLastStatementDate());
        if (accountStatementInfo == null) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.LB_E);
        }
        //下一系统处理日
        LocalDate nextProcessingDay = parameterConfig.getNextProcessingDay();
        //
        LocalDate payDueDate = accountStatementInfo.getPaymentDueDate();
        if (nextProcessingDay.isAfter(payDueDate)) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_VALUE_FAULT, AccountingRepDetailEnum.RE_RE);
        }

        LocalDate next = calculateAnnualFee(nextProcessingDay, account.getCycleDay());
        if (req.getPaymentDueDate().isAfter(next)) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_VALUE_FAULT, AccountingRepDetailEnum.MO_RE);
        }

        //延迟还款日
        LocalDate paymentDueDateNew = req.getPaymentDueDate();
        LocalDate paymentDueDate = accountStatementInfo.getPaymentDueDate();
        //计算天数
        long days = paymentDueDate.until(paymentDueDateNew, ChronoUnit.DAYS);
        //违约金收取日
        LocalDate lateChargeDate = accountStatementInfo.getLateChargeDate();
        if (lateChargeDate.plusDays(days).isAfter(next)) {
            lateChargeDate = next;
        } else {
            lateChargeDate = lateChargeDate.plusDays(days);
        }

        LocalDate interestWaiveGraceDate = accountStatementInfo.getInterestWaiveGraceDate();
        if (interestWaiveGraceDate.plusDays(days).isAfter(next)) {
            interestWaiveGraceDate = next;
        } else {
            interestWaiveGraceDate = interestWaiveGraceDate.plusDays(days);
        }

        accountStatementInfo.setPaymentDueDate(req.getPaymentDueDate());
        accountStatementInfo.setLateChargeDate(lateChargeDate);
        accountStatementInfo.setInterestWaiveGraceDate(interestWaiveGraceDate);
        accountStatementInfoMapper.updateByPrimaryKeySelective(accountStatementInfo);
    }

    /**
     * 下次年费收取日期 （下次年费收取日期=下一个账单日）
     *
     * @param nextProcessingDay
     * @param cycleDate
     * @return 下次年费收取日期
     */
    private LocalDate calculateAnnualFee(LocalDate nextProcessingDay, short cycleDate) {
        int month = 0;
        int year = 0;
        if (nextProcessingDay.getDayOfMonth() > cycleDate) {
            month = nextProcessingDay.getMonthValue() + 1;
        } else {
            month = nextProcessingDay.getMonthValue();
        }
        year = nextProcessingDay.getYear();
        if (month > 13) {
            month = month - 12;
            year = year + 1;
        }
        LocalDate annualFee = LocalDate.of(year, month, cycleDate);
        return annualFee;
    }

    @Override
    public void registAccountStatementInfo(AccountStatementInfoDTO accountStatementInfoDTO) {
        try {
            AccountStatementInfo info = new AccountStatementInfo();
            BeanCopier copierAccountStatementDtoToModel = BeanCopier.create(AccountStatementInfoDTO.class, AccountStatementInfo.class, false);
            copierAccountStatementDtoToModel.copy(accountStatementInfoDTO, info, null);
            accountStatementInfoMapper.insertSelective(info);
        } catch (Exception e) {
            log.error("调用[{}]插入数据库表[{}]失败",
                    "insertSelective", "ACCOUNT_STATEMENT_INFO", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
    }

    @Override
    public void modifyAccountStatementInfo(AccountStatementInfoDTO accountStatementInfoDTO) {
        try {
            AccountStatementInfo info = new AccountStatementInfo();
            BeanCopier copierAccountStatementDtoToModel = BeanCopier.create(AccountStatementInfoDTO.class, AccountStatementInfo.class, false);
            copierAccountStatementDtoToModel.copy(accountStatementInfoDTO, info, null);
            accountStatementInfoMapper.updateByPrimaryKeySelective(info);
        } catch (Exception e) {
            log.error("调用[{}]更新数据库表[{}]失败",
                    "updateByPrimaryKeySelective", "ACCOUNT_STATEMENT_INFO", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
    }

    @Override
    public AccountStatementInfoDTO findStatementInfo(String statementId) {
        AccountStatementInfoDTO dto = null;
        try {
            AccountStatementInfo info = accountStatementInfoMapper.selectByPrimaryKey(statementId);
            if (null != info) {
                dto = new AccountStatementInfoDTO();
                BeanCopier copierAccountStatementModelToDTO = BeanCopier.create(AccountStatementInfo.class, AccountStatementInfoDTO.class, false);
                copierAccountStatementModelToDTO.copy(info, dto, null);
            }

        } catch (Exception e) {
            log.error("调用[{}]查询数据库表[{}]失败",
                    "selectByPrimaryKey", "ACCOUNT_STATEMENT_INFO", e);
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }

        return dto;
    }

    @Override
    public AccountStatementInfoDTO findStatementInfoByIdAndDate(String accountManageInfoId, LocalDate date) {
        AccountStatementInfoDTO dto = null;
        try {

            AccountStatementInfo info = accountStatementInfoSelfMapper.selectByAccountManagementIdAndDate(accountManageInfoId, date);
            if (null != info) {
                dto = new AccountStatementInfoDTO();
                BeanCopier copierAccountStatementModelToDTO = BeanCopier.create(AccountStatementInfo.class, AccountStatementInfoDTO.class, false);
                copierAccountStatementModelToDTO.copy(info, dto, null);
            }

        } catch (Exception e) {
            log.error("调用[{}]查询数据库表[{}]失败",
                    "selectByAccountManagementIdAndDate", "ACCOUNT_STATEMENT_INFO", e);
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }

        return dto;
    }

    @Override
    public int getAccountManagementCount(String partitionKey, List<Map<String, Object>> organizations) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }

        return accountStatementInfoSelfMapper.getAccountManagementCount(partitionKey0, partitionKey1, organizations);
    }

    @Override
    public List<String> queryAccountManagementIds(String partitionKey, List<Map<String, Object>> organizations, List<Integer> rowNumbers) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }

        return accountStatementInfoSelfMapper.queryAccountManagementIds(partitionKey0, partitionKey1, organizations, rowNumbers);
    }

    @Override
    public BigDecimal getCloseBalance(String accountManagementId, LocalDate lastStatementDate) {
        return accountStatementInfoSelfMapper.getCloseBalance(accountManagementId, lastStatementDate);
    }

    /**
     * 批量查询，根据管理账户id和账单日
     *
     * @param statementList
     * @return
     */
    @Override
    public List<AccountStatementInfoDTO> selectByManageIdAndDate(List<Map<String, Object>> statementList) {
        List<AccountStatementInfo> accountStatementInfos = accountStatementInfoSelfMapper.selectByManageIdAndDate(statementList);
        return BeanMapping.copyList(accountStatementInfos, AccountStatementInfoDTO.class);
    }
}
