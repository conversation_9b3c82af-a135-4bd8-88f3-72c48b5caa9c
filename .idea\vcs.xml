<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="VcsDirectoryMappings">
    <mapping directory="$PROJECT_DIR$/anytxn-account" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-accounting" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-authorization" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-business-core" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-card" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-common-manager" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-customer" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-gateway" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-installment" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-limit" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-mapping" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-monetary-processing" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-parameter" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-parent" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-rule" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-settlement" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-third-party-service" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/anytxn-transaction" vcs="Git" />
  </component>
</project>