package com.anytech.anytxn.accounting.batch.job.report.step.dayinstallfree.dto;

import com.anytech.anytxn.accounting.batch.job.report.step.dayinstallfree.model.InstallFreeDetail;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/9/16 16:55
 */
@Getter
@Setter
@ToString
public class InstallFeeReport {
    private List<InstallFeeSubReport> list;
    //总数
    private Integer total;

    public static InstallFeeReport resetReportData(List<InstallFreeDetail> data){
        InstallFeeReport report = new InstallFeeReport();
        report.setTotal(data.size());
        List<InstallFeeSubReport> subs = new ArrayList<>();
        report.setList(subs);

        Map<String, List<InstallFreeDetail>> group = data.stream().collect(Collectors.groupingBy(x -> x.getProductNum() + x.getCurrCode()));
        int i = 0;
        for (Map.Entry<String, List<InstallFreeDetail>> entry : group.entrySet()) {
            List<InstallFreeDetail> value = entry.getValue();
            InstallFreeDetail temple = value.get(0);

            InstallFeeSubReport subReport = new InstallFeeSubReport();
            subs.add(subReport);

            subReport.setCurrencyCode(temple.getCurrCode());
            subReport.setEnd(++i == group.size());
            subReport.setProductId(temple.getProductNum());
            subReport.setSubCount(value.size());
            subReport.setAmountTotal(value.stream().map(InstallFreeDetail::getFreeAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

            List<InstallFeeDetailReport> detailReports = new ArrayList<>();
            subReport.setDetailList(detailReports);

            for (InstallFreeDetail detail : value) {
                InstallFeeDetailReport detailReport = new InstallFeeDetailReport();
                detailReports.add(detailReport);

                detailReport.setCurrencyCode(detail.getCurrCode());
                detailReport.setProductDesc(detail.getDescription());
                detailReport.setProductId(detail.getProductNum());
                detailReport.setIsPostFeeAmount(detail.getFreeAmount());
                detailReport.setNoPostFeeAmount(detail.getNoFreeAmount());
            }
        }

        return report;
    }
}
