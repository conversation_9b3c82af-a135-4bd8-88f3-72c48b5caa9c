package com.anytech.anytxn.accounting.base.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 模块标识
 * @date 2020-02-16 14:06
 **/
public enum TxnIndEnum {
    /**
     * 0：原始交易
     * 1：余额变动
     * 2：余额变动-衍生
     * 3：ABS分录
     * 4：分期整笔
     */
    CORE("0", "原始交易"),
    CHARGE_OFF("1", "余额变动"),
    TAX("2", "余额变动-衍生"),
    YEAR_END("3", "ABS分录"),
    INST("4", "分期整笔"),
    INST_DI("5", "分期递延"),
    ;
    private String code;
    private String desc;


    TxnIndEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String getValue(String code) {
        if (code == null) {
            return null;
        }
        TxnIndEnum[] var2 = values();
        int var3 = var2.length;
        for (int var4 = 0; var4 < var3; ++var4) {
            TxnIndEnum moduleFlagEnum = var2[var4];
            if (Objects.equals(code, moduleFlagEnum.getCode())) {
                return moduleFlagEnum.getDesc();
            }
        }
        return null;
    }
}
