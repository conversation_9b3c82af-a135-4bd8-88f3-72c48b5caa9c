package com.anytech.anytxn.account.job.exchange.step;

import com.anytech.anytxn.business.base.account.domain.bo.ExchangePaymentBO;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.SettlementLogDTO;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.transaction.base.service.ISettlementLogService;
import org.assertj.core.util.Lists;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-09 14:55
 **/
public class ExchangePaymentWriter implements ItemWriter<ExchangePaymentBO> {

    @Autowired
    private ISettlementLogService settlementLogService;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    public void write(List<? extends ExchangePaymentBO> items) throws Exception {

        List<SettlementLogDTO> settlementLogDtos = Lists.newArrayList();

        items.forEach(x-> {
            settlementLogDtos.add(buildSettlementLog(x.getRecordedBOIn()));
            settlementLogDtos.add(buildSettlementLog(x.getRecordedBOOut()));
        });

        settlementLogService.addBatch(settlementLogDtos);
    }

    private SettlementLogDTO buildSettlementLog(RecordedBO recorded){
        SettlementLogDTO settlementLogDTO = new SettlementLogDTO();
        settlementLogDTO.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        settlementLogDTO.setTxnAccountManageId(recorded.getTxnAccountManageId());
        settlementLogDTO.setTxnAuthMatchIndicator(recorded.getTxnAuthorizationMatchIndicator());
        // 入账金额
        settlementLogDTO.setTxnBillingAmount(recorded.getTxnBillingAmount());
        // 入账币种
        settlementLogDTO.setTxnBillingCurrency(recorded.getTxnBillingCurrency());
        // 入账日期
        settlementLogDTO.setTxnBillingDate(recorded.getTxnBillingDate());
        settlementLogDTO.setTxnExchangeRate(recorded.getTxnExchangeRate());
        settlementLogDTO.setTxnPostMethod(recorded.getTxnPostMethod());
        settlementLogDTO.setTxnReleaseAuthAmount(recorded.getTxnReleaseAuthorizationAmount());
        settlementLogDTO.setTxnReverseFeeIndicator(recorded.getTxnReverseFeeIndicator());
        // 清算金额
        settlementLogDTO.setTxnTransactionAmount(recorded.getTxnTransactionAmount());
        // 清算币种
        settlementLogDTO.setTxnSettlementCurrency(recorded.getTxnSettlementCurrency());
        // 交易金额
        settlementLogDTO.setTxnTransactionAmount(recorded.getTxnTransactionAmount());
        // 交易码
        settlementLogDTO.setTxnTransactionCode(recorded.getTxnTransactionCode());
        // 交易币种
        settlementLogDTO.setTxnTransactionCurrency(recorded.getTxnTransactionCurrency());
        // 交易日期
        settlementLogDTO.setTxnTransactionDate(recorded.getTxnTransactionDate());
        settlementLogDTO.setTxnTransactionDescription(recorded.getTxnTransactionDescription());
        settlementLogDTO.setTxnTransactionSource(recorded.getTxnTransactionSource());

        settlementLogDTO.setCustomerId(recorded.getCustomerId());
        settlementLogDTO.setPartitionKey(PartitionKeyUtils.IntPartitionKey(recorded.getCustomerId()));
        settlementLogDTO.setCreateTime(LocalDateTime.now());
        settlementLogDTO.setUpdateTime(LocalDateTime.now());
        settlementLogDTO.setVersionNumber(1L);
        return settlementLogDTO;
    }

    @Override
    public void write(Chunk<? extends ExchangePaymentBO> chunk) throws Exception {
        write(chunk.getItems());
    }
}
