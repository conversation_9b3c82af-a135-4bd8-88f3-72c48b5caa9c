<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.anytech</groupId>
        <artifactId>anytxn-account</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>anytxn-account-server</artifactId>

    <dependencies>
        <!--        内部依赖start-->
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-account-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-sharding</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-loadbalancer</artifactId>
        </dependency>
        <!--        内部依赖end-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <!-- 监控 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>

        <!--日志-->
        <dependency>
            <groupId>com.plumelog</groupId>
            <artifactId>plumelog-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.plumelog</groupId>
            <artifactId>plumelog-trace</artifactId>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <!--打包插件-->
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <appendAssemblyId>false</appendAssemblyId>
                            <skipAssembly>false</skipAssembly>
                            <finalName>${project.artifactId}-${project.version}</finalName>
                            <descriptors>
                                <descriptor>src/assembly/package.xml</descriptor> <!-- Assembly 描述符文件 -->
                            </descriptors>
                        </configuration>
                    </execution>
                    <execution>
                        <id>make-assembly-docker</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <appendAssemblyId>false</appendAssemblyId>
                            <skipAssembly>false</skipAssembly>
                            <finalName>${project.artifactId}</finalName>
                            <descriptors>
                                <descriptor>src/assembly/package-docker.xml</descriptor> <!-- Assembly 描述符文件 -->
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- 镜像仓库 -->
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <!--配置远程docker守护进程url-->
                    <!--                    <dockerHost></dockerHost>-->
                    <!--认证配置,用于私有registry认证-->
                    <authConfig>
                        <username>${harbor.auth.user}</username>
                        <password>${harbor.auth.passd}</password>
                    </authConfig>
                    <!--镜像相关配置,支持多镜像-->
                    <images>
                        <!-- 单个镜像配置 -->
                        <image>
                            <!--镜像名(含版本号)-->
                            <name>k8s.jrx.com/anytxn/${project.artifactId}:${project.version}</name>
                            <!--registry地址,用于推送,拉取镜像-->
                            <registry>${harbor.registry}</registry>
                            <!--镜像build相关配置-->
                            <build>
                                <!--使用dockerFile文件-->
                                <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                            </build>
                        </image>
                    </images>
                </configuration>
            </plugin>
            <!-- 代码不发布到maven仓库 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
