package com.anytech.anytxn.account.job.egprepayschedule.config;

import com.anytech.anytxn.account.job.egprepayschedule.steps.UpdateEgpRepayScheduleInfoTasklet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * EGP还款计划处理Job
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
@Configuration
@Slf4j
public class EgpRepayScheduleConfig {
    @Autowired
    private JobBuilderFactory jobBuilderFactory;
    @Autowired
    private StepBuilderFactory stepBuilderFactory;
    @Autowired
    private UpdateEgpRepayScheduleInfoTasklet updateEgpRepayScheduleInfoTasklet;

    @Bean
    public Job egpRepayScheduleJob(@Qualifier("updateEgpRepayInfoStep")Step updateEgpRepayInfoStep){
        return jobBuilderFactory.get("egpRepayScheduleJob")
                .start(updateEgpRepayInfoStep)
                .build();
    }
    @Bean
    public Step updateEgpRepayInfoStep(){
        return stepBuilderFactory.get("updateEgpRepayInfoStep")
                .tasklet(updateEgpRepayScheduleInfoTasklet)
                .build();
    }
}
