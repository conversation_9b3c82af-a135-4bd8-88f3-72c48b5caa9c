package com.anytech.anytxn.account.base.constants;

/**
 * 自动购汇标志
 *
 * <AUTHOR>
 * @date 2018-9-22
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 *
 */
public enum AutoExIndicator {


    /**
     * 关闭自动购汇
     */
    CLOSE("0", "关闭自动购汇"),

    OPEN("1", "开通自动购汇");


    private String code;
    private String reason;


    AutoExIndicator(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }
    }
