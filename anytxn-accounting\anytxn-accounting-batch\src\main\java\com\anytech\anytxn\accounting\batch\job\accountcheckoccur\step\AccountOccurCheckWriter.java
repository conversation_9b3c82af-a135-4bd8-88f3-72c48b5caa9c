package com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkOccurDTO;
import com.anytech.anytxn.accounting.mapper.AccountantGlbalchkOccurMapper;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlbalchkOccur;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/13
 */
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class AccountOccurCheckWriter implements ItemWriter<List<TAmsGlbalchkOccurDTO>> {

    @Autowired
    private AccountantGlbalchkOccurMapper amsGlbalchkOccurMapper;

    @Override
    public void write(Chunk<? extends List<TAmsGlbalchkOccurDTO>> chunk) throws Exception {
        List<? extends List<TAmsGlbalchkOccurDTO>> items = chunk.getItems();
        items.forEach(x-> {
            if (CollectionUtils.isNotEmpty(x)) {
                BeanMapping.copyList(x, AccountantGlbalchkOccur.class).forEach(amsGlbalchkOccurMapper::insert);
            }
        });
    }

}

