package com.anytech.anytxn.account.job.stamptaxsumlimit.shard.config;

import com.anytech.anytxn.account.utils.FileConstant;
import com.anytech.anytxn.account.job.stamptaxsumlimit.shard.step.ShardTasklet;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;

/**
 * 单分片印花税数据汇总入库及写出到文件
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
@Configuration
@Slf4j
public class ShardConfig {
    @Autowired
    private JobBuilderFactory jobBuilderFactory;
    @Autowired
    private StepBuilderFactory stepBuilderFactory;
    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Value("${data-id:-1}")
    private String dataId;

    @Resource(name = "stampTaxSumLimitFilePathConfig")
    private AnytxnFilePathConfig stampTaxSumLimitFilePathConfigInput;

    private String getDusId(){
        if (dataId.contains(".")) {
            dataId = dataId.split("\\.")[0];
        }
        if (dataId.contains("-")){
            dataId = dataId.split("-")[1];
        }
        return dataId;
    }

    private String joinStampTaxSumLimitFilePath(){
        String orgNum = OrgNumberUtils.getOrg();
        ParmOrganizationInfo org = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(orgNum);
        // 拼接输出文件目录：前缀/分片号/机构号/文件名
        log.info("joinStampTaxSumLimitFilePath : {}",stampTaxSumLimitFilePathConfigInput.getShardingPath(Integer.parseInt(getDusId())) + joinStampTaxSumLimitFileName(org));
        return stampTaxSumLimitFilePathConfigInput.getShardingPath() + joinStampTaxSumLimitFileName(org);
    }

    private String joinStampTaxSumLimitFileName(ParmOrganizationInfo org){
        // 文件名称由【文件前缀_机构当前日期_分片号】组成
        String today = FileConstant.YYYYMMDD.format(org.getToday());
        return FileConstant.SHARD_FILE_PREFIX + "_" + today + "_" + getDusId() + FileConstant.SHARD_FILE_SUFFIX;
    }

    @Bean
    public Job stampTaxSumLimitJob(@Qualifier("shardStep") Step shardStep) {
        return jobBuilderFactory.get("stampTaxSumLimitJob")
                .start(shardStep)
                .build();
    }

    @Bean
    @StepScope
    public ShardTasklet shardTasklet(){
        return new ShardTasklet(joinStampTaxSumLimitFilePath());
    }

    @Bean
    public Step shardStep(
            @Qualifier("shardTasklet") ShardTasklet shardTasklet) {
        return stepBuilderFactory
                .get("shardStep")
                .tasklet(shardTasklet)
                .build();
    }
}
