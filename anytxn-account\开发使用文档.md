
# 开发使用手册

## 安装编译

> maven命令进行打包，mvn install

## 使用说明

> base模块打包，使用以下配置引入依赖:

```
    <dependency>                                      
        <groupId>com.anytech.anytxn</groupId>
        <artifactId>anytxn-accounting-base</artifactId>
        <version>3.x.x-SNAPSHOT</version>             
    </dependency>                                     
```

> sdk模块，使用以下配置引入依赖:

```
    <dependency>                                      
        <groupId>com.anytech.anytxn</groupId>
        <artifactId>anytxn-accounting-sdk</artifactId>
        <version>3.x.x-SNAPSHOT</version>             
    </dependency>                                    
```

## 工程结构说明
> 工程目录结构，包结构说明
- anytxn-accounting-base(anytxn账户服务接口模块)
    + 包结构说明
    ```text
      |-java
         |-com.anytech.anytxn.account      
              |-dto        接口请求，响应实体
              |-service           服务接口
    ```
               
- anytxn-accounting-batch(anytxn账户核心批量作业模块)
    + 包结构说明
    ```text
      |-java
         |-com.anytech.anytxn.account  
              |-config        系统配置类包，系统相关的java配置类统一
              |-job           作业任务包
              |-service          参数请求包
              |-AnytxnAccountingBatchApplication.java 服务启动类
      |-resources   静态资源包
    ```
                
- anytxn-accounting-sdk(anytxn账户服务实现模块)
    + 包结构说明
    ```text
      |-java
         |-com.anytech.anytxn.account
              |-config       系统配置类包，系统相关的java配置类统一
              |-controller          controller层
              |-service.impl          服务实现层
              |-EnableAccountingApi.java          服务启动容器加载bean的配置类，包括controller层
              |-EnableAccountingService.java          服务启动容器加载bean的配置类
              
    ```
    
- anytxn-accounting-server(anytxn账户web模块)
    + 包接口说明
    ```
      |-java                         
          |-com.anytech.anytxn.account.server    
              |-config    bean配置类
              |-AnyTxnAccountingServerApplication     springboot启动类     
    ``` 
                         

## 交易模块在spring，依赖数据库环境的配置使用
1. 在工程启动类或会被自动加载的配置类中添加@EnableAccountingService注解
    ```java
    @SpringBootApplication
    @EnableAccountingService
    public class DemoApplication {
    
        public static void main(String[] args){
            SpringApplication.run(DemoApplication.class);
        }
    }
    
    // 或者
    
    @Configuration
    @EnableAccountingService
    public class DemoConfiguration {
    }
    
    ```
2.  定义配置类并引入公共数据源配置(序列号生成使用)和业务数据源
    ```java
    @Configuration
    @Import({CommonDbConfiguration.class, BusinessDbConfiguration.class})
    public class ParamTestConfiguration {
    }
    
    ```
## 账户模块dto依赖(不依赖业务逻辑)使用
1. 仅引入anytxn-accounting-base模块pom即可

## 工程接口说明参考readme.md文件