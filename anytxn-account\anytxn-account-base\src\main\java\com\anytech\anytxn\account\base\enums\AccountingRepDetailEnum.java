package com.anytech.anytxn.account.base.enums;

import com.anytech.anytxn.common.core.constants.MessageHandle;

/**
 * <AUTHOR>
 * @date 2021/3/2
 */
public enum AccountingRepDetailEnum implements MessageHandle {
    NULL("%s", "%s"),
    AM_NE("管理账户号错误", "Management account number error"),
    FFE("文件格式错误", "File format error"),
    CF_DE("创建文件目录失败", "Failed to create file directory"),
    DFF("删除文件失败", "Failed to delete file"),
    CFF("创建文件失败", "Failed to create file"),
    DC_CN("此证件号有重复客户", "There are duplicate customers in this certificate number"),
    CB_NF("未查到客户基础表数据", "Customer basic table data not found"),
    UT_AB("更新交易账户账单日失败，请检查原数据正确性", "Failed to update the transaction account bill date, please check the correctness of the original data"),
    OAD_IF("交易账户原账单日或免息结息日为空，请检查原数据正确性", "The original account date or interest free settlement date of the transaction account is empty, please check the correctness of the original data"),
    MA_E("管理账户不存在", "The management account does not exist"),
    Q_AM("查询[%s]管理账户失败", "Failed to query the management account of [%s]"),
    I_AM("新增管理账户[%s]失败", "Failed to add management account (%s)"),
    GMP("获取机构参数失败", "Failed to get mechanism parameters"),
    OPD_ND("机构参数表上一处理日期,下一处理日期或账单账户还款日错误", "Error in previous processing date, next processing date or repayment date of bill account in institution parameter table"),
    RA("找不到人民币账户", "RMB account not found"),
    MAI_E("管理账户信息表in_collection_indicato为空", "Management account information table_collection_Indicator is empty"),
    AP_E("管理账户配置信息表为空", "Account optinal information table_collection_Indicator is empty"),
    FLC("账户的五级分类不得高于个人贷款的五级分类", "The five level classification of accounts shall not be higher than that of personal loans"),
    Q_OPC("根据机构号、产品编号、币种查询账户产品参数表失败", "Failed to query account product parameter table according to organization number, product number and currency"),
    U_AB_AP("修改前账产品中账户封锁码参数表ID为空", "The parameter table ID of account block code in the account product before modification is empty"),
    Q_OBB("根据机构号、账户封锁码参数表ID、修改前的账户封锁码查询账户层封锁码参数表失败", "Failed to query account layer blocking code parameter table according to organization number, account blocking code parameter table ID and account blocking code before modification"),
    IBC("封锁码非法", "Illegal block code"),
    U_AB_AP2("修改后账产品中账户封锁码参数表ID为空", "The parameter table ID of account blocking code in the modified account product is empty"),
    Q_OBB2("根据机构号、账户封锁码参数表ID、修改后的账户封锁码查询账户层封锁码参数表失败", "Failed to query account layer blocking code parameter table according to organization number, account blocking code parameter table ID and modified account blocking code"),
    PNB_O("新封锁码优先级低于原封锁码", "The priority of the new block code is lower than that of the original one"),
    BD_BJ("账单日须在1-28日之间", "The billing date must be between January and 28"),
    ST_BJ("账单信息不存在", "Billing information does not exist"),
    MB_PB("没有出过账单 不能修改账单日", "You can't modify the bill date without posting the bill"),
    DAB_BD("因账户封锁码原因 不能修改账单日", "Due to account block code, the bill date cannot be modified"),
    CU_AC("当前账户已逾期 不能修改账单日", "Current account is overdue and cannot modify bill date"),
    ON_E("机构号不能为空", "Institution No. cannot be empty"),
    MI_E("管理账户id不能为空","Management account ID cannot be empty"),
    BI_DA("有分期订单尚未结清 不能修改账单日", "The bill date cannot be modified if the installment order has not been settled"),
    CM("当前延滞状态下,当期最小还款必须等于0", "Under the current delay state, the current minimum repayment must be equal to 0"),
    CX("当前延滞状态下,X天金额必须等于0", "In the current delay state, the X-day amount must be equal to 0"),
    C30("当前延滞状态下,30天金额必须等于0", "The 30 day amount must be equal to 0 in the current delay state"),
    C60("当前延滞状态下,60天金额金额必须等于0","The 60 day amount must be equal to 0 in the current delay state"),
    C90("当前延滞状态下,60天金额金额必须等于0","The 90 day amount must be equal to 0 in the current delay state"),
    C120("当前延滞状态下,120天金额金额必须等于0","The 120 day amount must be equal to 0 in the current delay state"),
    C150("当前延滞状态下,150天金额金额必须等于0","The 150 day amount must be equal to 0 in the current delay state"),
    C180("当前延滞状态下,180天金额金额必须等于0","The 180 day amount must be equal to 0 in the current delay state"),
    C210("当前延滞状态下,210天金额金额必须等于0","The 210 day amount must be equal to 0 in the current delay state"),
    C240("当前延滞状态下,240天金额金额必须等于0","The 240 day amount must be equal to 0 in the current delay state"),
    C270("当前延滞状态下,270天金额金额必须等于0","The 270 day amount must be equal to 0 in the current delay state"),
    C300("当前延滞状态下,300天金额金额必须等于0","The 300 day amount must be equal to 0 in the current delay state"),
    C330("当前延滞状态下,330天金额金额必须等于0","The 330 day amount must be equal to 0 in the current delay state"),
    C360("当前延滞状态下,360天金额金额必须等于0","The 360 day amount must be equal to 0 in the current delay state"),
    C390("当前延滞状态下,390天金额金额必须等于0","The 390 day amount must be equal to 0 in the current delay state"),
    CM2("当前延滞状态下,当期最小还款不可小于等于0", "Under the current delay state, the current minimum repayment cannot be less than or equal to 0"),
    CML0("当前延滞状态下,当期最小还款不可小于0", "Under the current delay state, the current minimum repayment cannot be less than 0"),
    CXL0("当前延滞状态下,X天金额不可小于0", "In the current delay state, the amount of X days cannot be less than 0"),
    C30L0("当前延滞状态下,30天金额不可小于0", "Under the current delay state, the 30 day amount cannot be less than 0"),
    C60L0("当前延滞状态下,60天金额不可小于0", "Under the current delay state, the 60 day amount cannot be less than 0"),
    C90L0("当前延滞状态下,90天金额不可小于0", "Under the current delay state, the 90 day amount cannot be less than 0"),
    C120L0("当前延滞状态下,120天金额不可小于0", "Under the current delay state, the 120 day amount cannot be less than 0"),
    C150L0("当前延滞状态下,150天金额不可小于0", "Under the current delay state, the 150 day amount cannot be less than 0"),
    C180L0("当前延滞状态下,180天金额不可小于0", "Under the current delay state, the 180 day amount cannot be less than 0"),
    C210L0("当前延滞状态下,210天金额不可小于0", "Under the current delay state, the 210 day amount cannot be less than 0"),
    C240L0("当前延滞状态下,240天金额不可小于0", "Under the current delay state, the 240 day amount cannot be less than 0"),
    C270L0("当前延滞状态下,270天金额不可小于0", "Under the current delay state, the 270 day amount cannot be less than 0"),
    C300L0("当前延滞状态下,300天金额不可小于0", "Under the current delay state, the 300 day amount cannot be less than 0"),
    C330L0("当前延滞状态下,330天金额不可小于0", "Under the current delay state, the 330 day amount cannot be less than 0"),
    C360L0("当前延滞状态下,360天金额不可小于0", "Under the current delay state, the 360 day amount cannot be less than 0"),
    CX_LE0("当前延滞状态下,X天金额不可小于等于0", "In the current delay state, the amount of X days cannot be less than or equal to 0"),
    C30_LE0("当前延滞状态下,30天金额不可小于等于0", "In the current delay state, the 30 day amount cannot be less than or equal to 0"),
    C60_LE0("当前延滞状态下,60天金额不可小于等于0", "In the current delay state, the 60 day amount cannot be less than or equal to 0"),
    C90_LE0("当前延滞状态下,90天金额不可小于等于0", "In the current delay state, the 90 day amount cannot be less than or equal to 0"),
    C120_LE0("当前延滞状态下,120天金额不可小于等于0", "In the current delay state, the 120 day amount cannot be less than or equal to 0"),
    C150_LE0("当前延滞状态下,150天金额不可小于等于0", "In the current delay state, the 150 day amount cannot be less than or equal to 0"),
    C180_LE0("当前延滞状态下,180天金额不可小于等于0", "In the current delay state, the 180 day amount cannot be less than or equal to 0"),
    C210_LE0("当前延滞状态下,210天金额不可小于等于0", "In the current delay state, the 210 day amount cannot be less than or equal to 0"),
    C240_LE0("当前延滞状态下,240天金额不可小于等于0", "In the current delay state, the 240 day amount cannot be less than or equal to 0"),
    C270_LE0("当前延滞状态下,270天金额不可小于等于0", "In the current delay state, the 270 day amount cannot be less than or equal to 0"),
    C300_LE0("当前延滞状态下,300天金额不可小于等于0", "In the current delay state, the 300 day amount cannot be less than or equal to 0"),
    C330_LE0("当前延滞状态下,330天金额不可小于等于0", "In the current delay state, the 330 day amount cannot be less than or equal to 0"),
    C360_LE0("当前延滞状态下,360天金额不可小于等于0", "In the current delay state, the 360 day amount cannot be less than or equal to 0"),
    C390_LE0("当前延滞状态下,390天金额不可小于等于0", "In the current delay state, the 390 day amount cannot be less than or equal to 0"),
    RX_L0("当前延滞状态下,当期最小还款不能小于0", "Under the current delay, the minimum repayment of the current period cannot be less than 0"),
    ST_E("统计账户信息不存在", "Statistical account information does not exist"),
    AF_AD("调整后最小还款额大于总欠款", "After adjustment, the minimum payment is greater than the total debt"),
    NO_ACC("根据账户号未查询到数据", "No data is found according to the account number"),
    SE_ATT("所选卡片不是附卡", "The selected card is not attached"),
    EN_OP("请输入临时自扣操作日期", "Please enter the operation date of temporary self deduction"),
    DE_LA("最后还款日开始，不允许进行补自扣处理", "It is not allowed to make up and deduct from the last repayment date"),
    HI_AL("不允许输入历史日期", "Historical date not allowed"),
    OR_AL("临时自扣日期不允许在机构的下一处理日之前", "Temporary withholding dates are not allowed before the institution's next processing date"),
    ST_AL("临时自扣日期不允许在最近一个账单的还款日之后", "Temporary deduction dates are not allowed after the last bill payment date"),
    AG_SE("已存在约定自扣日期", "Agreed self deduction date already exists"),
    EN_DE("请输入临时自扣方式", "Please enter temporary self deduction method"),
    IN_TE("请输入临时自扣本行借记账户号", "Please input the temporary debit account number of the bank"),
    IN_WI("请输入临时自扣本行银行号", "Please input the bank number of temporary self withholding bank"),
    IN_DE("请输入临时自扣他行借记账户号", "Please input temporary debit account number of other bank"),
    IN_BA("请输入临时自扣他行银行号", "Please input the bank number of other bank for temporary self deduction"),
    CE("卡片不存在", "The card does not exist"),
    OE("机构参数不存在", "The mechanism parameter does not exist"),
    AE("未获取到账户", "Account not obtained"),
    BE("无账单数据", "No billing data"),
    LB_E("未获取到最近一期账单信息", "The latest billing information was not obtained"),
    RE_RE("已到本期还款日，不允许修改", "It has reached the repayment date of the current period, and no modification is allowed"),
    MO_RE("修改后还款日超过下一账单日", "The modified repayment date exceeds the next account date"),
    Q_MA("查询管理账户:[%s]交易类型:[%s]统计账户失败", "Query management account: [%s] transaction type: [%s] statistics account failed"),
    Q_ST("查询[%s]除去溢缴款统计账户失败", "Failed to query the statistics account of the excess payment removed by [%s]"),
    Q_ST2("查询[%s]溢缴款统计账户失败", "Failed to query the statistical account of excess payment in [%s]"),
    A_SU("新增汇总统计账户[%s]失败", "Failed to add summary statistics account (%s)"),
    AU_RE("根据主键查询自动还款历史明细 主键不能为空", "Query the automatic repayment history details according to the primary key. The primary key cannot be empty"),
    Q_AU_AU("根据主键查询自动还款历史明细失败", "Failed to query automatic repayment history details based on primary key"),
    MA_DE("管理账号或扣款金融不能为空", "Management account number or deduction finance cannot be blank"),
    ME("管理账号数据不存在", "Management account data does not exist"),
    CS("该卡号没有签约", "The card number is not signed"),
    PR_AG("根据主键查询约定扣款流水明细 主键不能为空", "According to the primary key query agreement, the primary key of deduction flow details cannot be empty"),
    DE_DE("根据主键查询约定扣款流水明细失败", "Failed to deduct the details of the daily payment according to the primary key query agreement"),
    CU_AU("根据客户号与机构号查询客户授权信息数据不存在", "Customer authorization information query based on customer number and organization number does not exist"),
    CR_AU("根据卡号与机构号查询卡片授权信息数据不存在", "Card authorization information query based on card number and organization number does not exist"),
    OR_E("机构号不存在", "The organization number does not exist"),
    US("更新成功", "Update succeeded"),
    WE_OF("微信公众号还款记录产生成功", "WeChat official account repayment record success"),
    AUTO_OF("约定扣款记录产生成功", "Appointment debit record generation success"),
    AUTO_UPDATE_OF("约定扣款记录更新成功", "Appointment debit record updated success"),
    AUTO_TEMP_OF("临时扣款记录产生成功", "Temporary debit record generation success"),
    AUTO_TEMP_UPDATE_OF("临时扣款记录更新成功", "Temporary debit record updated success"),
    AUTO_TEMP("约定扣款签约成功", "Appointment deduction contract success"),
    OUT_FILE("输出文件格式化条件错误","Output file format condition error"),
    IN_FILE("输入文件格式化条件错误","Input file format condition error"),
    ILFEE("ILFEE文件不存在或者无内容", "ILFEE file does not exist or has no content"),
    BLK_SUCCESS("封锁成功", "Block success"),
    UN_BLK_SUCCESS("解锁成功", "UnBlock succes"),
    LOYALY_UPDATE_E("更新账户权益信息表失败", "Failed to update the account loyalty table information"),
    LOYALY_UPDATE_PE("更新账户权益信息失败,管理账户ID不存在", "Failed to update account loyalty information, AccountManagement  ID Not Exist."),
    LOYALY_UPDATE_PE2("更新账户权益信息失败,积分权益ID不存在", "Failed to update account entitlement information, Loyalty project ID does not exist.."),
    LOYALY_NOT_EXIST("不存在该账户权益信息", "No information on loyalty in this account exists."),
    LOYALY_SYNC_E("权益信息修改失败，同步Loyalty处理失败","Loyalty Information Modification Failed, Synchronized Loyalty Processing Failed" ),
    LOYALY_SYNC_ACCT_NOT_E("同步Loyalty处理失败,管理账户不存在","Synchronized Loyalty Processing Failed, Management Account Doesn't Exist" ),
    LOYALY_SYNC_CARD_NOT_E("同步Loyalty处理失败,卡授权信息不存在","Synchronized Loyalty Processing Failed,Card Authorization Info Doesn't Exist" ),
    LOYALY_SYNC_TYPE_NOT_E("权益信息修改失败，无匹配积分类型","Loyalty information modification failed, no matching Loyalty type" ),
    LOYALY_EFFECT_NOT_E("生效权益信息不存在","Effective interest information does not exist" ),
    LOYALY_SELECT_E("查询loyalty生效权益信息失败","Failed to query loyalty effective interest information" );

    private String CnMsg;
    private String EnMsg;

    AccountingRepDetailEnum(String cnMsg, String enMsg) {
        CnMsg = cnMsg;
        EnMsg = enMsg;
    }

    @Override
    public String getCnMsg() {
        return CnMsg;
    }

    @Override
    public String getEnMsg() {
        return EnMsg;
    }
}
