package com.anytech.anytxn.account;

import com.anytech.anytxn.file.EnableFilePathConfig;
import jrx.anyscheduler.batch.job.single.EnableSingleBatchJobApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.transaction.annotation.EnableTransactionManagement;
/**
 *
 * <AUTHOR>
 * @date 2019-09-02
 **/
@EnableSingleBatchJobApplication
@EnableAccountingService
@EnableTransactionManagement
//@EnableFeignClients(basePackages = {"com.anytech.anytxn.transaction.feign","com.anytech.anytxn.accounting.feign"})
//@Import({BusinessDbConfiguration.class, CommonDbConfiguration.class})
@EnableFilePathConfig
public class AnytxnAccountingBatchApplication {

    public static void main(String[] args) {
//        args = new String[]{"--t.job=repaymentFileJob", "--debug"};
        new SpringApplicationBuilder(AnytxnAccountingBatchApplication.class)
                .web(WebApplicationType.NONE)
                .run(args);
    }


}

