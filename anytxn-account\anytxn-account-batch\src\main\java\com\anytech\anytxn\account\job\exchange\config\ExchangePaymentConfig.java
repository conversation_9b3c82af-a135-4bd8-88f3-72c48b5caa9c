package com.anytech.anytxn.account.job.exchange.config;

import com.anytech.anytxn.account.job.exchange.step.ExchangePaymentProcessor;
import com.anytech.anytxn.account.job.exchange.step.ExchangePaymentReader;
import com.anytech.anytxn.account.job.exchange.step.ExchangePaymentWriter;
import com.anytech.anytxn.business.base.account.domain.bo.ExchangePaymentBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.ExPurchaseInfoDTO;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * 购汇批量配置
 *
 * <AUTHOR>
 * @date 2018-11-09 14:56
 **/
@Configuration
public class ExchangePaymentConfig {
    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;

    @Value("${anytxn.batch.stmtProcessJob.stmtMasterStep.grid-size:8}")
    private Integer gridSize;

    @Value("${anytxn.batch.stmtProcessJob.stmtStep.chunk-limit:1000}")
    private Integer chunkLimit;

    @Value("${anytxn.batch.stmtProcessJob.stmtStep.page-size:1000}")
    private Integer pageSize;

    /**
     * 购汇批量 Job配置
     * @param masterExchangeStep
     * @return
     */
    @Bean
    public Job exchangePaymentJob(@Qualifier("exchangePaymentStep") Step exchangePaymentStep) {
        return jobs.get("exchangePaymentJob")
                .start(exchangePaymentStep)
                .build();
    }

//    /**
//     * 购汇批量 masterStep
//     * @param exchangePaymentStep
//     * @param exchangeCurrencyPartitioner
//     * @return
//     */
//    @Bean
//    public Step masterExchangeStep(@Qualifier("exchangePaymentStep") Step exchangePaymentStep,
//                                   @Qualifier("exchangePaymentStep") ExchangeCurrencyPartitioner exchangeCurrencyPartitioner) {
//        return steps.get("masterExchangeStep")
//                .partitioner(exchangePaymentStep.getName(), exchangeCurrencyPartitioner)
//                .step(exchangePaymentStep)
//                .gridSize(gridSize)
//                .taskExecutor(new SimpleAsyncTaskExecutor("spring_batch"))
//                .build();
//    }

    /**
     * 购汇批量 slaveStep
     * @param exchangePaymentReader
     * @param exchangePaymentProcess
     * @param exchangePaymentWriter
     * @return
     */
    @Bean
    public Step exchangePaymentStep(
            @Qualifier("exchangePaymentReader") ExchangePaymentReader exchangePaymentReader,
            @Qualifier("exchangePaymentProcessor") ExchangePaymentProcessor exchangePaymentProcess,
            @Qualifier("exchangePaymentWriter") ExchangePaymentWriter exchangePaymentWriter
    ) {
        return steps.get("exchangePaymentStep").<ExPurchaseInfoDTO, ExchangePaymentBO>chunk(chunkLimit)
                .reader(exchangePaymentReader).processor(exchangePaymentProcess)
                .writer(exchangePaymentWriter).build();
    }

    @Bean
    @StepScope
    public ExchangePaymentReader exchangePaymentReader(@Qualifier("businessDataSource") DataSource dataSource,
                                                       @Value("#{stepExecutionContext['fromId']}") String fromId,
                                                       @Value("#{stepExecutionContext['endId']}") String endId,
                                                       @Value("#{jobParameters['job.partitionKey']}") String partitionKey) {
        ExchangePaymentReader reader = new ExchangePaymentReader(partitionKey, dataSource);
        reader.setDataSource(dataSource);
        reader.setPageSize(pageSize);
        //记录状态信息和断点
        reader.setSaveState(true);
//        Map<String, Object> parameters = new HashMap<>(4);
//        parameters.put("fromId", fromId);
//        parameters.put("endId", endId);
//        if (null != partitionKey) {
//            parameters.put("partitionKey0", partitionKey.split("-")[0]);
//            parameters.put("partitionKey1", partitionKey.split("-")[1]);
//        }
//        reader.setParameterValues(parameters);
        return reader;
    }

    @Bean
    @StepScope
    public ExchangePaymentProcessor exchangePaymentProcessor() {
        return new ExchangePaymentProcessor();
    }

    @Bean
    @StepScope
    public ExchangePaymentWriter exchangePaymentWriter() {
        return new ExchangePaymentWriter();
    }
//
//    @Bean
//    @StepScope
//    public ExchangeCurrencyPartitioner exchangeCurrencyPartitioner() {
//        return new ExchangeCurrencyPartitioner();
//    }

//    @Bean
//    public TaskExecutor taskExecutor() {
//        return new SimpleAsyncTaskExecutor("spring_batch");
//    }

}
