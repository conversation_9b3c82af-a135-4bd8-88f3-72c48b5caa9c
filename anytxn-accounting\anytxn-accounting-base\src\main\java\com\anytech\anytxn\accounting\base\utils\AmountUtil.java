package com.anytech.anytxn.accounting.base.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;

/**
 * @description: 数据操作
 * @author: ZXL
 * @create: 2019-10-08 16:40
 * 
 * 普通工具类使用apache开源的{https://commons.apache.org/},
 * 如果涉及到特殊的功能可以自己编写,如果需要提交整合进biz-common,需要集中讨论
 * 本类仅在accountant工程使用,迁移至accountant.
 * @Deprecated
 */
public class AmountUtil {


    private static final Set<Collector.Characteristics> CH_NOID = Collections.EMPTY_SET;

    static class CollectorImpl<T, A, R> implements Collector<T, A, R> {
        private final Supplier<A> supplier;
        private final BiConsumer<A, T> accumulator;
        private final BinaryOperator<A> combiner;
        private final Function<A, R> finisher;
        private Set<Characteristics> characteristics;

        CollectorImpl(Supplier<A> supplier, BiConsumer<A, T> accumulator, BinaryOperator<A> combiner, Function<A, R> finisher, Set<Characteristics> characteristics) {
            this.supplier = supplier;
            this.accumulator = accumulator;
            this.combiner = combiner;
            this.finisher = finisher;
            this.characteristics = characteristics;
        }

        @Override
        public Supplier<A> supplier() {
            return supplier;
        }

        @Override
        public BiConsumer<A, T> accumulator() {
            return accumulator;
        }

        @Override
        public BinaryOperator<A> combiner() {
            return combiner;
        }

        @Override
        public Function<A, R> finisher() {
            return finisher;
        }

        @Override
        public Set<Characteristics> characteristics() {
            return characteristics;
        }
    }

    /**
     * @Description: 累加
     * @Param: [mapper]
     * @return: java.util.stream.Collector<T, ?, java.math.BigDecimal>
     * @Author: ZXL
     * @date: 2019/10/8
     */
    public static <T> Collector<T, ?, BigDecimal> summingBigDecimal(ToBigDecimalFunction<? super T> mapper) {
        return new CollectorImpl<>(() -> new BigDecimal[1], (a, t) -> {
            if (a[0] == null) {
                a[0] = BigDecimal.ZERO;
            }
            a[0] = a[0].add(mapper.applyAsBigDecimal(t));
        }, (a, b) -> {
            a[0] = a[0].add(b[0]);
            return a;
        }, a -> a[0], CH_NOID);
    }

    @FunctionalInterface
    public interface ToBigDecimalFunction<T> {
        /**
         * 累加
         * @param value
         * @return
         */
        BigDecimal applyAsBigDecimal(T value);
    }

    /**
     * @Description: 是否为0
     * @Param: [bigDecimal]
     * @return: boolean
     * @Author: ZXL
     * @date: 2019/10/8
     */
    public static boolean isZero(BigDecimal bigDecimal) {
        return BigDecimal.ZERO.compareTo(bigDecimal) == 0;
    }

    public static BigDecimal addAmount(BigDecimal... amounts) {
        return Arrays.stream(amounts).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
    }

    public static BigDecimal subtractAmount(BigDecimal... amounts) {
        return Arrays.stream(amounts).map(bigDecimal -> {
            if (bigDecimal == null) {
                bigDecimal = BigDecimal.ZERO;
            }
            return bigDecimal;
        }).reduce(BigDecimal::subtract).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
    }


}