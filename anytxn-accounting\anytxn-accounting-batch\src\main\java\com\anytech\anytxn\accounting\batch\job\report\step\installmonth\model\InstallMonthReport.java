package com.anytech.anytxn.accounting.batch.job.report.step.installmonth.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2020/9/10
 */
@Getter
@Setter
public class InstallMonthReport {

    /**
     * 技术id
     */
    private String id;

    /**
     * 机构
     */
    private String organizationNumber;

    /**
     * 入账日期
     */
    private LocalDate termPostDate;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 分期产品编号
     */
    private String productCode;

    /**
     * 分期产品描述
     */
    private String description;

    /**
     * 币种
     */
    private String currCode;

    /**
     * 本金金额
     */
    private BigDecimal termAmount;

    /**
     * 费用金额
     */
    private BigDecimal freeAmount;
}
