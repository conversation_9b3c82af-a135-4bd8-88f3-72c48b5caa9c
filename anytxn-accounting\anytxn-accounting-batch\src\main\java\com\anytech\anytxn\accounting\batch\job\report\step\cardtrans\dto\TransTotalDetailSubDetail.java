package com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2020/10/15 18:11
 */
@Getter
@Setter
public class TransTotalDetailSubDetail {

    private String transCode;//交易代码

    private String credDesc;//描述 贷记卡

    private String cardType;//准贷记卡

    private String settlementDesc;//结算金额

    private String transSource;//交易来源

    private String transCount;//交易笔数

    private String clearAmountDesc;//卡组织清算金额

    private String transResult;//交易结果

    private String DebitSubject;//借方科目

    private String credSubject;//贷方科目
}
