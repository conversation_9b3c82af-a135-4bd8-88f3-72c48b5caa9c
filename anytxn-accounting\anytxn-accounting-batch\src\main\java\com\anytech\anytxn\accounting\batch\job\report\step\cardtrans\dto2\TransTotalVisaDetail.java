package com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.dto2;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020/10/16 9:36
 */
@Getter
@Setter
@ToString
public class TransTotalVisaDetail {

    private String transSource;//交易来源

    private Integer transCount;//交易笔数

    private String transCurrency;//交易币种

    private BigDecimal postingAmount;//入账金额

    private String postingCurrency;//入账币种

    private String clearAmountDesc;//卡组织清算金额

    private String quotePrice;//牌价

    private String woff;//woff

    private String code;//code

}
