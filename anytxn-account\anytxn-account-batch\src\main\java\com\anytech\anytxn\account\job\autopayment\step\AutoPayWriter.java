package com.anytech.anytxn.account.job.autopayment.step;

import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogHistoryService;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogService;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2019-09-02
 **/
public class AutoPayWriter implements ItemWriter<AutoPaymentBO> {

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Autowired
    private IAutoPaymentLogService autoPaymentLogService;
    @Autowired
    private IAutoPaymentLogHistoryService autoPaymentLogHistoryService;


    public void write(List<? extends AutoPaymentBO> list) throws Exception {
        autoPaymentLogService.batchSave((List<AutoPaymentBO>) list);
        //插入自动还款历史
        autoPaymentLogHistoryService.batchSave((List<AutoPaymentBO>) list);
    }

    @Override
    public void write(Chunk<? extends AutoPaymentBO> chunk) throws Exception {
        write(chunk.getItems());
    }
}
