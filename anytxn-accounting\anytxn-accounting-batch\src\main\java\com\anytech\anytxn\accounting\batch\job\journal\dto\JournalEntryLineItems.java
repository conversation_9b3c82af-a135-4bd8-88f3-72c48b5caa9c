package com.anytech.anytxn.accounting.batch.job.journal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;


import java.math.BigDecimal;

/**
 * @Author: sukang
 * @Date: 2022/11/24 10:45
 */

@Getter
public class JournalEntryLineItems {

    private JournalIdDTO account;

    private String memo;

    private BigDecimal credit;

    private BigDecimal debit;

    @JsonProperty("cseg_pr_type")
    private JournalIdDTO csegProductType;


    private JournalIdDTO department;


    @JsonProperty("cseg_scheme")
    private JournalIdDTO csegScheme;



    @JsonProperty("cseg_business")
    private JournalIdDTO csegBusiness;


    public void setAccount(String externalId) {
        this.account = new JournalIdDTO(externalId);
    }

    public void setAccount(Integer externalId) {
        this.account = new JournalIdDTO(externalId);
    }



    public void setDepartment(Integer id) {
        this.department = new JournalIdDTO(id);
    }

    public void setCsegProductType(Integer id) {
        this.csegProductType = new JournalIdDTO(id);
    }

    public void setCsegScheme(Integer id) {
        this.csegScheme = new JournalIdDTO(id);
    }

    public void setCsegBusiness(Integer id) {
        this.csegBusiness = new JournalIdDTO(id);
    }



    public void setMemo(String memo) {
        this.memo = memo;
    }



    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }


    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }




}
