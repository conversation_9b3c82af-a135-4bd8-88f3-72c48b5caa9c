package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.anytxn.common.core.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 交易余额平衡检查结果 
 * 表:T_AMS_TXN_CHK 
 * <AUTHOR> 
 * @date 2019-10-11 
 */
public class TAmsTxnChk extends BaseEntity {
    /**
     * 技术主键
     * 表字段:ID
     */
    private Long id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    private String accountManagementId;

    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    private String acctLogo;

    /**
     * 全局业务流水号
     * 表字段:GLOBAL_FLOW_NO
     */
    private String globalFlowNo;

    /**
     * 模块标识
     * 表字段:MODULE_FLAG
     */
    private String moduleFlag;

    /**
     * 交易码
     * 表字段:TXN_CODE
     */
    private String txnCode;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    private LocalDate postingDate;

    /**
     * 入账币种
     * 表字段:POSTING_CURRENCY_CODE
     */
    private String postingCurrencyCode;

    /**
     * 入账金额
     * 表字段:POSTING_AMT
     */
    private BigDecimal postingAmt;

    /**
     * 表内表外标识
     * 表字段:INTEREST_IND
     */
    private String interestInd;

    /**
     * 价税分离标识
     * 表字段:PRICE_TAX_FLG
     */
    private String priceTaxFlg;

    /**
     * 原始交易码
     * 表字段:TXN_CODE_ORIG
     */
    private String txnCodeOrig;

    /**
     * 余额类型
     * 表字段:BAL_TYPE
     */
    private String balType;

    /**
     * 交易标志
     * 表字段:TXN_IND
     */
    private String txnInd;

    /**
     * 余额结转标志
     * 表字段:BAL_PROCESS_IND
     */
    private String balProcessInd;

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    private String absStatus;

    /**
     * 资产包编号
     * 表字段:ASSET_NO
     */
    private String assetNo;

    /**
     * 原始交易ABS借贷记标志
     * 表字段:ORIG_TXN_ABS_IND
     */
    private String origTxnAbsInd;

    /**
     * 分期订单号
     * 表字段:ORDER_ID
     */
    private String orderId;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

    /**
     * 获取技术主键
     * @return id Long
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置技术主键
     * @param id 技术主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取分行号
     * @return branchid String
     */
    public String getBranchid() {
        return branchid;
    }

    /**
     * 设置分行号
     * @param branchid 分行号
     */
    public void setBranchid(String branchid) {
        this.branchid = branchid == null ? null : branchid.trim();
    }

    /**
     * 获取账户管理信息id
     * @return accountManagementId String
     */
    public String getAccountManagementId() {
        return accountManagementId;
    }

    /**
     * 设置账户管理信息id
     * @param accountManagementId 账户管理信息id
     */
    public void setAccountManagementId(String accountManagementId) {
        this.accountManagementId = accountManagementId == null ? null : accountManagementId.trim();
    }

    /**
     * 获取产品代码
     * @return acctLogo String
     */
    public String getAcctLogo() {
        return acctLogo;
    }

    /**
     * 设置产品代码
     * @param acctLogo 产品代码
     */
    public void setAcctLogo(String acctLogo) {
        this.acctLogo = acctLogo == null ? null : acctLogo.trim();
    }

    /**
     * 获取全局业务流水号
     * @return globalFlowNo String
     */
    public String getGlobalFlowNo() {
        return globalFlowNo;
    }

    /**
     * 设置全局业务流水号
     * @param globalFlowNo 全局业务流水号
     */
    public void setGlobalFlowNo(String globalFlowNo) {
        this.globalFlowNo = globalFlowNo == null ? null : globalFlowNo.trim();
    }

    /**
     * 获取模块标识
     * @return moduleFlag String
     */
    public String getModuleFlag() {
        return moduleFlag;
    }

    /**
     * 设置模块标识
     * @param moduleFlag 模块标识
     */
    public void setModuleFlag(String moduleFlag) {
        this.moduleFlag = moduleFlag == null ? null : moduleFlag.trim();
    }

    /**
     * 获取交易码
     * @return txnCode String
     */
    public String getTxnCode() {
        return txnCode;
    }

    /**
     * 设置交易码
     * @param txnCode 交易码
     */
    public void setTxnCode(String txnCode) {
        this.txnCode = txnCode == null ? null : txnCode.trim();
    }

    /**
     * 获取入账日期
     * @return postingDate LocalDate
     */
    public LocalDate getPostingDate() {
        return postingDate;
    }

    /**
     * 设置入账日期
     * @param postingDate 入账日期
     */
    public void setPostingDate(LocalDate postingDate) {
        this.postingDate = postingDate;
    }

    /**
     * 获取入账币种
     * @return postingCurrencyCode String
     */
    public String getPostingCurrencyCode() {
        return postingCurrencyCode;
    }

    /**
     * 设置入账币种
     * @param postingCurrencyCode 入账币种
     */
    public void setPostingCurrencyCode(String postingCurrencyCode) {
        this.postingCurrencyCode = postingCurrencyCode == null ? null : postingCurrencyCode.trim();
    }

    /**
     * 获取入账金额
     * @return postingAmt BigDecimal
     */
    public BigDecimal getPostingAmt() {
        return postingAmt;
    }

    /**
     * 设置入账金额
     * @param postingAmt 入账金额
     */
    public void setPostingAmt(BigDecimal postingAmt) {
        this.postingAmt = postingAmt;
    }

    /**
     * 获取表内表外标识
     * @return interestInd String
     */
    public String getInterestInd() {
        return interestInd;
    }

    /**
     * 设置表内表外标识
     * @param interestInd 表内表外标识
     */
    public void setInterestInd(String interestInd) {
        this.interestInd = interestInd == null ? null : interestInd.trim();
    }

    /**
     * 获取价税分离标识
     * @return priceTaxFlg String
     */
    public String getPriceTaxFlg() {
        return priceTaxFlg;
    }

    /**
     * 设置价税分离标识
     * @param priceTaxFlg 价税分离标识
     */
    public void setPriceTaxFlg(String priceTaxFlg) {
        this.priceTaxFlg = priceTaxFlg == null ? null : priceTaxFlg.trim();
    }

    /**
     * 获取原始交易码
     * @return txnCodeOrig String
     */
    public String getTxnCodeOrig() {
        return txnCodeOrig;
    }

    /**
     * 设置原始交易码
     * @param txnCodeOrig 原始交易码
     */
    public void setTxnCodeOrig(String txnCodeOrig) {
        this.txnCodeOrig = txnCodeOrig == null ? null : txnCodeOrig.trim();
    }

    /**
     * 获取余额类型
     * @return balType String
     */
    public String getBalType() {
        return balType;
    }

    /**
     * 设置余额类型
     * @param balType 余额类型
     */
    public void setBalType(String balType) {
        this.balType = balType == null ? null : balType.trim();
    }

    /**
     * 获取交易标志
     * @return txnInd String
     */
    public String getTxnInd() {
        return txnInd;
    }

    /**
     * 设置交易标志
     * @param txnInd 交易标志
     */
    public void setTxnInd(String txnInd) {
        this.txnInd = txnInd == null ? null : txnInd.trim();
    }

    /**
     * 获取余额结转标志
     * @return balProcessInd String
     */
    public String getBalProcessInd() {
        return balProcessInd;
    }

    /**
     * 设置余额结转标志
     * @param balProcessInd 余额结转标志
     */
    public void setBalProcessInd(String balProcessInd) {
        this.balProcessInd = balProcessInd == null ? null : balProcessInd.trim();
    }

    /**
     * 获取资产出表状态
     * @return absStatus String
     */
    public String getAbsStatus() {
        return absStatus;
    }

    /**
     * 设置资产出表状态
     * @param absStatus 资产出表状态
     */
    public void setAbsStatus(String absStatus) {
        this.absStatus = absStatus == null ? null : absStatus.trim();
    }

    /**
     * 获取资产包编号
     * @return assetNo String
     */
    public String getAssetNo() {
        return assetNo;
    }

    /**
     * 设置资产包编号
     * @param assetNo 资产包编号
     */
    public void setAssetNo(String assetNo) {
        this.assetNo = assetNo == null ? null : assetNo.trim();
    }

    /**
     * 获取原始交易ABS借贷记标志
     * @return origTxnAbsInd String
     */
    public String getOrigTxnAbsInd() {
        return origTxnAbsInd;
    }

    /**
     * 设置原始交易ABS借贷记标志
     * @param origTxnAbsInd 原始交易ABS借贷记标志
     */
    public void setOrigTxnAbsInd(String origTxnAbsInd) {
        this.origTxnAbsInd = origTxnAbsInd == null ? null : origTxnAbsInd.trim();
    }

    /**
     * 获取分期订单号
     * @return orderId String
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置分期订单号
     * @param orderId 分期订单号
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * 获取创建日期
     * @return createTime LocalDateTime
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建日期
     * @param createTime 创建日期
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新日期
     * @return updateTime LocalDateTime
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新日期
     * @param updateTime 更新日期
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新用户
     * @return updateBy String
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新用户
     * @param updateBy 更新用户
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    /**
     * 获取版本号
     * @return versionNumber Long
     */
    public Long getVersionNumber() {
        return versionNumber;
    }

    /**
     * 设置版本号
     * @param versionNumber 版本号
     */
    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }
}