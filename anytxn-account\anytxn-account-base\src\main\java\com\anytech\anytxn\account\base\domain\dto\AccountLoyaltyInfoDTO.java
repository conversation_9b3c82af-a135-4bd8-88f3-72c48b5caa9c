package com.anytech.anytxn.account.base.domain.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/4/9  10:52
 * @Version 1.0
 */

@Getter
@Setter
public class AccountLoyaltyInfoDTO {
  /**
   * 主键ID
   * 表字段：ID
   */
  private String id;

  /**
   * 管理账户ID
   * 表字段：ACCOUNT_MANAGEMENT_ID
   */
  @NotBlank(message ="accountManagementId is a mandatory field")
  private String accountManagementId;

  /**
   * 权益ID
   * 表字段：PROJECT_ID
   */
  @NotBlank(message ="projectId is a mandatory field")
  private String projectId;

  /**
   * 权益账户姓
   * 表字段：FAMIIY
   */
  private String family;

  /**
   * 权益账户名
   * 表字段：GIVEN
   */
  private String given;

  /**
   * 权益账户号码
   * 表字段：ACCOUNT_NUMBER
   */
  private String accountNumber;

  /**
   * 权益账户类型
   * 表字段：LOYALTY_ACCOUNT_TYPE
   */
  private String loyaltyAccountType;

  /**
   * 上一权益ID
   * 表字段：PREVIOUS_PROJECT_ID
   */
  private String previousProjectId;

  /**
   * 上一权益账户姓
   * 表字段：PREVIOUS_FAMIIY
   */
  private String previousFamily;

  /**
   * 上一权益账户名
   * 表字段：PREVIOUS_GIVEN
   */
  private String previousGiven;

  /**
   * 上一权益账户号码
   * 表字段：PREVIOUS_ACCOUNT_NUMBER
   */
  private String previousAccountNumber;

  /**
   * 上一权益账户类型
   * 表字段： PREVIOUS_LOYALTY_ACCOUNT_TYPE
   */
  private String preciousLoyaltyAccountType;

  /**
   * 权益生效时间
   * 表字段：START_DATE
   */
  private LocalDate startDate;

  /**
   * 生效方式
   */
  private  String effectMode;


  /**
   * 同步标志0：未同步，1：已同步 2：不同步
   * 表字段：SYNC_INDICATOR
   */
  private String syncIndicator;


  /**
   * 同步次数，不能超过3次，吵过次认定为不需同步数据
   * 表字段：SYNC_NUMBER
   */
  private Long syncNumber;

  /**
   * 创建时间
   * 表字段：CREATE_TIME
   */
  private LocalDateTime createTime;

  /**
   * 更新时间
   * 表字段：UPDATE_TIME
   */
  private LocalDateTime updateTime;

  /**
   * 操作员
   * 表字段：UPDATE_BY
   */
  private String updateBy;

  /**
   * 版本号
   * 表字段：VERSION_NUMBER
   */
  private Long versionNumber;

  /**
   * 积分活动类型
   */
  public String  loyaltyType;


}
