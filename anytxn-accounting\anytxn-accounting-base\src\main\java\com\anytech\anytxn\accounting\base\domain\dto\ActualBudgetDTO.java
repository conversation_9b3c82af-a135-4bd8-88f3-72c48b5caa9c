package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
*@description: 
*@author: ZXL
*@create: 2019-10-12 14:18
*/
@Getter
@Setter
public class ActualBudgetDTO extends BaseEntity {
    private TAmsGlacgpDTO tAmsGlacgp;
    private List<TAmsGlvcherSumDTO> amsGlvcherSums;
    private List<TAmsGlacgdDTO> amsGlacgds;
    private List<TAmsGlacgmDTO> amsGlacgms;
    private List<TAmsGlacgyDTO> amsGlacgies;
    private List<TAmsGlacgpDTO> amsGlacgpList;
    private List<TAmsGlacgdBDTO> amsGlacgdBs;
    private List<TAmsGlacgmBDTO> amsGlacgmBs;
    private List<TAmsGlacgyBDTO> amsGlacgyBs;

    public ActualBudgetDTO(){
        amsGlvcherSums=new ArrayList<>(16);
        amsGlacgds=new ArrayList<>(16);
        amsGlacgms=new ArrayList<>(16);
        amsGlacgies=new ArrayList<>(16);
        amsGlacgpList=new ArrayList<>(16);
        amsGlacgdBs=new ArrayList<>(16);
        amsGlacgmBs=new ArrayList<>(16);
        amsGlacgyBs=new ArrayList<>(16);
    }


    @Override
    public String toString() {
        return "ActualBudgetDTO{" +
                "tAmsGlacgp=" + tAmsGlacgp +
                ", amsGlvcherSums=" + amsGlvcherSums +
                ", amsGlacgds=" + amsGlacgds +
                ", amsGlacgms=" + amsGlacgms +
                ", amsGlacgies=" + amsGlacgies +
                ", amsGlacgpList=" + amsGlacgpList +
                ", amsGlacgdBs=" + amsGlacgdBs +
                ", amsGlacgmBs=" + amsGlacgmBs +
                ", amsGlacgyBs=" + amsGlacgyBs +
                '}';
    }
}