package com.anytech.anytxn.accounting.batch.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 每月分期付账配置
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.report.install-month")
public class InstallMonthReportConfigurer extends BaseConfigurer{

}
