package com.anytech.anytxn.account.client;

import com.anytech.anytxn.account.base.domain.dto.TxnUpdateToLoyaltyRespDTO;
import com.anytech.anytxn.account.base.domain.dto.TxnUpdateToLoyaltyReqDTO;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Loyalty服务调用
 *
 * <AUTHOR>
 * @Date 2024/4/9  11:14
 * @Version 1.0
 */
@Component
@RequestMapping("/loyalty/v1/biz/api")
@ResponseBody
public class TxnToLoyaltyFeignFallBack {

    @PutMapping(path = "/program/update")
    AnyTxnHttpResponse<List<TxnUpdateToLoyaltyRespDTO>>  txnUpdateToLoyalty(@RequestBody TxnUpdateToLoyaltyReqDTO txnUpdateToLoyalty,@RequestHeader("organizationNumber") String organizationNumber) {
        return AnyTxnHttpResponse.fail(AnyTxnAccountingRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), "LOYALTY项目服务不可用");
    }
}

