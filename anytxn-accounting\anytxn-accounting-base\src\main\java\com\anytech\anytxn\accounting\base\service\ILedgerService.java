package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.LedgerDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
/**
 * <AUTHOR>
public interface ILedgerService {
    /**
     * TODO
      * @param tPmsGlacgnDTO
     * @return com.anytech.anytxn.accountant.dto.LedgerDTO
     * <AUTHOR> @date 2020-7-28
     */
    LedgerDTO handleLedger(TPmsGlacgnDTO tPmsGlacgnDTO);

}
