package com.anytech.anytxn.account.controller;

import com.anytech.anytxn.account.base.service.IAccountLoyaltyInfoService;
import com.anytech.anytxn.account.base.domain.dto.AccountLoyaltyInfoDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 账户权益信息操作
 *
 * <AUTHOR>
 * @Date 2024/3/22  17:42
 * @Version 1.0
 */

@RestController
@Tag(name = "账户权益信息接口")
public class AccountLoyaltyInfoController extends BizBaseController {

    @Autowired
    private IAccountLoyaltyInfoService accountLoyaltyInfoService;

    @Operation(summary = "更新账户权益信息信息")
    @PostMapping("/account/loyalty/modify")
    public AnyTxnHttpResponse<AccountLoyaltyInfoDTO> modifyAccountLoyaltyInfo(@RequestBody AccountLoyaltyInfoDTO accountLoyaltyInfo) {
        return AnyTxnHttpResponse.success(accountLoyaltyInfoService.updateAccountLoyaltyInfo(accountLoyaltyInfo));
    }

    @Operation(summary = "根据管理账户ID查询账户权益信息")
    @GetMapping("/account/loyalty/query")
    public AnyTxnHttpResponse<AccountLoyaltyInfoDTO> queryAccountLoyaltyInfo(@RequestParam(value = "accountManagementId") String accountManagementId) {
        return AnyTxnHttpResponse.success(accountLoyaltyInfoService.queryAccountLoyaltyInfo(accountManagementId));
    }
}
