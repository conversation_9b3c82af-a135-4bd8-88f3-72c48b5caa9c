package com.anytech.anytxn.account.job.autopayment.step;

import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2019-09-02
 **/
public class AutoPayReader extends JdbcPagingItemReader<AutoEbitSignUpInforDTO> {

    public AutoPayReader(DataSource dataSource) {
        super();
        this.setRowMapper(new BeanPropertyRowMapper<>(AutoEbitSignUpInforDTO.class));
        this.setQueryProvider(AutoPayReader.oraclePagingQueryProvider(dataSource));
    }
    /**
     * v3r13改造： 读取自动扣款签约信息表
     */
    private static PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean provider = new SqlPagingQueryProviderFactoryBean();
        //获得入口数据
        provider.setSelectClause("ID,CUSTOMER_ID, AUTO_DEBIT_TYPE, CONTRACT_TYPE, AUTO_DEBIT_EXCHANGE_SIGN, " +
                "AUTO_PAYMENT_TYPE,BANKFLAG,AUTO_DEBIT_AFFILIATED_ACCOUNT,AUTO_DEBIT_RELATED_ACCOUNT_BANK_NUMBER," +
                "AUTO_DEBIT_RELATED_ACCOUNT_BRANCH_NUMBER,TEMPORARY_AUTO_DEBIT_OPERATION_DATE,CONTRACT_DATE,RELEASE_DATE," +
                "SOURCE,ORGANIZATION_NUMBER");
        provider.setFromClause("AUTO_EBIT_SIGN_UP_INFOR");
        String orgConditionStr = "ORGANIZATION_NUMBER = "+ OrgNumberUtils.getOrg();
        //设置查询条件
        provider.setWhereClause(orgConditionStr+" and CONTRACT_TYPE = '0'");

        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("ID", Order.ASCENDING);
        provider.setSortKeys(sortKey);
        provider.setDataSource(dataSource);
        try {
            return provider.getObject();
        } catch (Exception e) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.UNKONWN_ERR);
        }
    }
}
