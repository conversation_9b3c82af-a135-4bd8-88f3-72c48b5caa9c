package com.anytech.anytxn.accounting.batch.job.journal.task;

import com.anytech.anytxn.accounting.base.exception.AnyTxnAccountantException;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import com.anytech.anytxn.accounting.batch.job.journal.OracleSuiteApi;
import com.anytech.anytxn.accounting.batch.job.journal.OracleSuiteProperties;
import com.anytech.anytxn.accounting.batch.job.journal.dto.JournalEntry;
import com.anytech.anytxn.accounting.base.service.IAccountSummaryService;
import com.anytech.anytxn.business.dao.accounting.mapper.SubjectRecordSumMapper;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSumOracleLog;
import com.anytech.anytxn.common.core.constants.PropsConstant;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSum;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.accounting.mapper.ParmAccountantDictMappingMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;

/**
 * @Author: sukang
 * @Date: 2022/12/1 20:58
 */
@Slf4j
public class JournalBusinessTasklet extends AbstractJournalTasklet {

    @Resource
    private SubjectRecordSumMapper subjectRecordSumMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;



    @Value("#{jobParameters['job.accruedThruDay']}")
    public String accruedThruDay;


    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.journalEntryJob.approve:true}")
    public Boolean approve;

    @Resource
    private OracleSuiteProperties oracleSuiteProperties;


    @Resource
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;


    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    private ParmAccountantDictMappingMapper parmAccountantDictMappingMapper;



    @Resource
    private IAccountSummaryService accountSummaryServiceImpl;


    @Override
    RepeatStatus doProcess(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        String sumDate = getToDay(parmOrganizationInfoSelfMapper, accruedThruDay);

        // todo 临时为空
        List<SubjectRecordSum> subjectRecordSums = subjectRecordSumMapper.selectByDateGroupByField(
                sumDate,null,null,null,null);

        OracleSuiteApi oracleSuiteApi = new OracleSuiteApi(oracleSuiteProperties);


        for (SubjectRecordSum subjectRecordSum : subjectRecordSums) {

//            List<SubjectRecordSum> sumList = subjectRecordSumMapper.selectByDateAndField(
//                    getToDay(parmOrganizationInfoSelfMapper, accruedThruDay),
//                    subjectRecordSum.getModuleFlag(), subjectRecordSum.getCrdOrganization(),
//                    subjectRecordSum.getAccountProduct());
            // todo 临时调整临时为空
            List<SubjectRecordSum> sumList = subjectRecordSumMapper.selectByDateAndField(null);

            subjectRecordSum.setSumDate(sumDate);
            SubjectRecordSumOracleLog subjectRecordSumOracleLog = accountSummaryServiceImpl.insertSumOracleRecord(
                    subjectRecordSum);

            if (subjectRecordSumOracleLog.updateSuccessed()){
                continue;
            }


            log.info("group is {}--{}--{}", subjectRecordSum.getModuleFlag()
                    , subjectRecordSum.getCrdOrganization(), subjectRecordSum.getAccountProduct());


            JournalEntry journalEntry = buildJournalEntry(sumList, parmAccountantDictMappingMapper);
            journalEntry.setApproved(approve);

            journalEntry.setMemo(subjectRecordSum.getModuleFlag()
                    + ":" + subjectRecordSum.getCrdOrganization()
                    + ":" + subjectRecordSum.getAccountProduct());




            Boolean doPostToOralceApi = doPostToOralceApi(oracleSuiteApi, journalEntry);

            if (doPostToOralceApi) {
                accountSummaryServiceImpl.updateSuccessLogStatus(subjectRecordSumOracleLog.getId());
            }else {
                throw new AnyTxnAccountantException(AccountantRespCodeEnum.P_NET_SUITE_FAIL);
            }
        }

        return RepeatStatus.FINISHED;
    }










    private void initThreadPool(int i) {
        if(threadPoolTaskExecutor == null){
            threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
            threadPoolTaskExecutor.setCorePoolSize(10);
            threadPoolTaskExecutor.setMaxPoolSize(15);

            threadPoolTaskExecutor.initialize();
        }
    }
}
