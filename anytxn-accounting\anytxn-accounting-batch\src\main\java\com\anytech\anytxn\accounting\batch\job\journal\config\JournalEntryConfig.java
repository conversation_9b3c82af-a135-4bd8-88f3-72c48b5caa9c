package com.anytech.anytxn.accounting.batch.job.journal.config;

import com.anytech.anytxn.accounting.batch.job.journal.task.JournalBusinessTasklet;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.batch.core.job.flow.support.SimpleFlow;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;

/**
 * @Author: sukang
 * @Date: 2022/11/22 12:48
 */
@Configuration
public class JournalEntryConfig {



    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;


    @Bean
    public Job journalEntryJob(@Qualifier("journalEntryFlow") Flow journalEntryFlow) {

        return jobs.get("journalEntryJob")
                .start(journalEntryFlow)
                .build()
                .build();

    }


    @Bean
    public Flow journalEntryFlow(@Qualifier("journalEntryExecutor") TaskExecutor journalEntryExecutor,
                                 @Qualifier("businessFlow") Flow businessFlow) {
        return new FlowBuilder<SimpleFlow>("splitFlow")
                .split(journalEntryExecutor)
                .add(businessFlow)
                .build();
    }




   /* @Bean
    public Flow schemeFlow(@Qualifier("schemeStep") Step schemeStep) {
        return new FlowBuilder<SimpleFlow>("schemeFlow")
                .start(schemeStep)
                .build();
    }

    @Bean
    public Step schemeStep(@Qualifier("journalSchemeTasklet") Tasklet journalSchemeTasklet) {
       return steps.get("schemeStep")
                .tasklet(journalSchemeTasklet)
                .build();
    }


    @Bean
    @StepScope
    public JournalSchemeTasklet journalSchemeTasklet() {
        return new JournalSchemeTasklet();
    }*/





    @Bean
    public Step businessStep(@Qualifier("journalBusinessTasklet") Tasklet journalBusinessTasklet) {
        return steps.get("businessStep")
                .tasklet(journalBusinessTasklet)
                .build();
    }

    @Bean
    public Flow businessFlow(@Qualifier("businessStep") Step businessStep) {
        return new FlowBuilder<SimpleFlow>("businessFlow")
                .start(businessStep)
                .build();
    }

    @Bean
    @StepScope
    public Tasklet journalBusinessTasklet() {
        return new JournalBusinessTasklet();
    }






    /*@Bean
    public Flow productTypeFlow(@Qualifier("productTypeStep") Step productTypeStep) {
        return new FlowBuilder<SimpleFlow>("productTypeFlow")
                .start(productTypeStep)
                .build();
    }



    @Bean
    public Step productTypeStep(@Qualifier("journalProductTypeTasklet") Tasklet journalProductTypeTasklet) {
        return steps.get("productTypeStep")
                .tasklet(journalProductTypeTasklet)
                .build();
    }

    @Bean
    @StepScope
    public Tasklet journalProductTypeTasklet() {
        return new JournalProductTypeTasklet();
    }*/


    @Bean
    public TaskExecutor journalEntryExecutor() {
        return new SimpleAsyncTaskExecutor("journalEntryJob");
    }






}
