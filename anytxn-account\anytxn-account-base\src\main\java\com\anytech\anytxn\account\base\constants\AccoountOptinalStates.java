package com.anytech.anytxn.account.base.constants;

/**
 * <AUTHOR>
 * @date 2022/9/20
 * @apiNote
 */
public enum AccoountOptinalStates {

    /**
     *  状态
     *  0:ENROLLMENT_DATE和CANCEL_DATE同为空   没有被锁定
     *  1：ENROLLMENT_DATE和CANCEL_DATE同时非空 封锁后之后被解封
     *  2：ENROLLMENT_DATE非空，CANCEL_DATE为空 锁定状态
     */


    NARMAL("0", "正常"),

    UNLOCK("1", "解除封锁"),

    LOCKED("2","封锁");

    private String code;

    private String msg;


    AccoountOptinalStates(String code, String reason) {
        this.code = code;
        this.msg = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return msg;
    }



}
