package com.anytech.anytxn.accounting.batch.job.report.step.amountdetail;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.amountdetail.dto.Receivables;
import com.anytech.anytxn.accounting.batch.job.report.step.amountdetail.model.AmountDetail;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmProductInfoSelfMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.anytech.anytxn.accounting.batch.config.AmountDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportListTasklet;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlacgn;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应收款明细
 * <AUTHOR>
 * @date 2020/9/14
 */
//TODO 有点慢
public class AmountDetailReportTasklet extends AbstractReportListTasklet {

    private TimeTypeEnum timeTypeEnum;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private TPmsGlacgnSelfMapper tPmsGlacgnSelfMapper;
    @Autowired
    private AccountantGlvcherSelfMapper accountantGlvcherSelfMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;
    @Autowired
    private ParmProductInfoSelfMapper parmProductInfoSelfMapper;
    @Autowired
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;

    public AmountDetailReportTasklet(BaseConfigurer baseConfigurer, TimeTypeEnum timeTypeEnum) {
        super(baseConfigurer);
        this.timeTypeEnum = timeTypeEnum;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        if (reportConfigurer.isAbleReport()) {
            List<AccountantGlvcher> glvcherList = loadGlvcherModels();

            List<AmountDetail> data = modelToReport(glvcherList);
            out(data);
        }

        return RepeatStatus.FINISHED;
    }

    /**
     * 当前日期资产/负债传票（已处理）
     * @return
     */
    private List<AccountantGlvcher> loadGlvcherModels(){
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        List<AccountantGlvcher> result = null;
        switch (timeTypeEnum) {
            case D:
//                organizationInfo.setToday(LocalDate.of(2020,2,2));
                result = accountantGlvcherSelfMapper.selectByPostDateAndR2(organizationInfo.getOrganizationNumber(), organizationInfo.getToday());
                break;
            case M:
//                organizationInfo.setToday(LocalDate.of(2020,5,31));
                if (TimeUtils.isEndMonth(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.mothRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndR2(organizationInfo.getOrganizationNumber(),mothRand.getKey(), mothRand.getValue());
                }
                break;
            case Q:
//                organizationInfo.setToday(LocalDate.of(2020,6,30));
                if (TimeUtils.isEndQuarter(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.quarterRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndR2(organizationInfo.getOrganizationNumber(),mothRand.getKey(), mothRand.getValue());
                }
                break;
            case Y:
//                organizationInfo.setToday(LocalDate.of(2020,12,31));
                if (TimeUtils.isEndYear(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> yearRand = TimeUtils.yearRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndR2(organizationInfo.getOrganizationNumber(),yearRand.getKey(), yearRand.getValue());
                }
                break;
            default:
                break;
        }

        if (CollectionUtils.isNotEmpty(result)) {
            // 资产类和负债类
            List<TPmsGlacgn> pmsGlacgns = tPmsGlacgnSelfMapper.findByGlClass(Lists.newArrayList("ASSE", "DEBT"),OrgNumberUtils.getOrg());
            Set<String> glAcct = pmsGlacgns.stream().map(TPmsGlacgn::getGlAcct).collect(Collectors.toSet());

            result = result.stream().filter(x-> glAcct.contains(x.getGlAcct())).collect(Collectors.toList());
        }

        return result;
    }

    /**
     * 业务数据转报表数据
     * @return 入账汇总,入账会计,入账差异汇总,入账差异会计
     */
    private List<AmountDetail> modelToReport(List<AccountantGlvcher> accountantGlvchers){
        List<AmountDetail> result = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(accountantGlvchers)) {
            Map<String, AccountManagementInfo> aCache = Maps.newHashMap();
            Map<String, CustomerAuthorizationInfo> cCache = Maps.newHashMap();

            // 先根据管理账号分组
            Map<String, List<AccountantGlvcher>> group = accountantGlvchers.stream().collect(Collectors.groupingBy(x -> x.getAccountManagementId() + x.getAcctLogo() + x.getCurrCode() + x.getGlAcct() + x.getDrcr()));
            List<AmountDetail> cache = Lists.newArrayList();


            for (Map.Entry<String, List<AccountantGlvcher>> entry : group.entrySet()) {
                List<AccountantGlvcher> value = entry.getValue();
                AccountantGlvcher temple = value.get(0);

                AmountDetail detail = new AmountDetail();
                // 分行号
                detail.setBranchId(temple.getBranchid());
                // 客户号
                AccountManagementInfo managementInfo = aCache.containsKey(temple.getAccountManagementId()) ? aCache.get(temple.getAccountManagementId()) : accountManagementInfoMapper.selectByPrimaryKey(temple.getAccountManagementId());
                aCache.put(managementInfo.getAccountManagementId(), managementInfo);
                detail.setCustomerId(managementInfo.getCustomerId());
                // 客户名称
                CustomerAuthorizationInfo customer = cCache.containsKey(managementInfo.getCustomerId()) ? cCache.get(managementInfo.getCustomerId()) : customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),managementInfo.getCustomerId());
                cCache.put(customer.getCustomerId(), customer);
                detail.setCustomerName(customer.getChineseName());
                // 产品ID
                detail.setAcctLogo(temple.getAcctLogo());
                // 描述
                try {
//                    ParmProductInfo parmProductInfo = parmProductInfoSelfMapper.selectByProdNumber(temple.getAcctLogo()).get(0);
                    ParmAcctProductMainInfo acctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(OrgNumberUtils.getOrg(), temple.getAcctLogo());
                    detail.setDescription(acctProductMainInfo.getDescription());
                }catch (Exception e){

                }
                // 币种
                detail.setCurrCode(temple.getCurrCode());
                // 入账日期
                detail.setPostDate(temple.getPostingDate());
                // 科目号
                detail.setGlAcct(temple.getGlAcct());
                // 科目名称
                TPmsGlacgn pmsGlacgn = tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(temple.getOrganizationNumber(), temple.getBranchid(), temple.getGlAcct(), temple.getCurrCode());
                detail.setGlAcctName(pmsGlacgn.getGlAcctName());
                // 借贷方向
                detail.setDrcr(temple.getDrcr());
                // 发生额
                Optional<BigDecimal> decimal = value.stream().map(AccountantGlvcher::getGlAmount).reduce(BigDecimal::add);
                detail.setGlAmount(decimal.orElse(BigDecimal.ZERO));

                cache.add(detail);
            }

            // 再根据客户号分组
            Map<String, List<AmountDetail>> group2 = cache.stream().collect(Collectors.groupingBy(x -> x.getCustomerId() + x.getAcctLogo() + x.getCurrCode() + x.getGlAcct() + x.getDrcr()));
            for (Map.Entry<String, List<AmountDetail>> entry : group2.entrySet()) {
                List<AmountDetail> value = entry.getValue();

                Optional<BigDecimal> decimal = value.stream().map(AmountDetail::getGlAmount).reduce(BigDecimal::add);
                AmountDetail temple = value.get(0);
                temple.setGlAmount(decimal.orElse(BigDecimal.ZERO));

                result.add(temple);
            }
        }

        // 基于客户号排序
        return result.stream().sorted(Comparator.comparing(AmountDetail::getCustomerId)).collect(Collectors.toList());
    }

    /**
     * 输出报表
     * @param data
     */
    private void out(List<AmountDetail> data) throws FileNotFoundException {
        if (CollectionUtils.isNotEmpty(data)) {
            AmountDetailReportConfigurer reportConfigurer0 = (AmountDetailReportConfigurer)reportConfigurer;
            Map<String, Object> params = new HashMap<>();
            reportConfigurer0.putParam(params, timeTypeEnum);

            outReport(Lists.newArrayList(Receivables.resetReportData(data, timeTypeEnum)), reportConfigurer0.outFile(timeTypeEnum), reportConfigurer0.outFileName(timeTypeEnum), reportConfigurer0.templeFile(timeTypeEnum), params);
        }
    }
}
