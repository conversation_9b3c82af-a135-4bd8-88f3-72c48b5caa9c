package com.anytech.anytxn.account.job.errorfile.config;

import com.anytech.anytxn.account.job.errorfile.step.ErrorFileTasklet;
import com.anytech.anytxn.common.core.config.batch.CommonDbConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 差错文件处理
 *
 * <AUTHOR>
 * @date 2021/8/2
 */
@Configuration
@Slf4j
public class ErrorFileConfig {
    @Autowired
    private JobBuilderFactory jobBuilderFactory;

    @Autowired
    private StepBuilderFactory stepBuilderFactory;

    @Bean
    public Job errorFileJob(@Qualifier("errorFileStep") Step errorFileStep) {
        return jobBuilderFactory.get("errorFileJob")
                .start(errorFileStep)
                .build();
    }

    @Bean
    @StepScope
    public ErrorFileTasklet errorFileTasklet(@Qualifier(CommonDbConfiguration.SQLSESSIONTEMPLATE) SqlSessionTemplate sqlSessionTemplateCommon) {
        return new ErrorFileTasklet(sqlSessionTemplateCommon);
    }

    @Bean
    public Step errorFileStep(@Qualifier("errorFileTasklet")ErrorFileTasklet errorFileTasklet) {
        return stepBuilderFactory.get("errorFileStep")
                .tasklet(errorFileTasklet)
                .build();
    }
}
