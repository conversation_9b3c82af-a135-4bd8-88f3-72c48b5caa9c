package com.anytech.anytxn.accounting.base.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 模块标识
 * @date 2020-02-16 14:06
 **/
public enum AbsStatusEnum {
    /**
     * F-封包
     * A-已出表
     * N-未出表
     */
    CORE("F", "封包"),
    CHARGE_OFF("A", "已出表"),
    TAX("N", "未出表"),
    ;
    private String code;
    private String desc;


    AbsStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String getValue(String code) {
        if (code == null) {
            return null;
        }
        AbsStatusEnum[] var2 = values();
        int var3 = var2.length;
        for (int var4 = 0; var4 < var3; ++var4) {
            AbsStatusEnum moduleFlagEnum = var2[var4];
            if (Objects.equals(code, moduleFlagEnum.getCode())) {
                return moduleFlagEnum.getDesc();
            }
        }
        return null;
    }
}
