package com.anytech.anytxn.accounting.batch.config.file.IrrgulardataFiles;

import com.anytech.anytxn.accounting.base.domain.model.AccountantGlvcherOut;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlvcherOutHeader;
import com.anytech.anytxn.accounting.service.IllegalDataFileServiceImpl;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.common.service.system.OrganizationInfoServiceImpl;
import com.anytech.batch.job.file.common.FileHeader;
import com.anytech.batch.job.file.handler.TextLineHandler;
import com.anytech.batch.job.file.write.FileBody;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJob;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJobConfig;
import jakarta.annotation.Resource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;

@Configuration
public class IllegalGlvcherFileConfig extends SimpleWriteFileBatchJobConfig<AccountantGlvcher, AccountantGlvcherOut, Long> {
    @Resource
    private IllegalDataFileServiceImpl ilegalDataFileServiceImpl;
    @Resource
    private AnytxnFilePathConfig illGlvcherFileFormatPathConfig;
    @Resource
    private OrganizationInfoServiceImpl organizationInfoService;

    public IllegalGlvcherFileConfig(JobBuilderFactory jobs, StepBuilderFactory steps, SqlSessionFactory sqlSessionFactory,IllegalDataFileServiceImpl ilegalDataFileServiceImpl) {
        super(jobs, steps, sqlSessionFactory);
        this.ilegalDataFileServiceImpl = ilegalDataFileServiceImpl;
    }

    @Override
    public SimpleWriteFileBatchJob<AccountantGlvcher, AccountantGlvcherOut, Long> getBatchJob() {
        String illGlvcherFilePath = illGlvcherFileFormatPathConfig.getCommonPath();

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        LocalDate today = organizationInfo.getToday();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyMMdd");
        String format = dateTimeFormatter.format(today);
        HashMap<String, Object> map = new HashMap<>();
        map.put("postingDate", organizationInfo.getToday());
        map.put("processType","1");
        SimpleWriteFileBatchJob<AccountantGlvcher, AccountantGlvcherOut, Long> batchJob = SimpleWriteFileBatchJob.of();

        batchJob.name("illGlvcherFileProcess").path(illGlvcherFilePath).resolveClass(AccountantGlvcherOut.class)
                .fileName("EXCEPTIONAL.GLVCHER.OUT." + format)
                .header(new FileHeader(AccountantGlvcherOutHeader.class, (TextLineHandler) () -> {
                    AccountantGlvcherOutHeader headerDto = new AccountantGlvcherOutHeader();

                    return headerDto;
                }))
                .body(FileBody.<AccountantGlvcher, AccountantGlvcherOut, Long>of().queryId("com.anytech.anytxn.core.accountant.mapper.AccountantGlvcherSelfMapper.selectGlvcherByDateAndType")
                        .pageSize(1000)
                        .parameterValues(map)
                        .processorHandler(detailInfo ->
                                ilegalDataFileServiceImpl.buildGlvcherOut(detailInfo)));

        return batchJob;
    }
}
