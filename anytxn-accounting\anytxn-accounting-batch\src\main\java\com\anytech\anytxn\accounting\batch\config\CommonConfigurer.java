package com.anytech.anytxn.accounting.batch.config;

import jrx.anytxn.report.enums.ExportTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/9/15
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.report.common")
public class CommonConfigurer {
    /**
     * 报表模板文件路径
     */
    protected String templateFilePath;
    /**
     * 报表导出路径
     */
    protected String exportFilePath;
    /**
     * 报表文件类型
     */
    protected ExportTypeEnum exportFileType = ExportTypeEnum.PDF;

    /**
     * 压测用，报表最大行数。超过则拆分
     */
    protected Integer maxLineNum;
}
