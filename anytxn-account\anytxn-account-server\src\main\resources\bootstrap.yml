#server:
#  port: 9999
#spring:
#  # 可以放在远程配置文件中
#  cloud:
#    nacos:
#      # 配置中心
#      config:
#        prefix: accounting
#        file-extension: yaml
#        server-addr: ${SERVER_ADDR}
#        namespace: ${NAMESPACE}
#        # 扩展配置(公用db)
#        extension-configs[0]:
#          data-id: commonDb.yaml
#          refresh: true
#        extension-configs[1]:
#          data-id: ${DATA_ID}
#          refresh: true
#        extension-configs[2]:
#          data-id: cache.yaml
#          refresh: true
#        extension-configs[3]:
#          data-id: redisson.yaml
#          refresh: true
#      discovery:
#        server-addr: ${SERVER_ADDR}
#        namespace: ${NAMESPACE}
