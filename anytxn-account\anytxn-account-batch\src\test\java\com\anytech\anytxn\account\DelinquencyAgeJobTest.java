package com.anytech.anytxn.account;

import jrx.anyscheduler.batch.job.single.EnableSingleBatchJobApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 延滞滚动测试类
 * @author: zhangnan
 * @create: 2020-04-06
 **/
@EnableSingleBatchJobApplication
@EnableAccountingService
public class DelinquencyAgeJobTest {

    public static void main(String[] args) {
        SpringApplication batch = new SpringApplication(StatementProcessJobTest.class);
        //关闭web模式，防止引入web包导致批处理无法自动关闭
        batch.setWebApplicationType(WebApplicationType.NONE);
        List<String> list = new ArrayList<>();
        list.add("--task.jobName=delinquencyAgeJob");//延滞滚动
        list.add("--job.partitionKey=88-88");
        list.add("--job.runDate=" + System.currentTimeMillis());
        batch.run(list.toArray(new String[]{}));

    }
}
