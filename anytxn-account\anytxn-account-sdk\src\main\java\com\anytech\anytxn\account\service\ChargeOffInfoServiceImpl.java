package com.anytech.anytxn.account.service;

import cn.hutool.core.collection.ConcurrentHashSet;
import com.anytech.anytxn.central.service.common.AccountantRecordManager;
import com.anytech.anytxn.common.core.utils.DateHelper;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;

import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.business.base.account.enums.FinanceStatusEnum;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.transaction.mapper.GlPostedDetailMapper;
import com.anytech.anytxn.business.dao.transaction.model.GlPostedDetail;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;

import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class ChargeOffInfoServiceImpl {

    @Resource
    private IOrganizationInfoService organizationInfoService;

    @Resource
    private GlPostedDetailMapper glPostedDetailMapper;

    @Resource
    private AccountantGlamsMapper accountantGlamsMapper;

    @Resource
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;

    @Resource
    private ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper;

    @Resource
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Resource
    private AccountantRecordManager accountantRecordManager;

    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Resource
    private ITransactionCodeService transactionCodeService;
    public static final AtomicInteger ATOMIC_INTEGER = new AtomicInteger(1);

    public static final AtomicInteger ATOMIC_INTEGER_BATCHNO = new AtomicInteger(1);

    public void financeStatusChangeByBlockCode(AccountManagementInfo accountManagementInfo,AccountManagementInfo oldAccountManageInfo){


        if (shouldChange("FS1",accountManagementInfo,oldAccountManageInfo)){
            accountManagementInfo.setFinanceStatus(FinanceStatusEnum.BLOCK.getCode());
            accountManagementInfo.setFinanceStatusSetDate(LocalDate.now());
            accountManagementInfo.setPreviousFinanceStatus(oldAccountManageInfo.getFinanceStatus());
            accountManagementInfo.setPreviousFinanceStatusDate(oldAccountManageInfo.getFinanceStatusSetDate());
        }else if (shouldChange("FS2",accountManagementInfo,oldAccountManageInfo)){
            accountManagementInfo.setFinanceStatus(oldAccountManageInfo.getPreviousFinanceStatus());
            accountManagementInfo.setFinanceStatusSetDate(LocalDate.now());
            accountManagementInfo.setPreviousFinanceStatus(oldAccountManageInfo.getFinanceStatus());
            accountManagementInfo.setPreviousFinanceStatusDate(oldAccountManageInfo.getFinanceStatusSetDate());
        }
    }

    public void absGlGenerator(AccountManagementInfo accountManagementInfo,AccountManagementInfo oldAccountManageInfo){
        AccountStatisticsInfo accountStatisticsInfoInfo = accountStatisticsInfoSelfMapper.selectByIdAndType(accountManagementInfo.getAccountManagementId(),"00000");
        String curFinanceStatus = StringUtils.isEmpty(accountManagementInfo.getFinanceStatus())?"":accountManagementInfo.getFinanceStatus();
        String prevFinanceStatus = StringUtils.isEmpty(accountManagementInfo.getPreviousFinanceStatus())?"":accountManagementInfo.getPreviousFinanceStatus();
        LocalDate curFinanceStatusSetDate = accountManagementInfo.getFinanceStatusSetDate();
        String txnCode = "";
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(accountManagementInfo.getOrganizationNumber());

        if (shouldChange("GL",accountManagementInfo,oldAccountManageInfo)){
            if (!FinanceStatusEnum.BLOCK.getCode().equals(prevFinanceStatus) && FinanceStatusEnum.BLOCK.getCode().equals(curFinanceStatus)){
                txnCode = "BD013";
                buildAndInsertGl(accountManagementInfo,accountStatisticsInfoInfo,organizationInfo,txnCode);
                buildAndInsertGlams(accountManagementInfo,accountStatisticsInfoInfo,organizationInfo,txnCode);
            }else if (FinanceStatusEnum.BLOCK.getCode().equals(prevFinanceStatus) && !FinanceStatusEnum.BLOCK.getCode().equals(curFinanceStatus)){
                txnCode = "BD014";
                buildAndInsertGl(accountManagementInfo,accountStatisticsInfoInfo,organizationInfo,txnCode);
                buildAndInsertGlams(accountManagementInfo,accountStatisticsInfoInfo,organizationInfo,txnCode);
            }

        }

    }

    private Boolean shouldChange(String aim,AccountManagementInfo accountManagementInfo,AccountManagementInfo oldAccountManageInfo){
        String blockCode = StringUtils.isEmpty(accountManagementInfo.getBlockCode())?"":accountManagementInfo.getBlockCode();
        String oldBlockCode = StringUtils.isEmpty(oldAccountManageInfo.getBlockCode())?"":oldAccountManageInfo.getBlockCode();
        ConcurrentHashSet set = new ConcurrentHashSet();
        set.add("GN");set.add("GB");set.add("GD");set.add("GA");set.add("GY");set.add("GZ");
        if ("GL".equals(aim)){
            if (set.contains(blockCode.trim()) && set.contains(oldBlockCode.trim())){
                return false;
            }if (!set.contains(blockCode.trim()) && !set.contains(oldBlockCode.trim())){
                return false;
            }else {
                return true;
            }
        }else if (aim.startsWith("FS")){
            if ("FS1".equals(aim)){
                if (set.contains(blockCode.trim()) && !set.contains(oldBlockCode.trim())){
                    return true;
                }
            }else if ("FS2".equals(aim)){
                if (!set.contains(blockCode.trim()) && set.contains(oldBlockCode.trim())){
                    return true;
                }
            }
        }
        return false;
    }

    private void buildAndInsertGl(AccountManagementInfo accountManagementInfo,AccountStatisticsInfo accountStatisticsInfoInfo,OrganizationInfoResDTO organizationInfo,String txnCode){
        GlPostedDetail glDetailDTO = new GlPostedDetail();
        String currency = accountStatisticsInfoInfo.getCurrency();
        BigDecimal amount = accountStatisticsInfoInfo.getBalance();
        LocalDate glDate = organizationInfo.getNextProcessingDay();
        String postingDate = DateHelper.formatYmd(glDate);
        String defaultCurrency = currency == null ? organizationInfo.getOrganizationCurrency() : currency;

        //卡信息(卡号，卡组织)
        setCardInfo("GL",null,glDetailDTO,accountManagementInfo,organizationInfo);

        //固定信息
        glDetailDTO.setPaymentProcessingBranchCode("DINERS");
        glDetailDTO.setSourceId("2");
        glDetailDTO.setMerchantGstRegistrationNo("1");
        glDetailDTO.setRecordType("D");
        glDetailDTO.setCardType("P");
        glDetailDTO.setSettlementParty(" ");
        glDetailDTO.setCardStatus(" ");
        glDetailDTO.setCardStatusReasonCode(" ");
        glDetailDTO.setMcc(" ");
        glDetailDTO.setMerchantBankPayeeCode(" ");
        glDetailDTO.setCardAcceptorId(" ");
        glDetailDTO.setUsageCode(" ");
        glDetailDTO.setUsageCodeDesc(accountManagementInfo.getExternalReferenceNumber() == null ? " " : accountManagementInfo.getExternalReferenceNumber());
        glDetailDTO.setId(String.valueOf(sequenceIdGen.generateId(TenantUtils.getTenantId())));
        glDetailDTO.setTransactionCode(txnCode);

        glDetailDTO.setCreateTime(LocalDateTime.now());
        glDetailDTO.setUpdateTime(LocalDateTime.now());
        glDetailDTO.setAccountManagementId(accountManagementInfo.getAccountManagementId());


        int sequenceNo = ATOMIC_INTEGER.getAndIncrement();
        if (sequenceNo > 999999) {
            ATOMIC_INTEGER.set(1);
            sequenceNo = ATOMIC_INTEGER.getAndIncrement();
        }
        glDetailDTO.setSequenceNumber(sequenceNo);

        Integer batchNo = ATOMIC_INTEGER_BATCHNO.getAndIncrement();
        if (batchNo > 999) {
            ATOMIC_INTEGER_BATCHNO.set(1);
            batchNo = ATOMIC_INTEGER_BATCHNO.getAndIncrement();
        }
        glDetailDTO.setBatchNo(batchNo);
        glDetailDTO.setAcquirerId(accountManagementInfo.getBranchNumber() == null ? "DINERS" : accountManagementInfo.getBranchNumber());
        glDetailDTO.setAgeCodeBeforePostingTxn(String.valueOf(accountManagementInfo.getCycleDue() == null ? '0' : accountManagementInfo.getCycleDay()));
        glDetailDTO.setBalanceBeforePostingTxn(amount);

        //入账信息
        glDetailDTO.setPostingDate(Integer.valueOf(postingDate));
        glDetailDTO.setPostingAmount(amount);

        //币种信息
        glDetailDTO.setTransactionCurrencyCode(Integer.valueOf(currency));
        glDetailDTO.setSettlementCurrencyCode(Integer.valueOf(defaultCurrency));
        glDetailDTO.setPostingCurrencyCode(Integer.valueOf(currency));

        ImmutablePair<String, String> division = accountantRecordManager.getDivision(glDetailDTO.getCardProductCode(), glDetailDTO.getTransactionCode());
        glDetailDTO.setDivision(division.getLeft());
        glDetailDTO.setDivisionName(division.getRight());
        glPostedDetailMapper.insertSelective(glDetailDTO);

    }

    private void buildAndInsertGlams(AccountManagementInfo accountManagementInfo,AccountStatisticsInfo accountStatisticsInfoInfo,OrganizationInfoResDTO organizationInfo,String txnCode){
        AccountantGlams accountantGlams = new AccountantGlams();
        String currency = accountStatisticsInfoInfo.getCurrency();
        BigDecimal amount = accountStatisticsInfoInfo.getBalance();
        LocalDateTime glDate = organizationInfo.getToday().atTime(LocalTime.now());
        LocalDate glamsDate = organizationInfo.getNextProcessingDay();

        //卡信息(卡号，卡组织)
        setCardInfo("GLAMS",accountantGlams,null,accountManagementInfo,organizationInfo);
        //财务状态
        accountantGlams.setFinanceStatus(accountManagementInfo.getFinanceStatus());

        //固定信息
        accountantGlams.setMkUpFeeInd("N");
        accountantGlams.setInterestInd("0");
        accountantGlams.setPriceTaxFlg("N");
        accountantGlams.setAbsStatus("N");
        accountantGlams.setAssetNo(null);
        accountantGlams.setTxnSource("2");
        accountantGlams.setChannelId("2");
        accountantGlams.setSubchannelId("2");
        accountantGlams.setAcqId(accountManagementInfo.getBranchNumber() == null ? "DINERS" : accountManagementInfo.getBranchNumber());
        accountantGlams.setCurrencyRate("1");
        accountantGlams.setModuleFlag("0");
        accountantGlams.setBranchid("DINERS");
        accountantGlams.setOrigTxnAbsInd("0");
        accountantGlams.setOrderId(" ");
        accountantGlams.setProcessInd("0");
        accountantGlams.setDebitCreditIndicator("D");

        //常规信息
        accountantGlams.setUpdateBy(TransactionConstants.DEFAULT_USER);
        accountantGlams.setVersionNumber(1L);
        accountantGlams.setCreateTime(LocalDateTime.now());
        accountantGlams.setUpdateTime(LocalDateTime.now());
        accountantGlams.setPartitionKey(PartitionKeyUtils.partitionKey(accountManagementInfo.getCustomerId()));
        accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        accountantGlams.setGlobalFlowNo(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        accountantGlams.setOrganizationNumber(accountManagementInfo.getOrganizationNumber());

        //账户信息(五级分类，abs状态，网点号，账户，帐产品，rrn)
        accountantGlams.setFiveTypeIndicator(accountManagementInfo.getFiveTypeIndicator());
        accountantGlams.setAbsType(accountManagementInfo.getAbsType());
        accountantGlams.setFundId(accountManagementInfo.getNetpointNumber());
        String mid = accountManagementInfo.getAccountManagementId();
        accountantGlams.setAccountManagementId(mid);
        accountantGlams.setAcctLogo(accountManagementInfo.getProductNumber());
        accountantGlams.setRrn(accountManagementInfo.getExternalReferenceNumber() == null ? " " : accountManagementInfo.getExternalReferenceNumber());

        //交易码信息
        TransactionCodeResDTO transactionCodeDTO =
                transactionCodeService.findTransactionCode(organizationInfo.getOrganizationNumber(),
                        txnCode);
        accountantGlams.setTxnCode(txnCode);
        accountantGlams.setTxnCodeOrig(txnCode);
        accountantGlams.setTxnDescription(transactionCodeDTO.getDescription());

        //入账信息
        accountantGlams.setPostingAmt(amount);
        accountantGlams.setPostingDate(glamsDate);
        accountantGlams.setPostingCurrencyCode(currency);
        //交易信息
        accountantGlams.setTxnAmt(amount);
        accountantGlams.setTxnCurrency(currency);
        accountantGlams.setTxnDate(glDate);
        //清算信息
        accountantGlams.setSettleAmt(amount);
        accountantGlams.setSettleCurrency(currency);

        accountantGlamsMapper.insertSelective(accountantGlams);
        //txnRecordedAccountCacheService.saveGlAms(accountantGlams);
    }

    private void setCardInfo(String sign, AccountantGlams accountantGlams, GlPostedDetail glDetailDTO, AccountManagementInfo accountManagementInfo, OrganizationInfoResDTO organizationInfo) {
        String cardNum = cardFounder(accountManagementInfo, organizationInfo);
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(cardNum);
        String cardProductNumber = cardAuthorizationInfo == null
                ? accountManagementInfo.getProductNumber() : cardAuthorizationInfo.getProductNumber();
        ParmCardProductInfo parmCardProductInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(organizationInfo.getOrganizationNumber(), cardProductNumber);
        if ("GL".equals(sign)) {
            glDetailDTO.setCardProductCode(cardProductNumber);
            glDetailDTO.setCardNumber(cardNum);

            if (!ObjectUtils.isEmpty(parmCardProductInfo)) {
                glDetailDTO.setServicingBranch(parmCardProductInfo.getScheme());
            }else {
                glDetailDTO.setServicingBranch("NOSCHE");
            }
        } else if ("GLAMS".equals(sign)) {
            accountantGlams.setCrdProductNumber(cardProductNumber);
            accountantGlams.setCrdNumber(cardNum);

            if (!ObjectUtils.isEmpty(parmCardProductInfo)) {
                accountantGlams.setScheme(parmCardProductInfo.getScheme());
            }else {
                accountantGlams.setScheme("NOSCHE");
            }
        }
    }

    private String cardFounder(AccountManagementInfo accountManagementInfo,OrganizationInfoResDTO organizationInfo){
        String liability = accountManagementInfo.getLiability();
        String customerId = accountManagementInfo.getCustomerId();
        CardAuthorizationInfo cardAuthorizationInfo=null;
        if ("C".equals(liability)){
            customerId = accountManagementInfo.getCorporateCustomerId();
        }
        List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByCustomerIdDesc(customerId);
        List<ParmCardProductInfo> parmCardProductInfos = acctPdtFindCrdPdt(accountManagementInfo,organizationInfo);
        if (!CollectionUtils.isEmpty(parmCardProductInfos)){
            for (ParmCardProductInfo parmCardProductInfo : parmCardProductInfos) {
                cardAuthorizationInfo = cardAuthorizationInfos.stream().filter(x->("P".equals(x.getRelationshipIndicator()))
                                && parmCardProductInfo.getProductNumber().equals(x.getProductNumber()))
                        .collect(Collectors.toList())
                        .stream().findFirst().orElse(null);
            }
        }
        if (ObjectUtils.isEmpty(cardAuthorizationInfo)){
            return "";
        }else {
            return cardAuthorizationInfo.getCardNumber();
        }
    }

    private List<ParmCardProductInfo> acctPdtFindCrdPdt(AccountManagementInfo accountManagementInfo, OrganizationInfoResDTO organizationInfo){
        String acctPdt = accountManagementInfo.getProductNumber();
        String orgNum = organizationInfo.getOrganizationNumber();
        List<ParmCardProductInfo> parmProductInfos = parmCardProductInfoSelfMapper.selectByOrgAndAccountProductNum(orgNum,acctPdt);

        return parmProductInfos;
    }
}
