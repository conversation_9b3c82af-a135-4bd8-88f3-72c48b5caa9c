package com.anytech.anytxn.account;

import com.anytech.anytxn.transaction.EnableTransactionService;
import jrx.anyscheduler.batch.job.single.EnableSingleBatchJobApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.util.ArrayList;


/**
 * 免息计息分离，清理交易账户上已结清利息。
 */
@EnableSingleBatchJobApplication
@EnableTransactionService
public class StatementClearJobTest {

	public static void main(String[] args) {
		ArrayList<String> list = new ArrayList<>();
		list.add("--t.job=autoPaymentJob");
		list.add("--job.partitionKey=0-9999");
		list.add("--job.organizationNumber=0002");
		list.add("--data-id=bizDb-2.yaml");
		list.add("--debug");
		new SpringApplicationBuilder(AnytxnAccountingBatchApplication.class)
				.web(WebApplicationType.NONE)
				.run(list.toArray(new String[]{}));
	}
}
