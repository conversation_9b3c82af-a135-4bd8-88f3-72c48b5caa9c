package com.anytech.anytxn.accounting.batch.job.glamsfromupi.dto;

import lombok.Getter;
import lombok.Setter;
import org.springframework.core.annotation.Order;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class DetailC602DZ004 implements UPIMessage {
    private static final long serialVersionUID = 942082961184839332L;
    /**
     * 交易类型
     */
    @Order(0)
    @Size(max=36)
    private String transactionType;

    /**
     * 机构角色
     */
    @Order(1)
    @Size(max=13)
    private String insRole;

    /**
     * 清算笔数
     */
    @Order(2)
    @Size(max=17)
    private String settlementCount;

    /**
     * 附加费
     */
    @Order(3)
    @Size(max=23)
    private String additionalFee;

}
