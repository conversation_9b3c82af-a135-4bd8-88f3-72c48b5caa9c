package com.anytech.anytxn.accounting.batch.job.report.step.installmonth.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 根据分期产品ID+币种维度
 * <AUTHOR>
 * @Date 2020/9/11 11:46
 */
@Getter
@Setter
public class OrderPlanSub {
    private List<OrderPlanDetail> detailList;
    /**
     * 分期产品id
     */
    private String productId;
    /**
     * 币种
     */
    private Integer currencyCode;
    /**
     * 条数
     */
    private Integer installCount;
    /**
     * 本金总额
     */
    private BigDecimal installAmountTotal;
    /**
     * 费用总额
     */
    private BigDecimal installFeeTotal;
    /**
     * 是否最后一组数据
     */
    private Boolean end = false;
}
