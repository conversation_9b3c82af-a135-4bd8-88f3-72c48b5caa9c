package com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsSumDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvatbalDTO;

import com.anytech.anytxn.accounting.base.domain.bo.GenerateVoucherBO;
import com.anytech.anytxn.accounting.mapper.AccountantGlvatbalMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsSumMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlamsSum;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvatbal;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherAbs;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 插入会计传票
 * <AUTHOR>
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = {Exception.class,RuntimeException.class})
public class AmsToVoucherWriter implements ItemWriter<GenerateVoucherBO> {

    Logger logger = LoggerFactory.getLogger(AmsToVoucherWriter.class);

    @Resource
    private NamedParameterJdbcTemplate bizJdbcTemplate;


    private AccountantGlvatbalMapper amsGlvatbalMapper;

    private AccountantGlamsSumMapper amsGlamsSumMapper;

    private AccountantGlamsMapper tAmsGlamsMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Value("${anytxn.batch.stmt.max-insert:500}")
    private int maxInsert;

    @Override
    public void write(Chunk<? extends GenerateVoucherBO> chunk) throws Exception {
        List<? extends GenerateVoucherBO> items = chunk.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        int size = items.size();

        int littleSize  = size / 100;

        List<AccountantGlamsSum> accountantGlamsSumArrayList = new ArrayList<>(size);

        List<AccountantGlvcher> accountantGlvcherArrayList = new ArrayList<>(size);

        List<TAmsGlamsSumDTO> oldAmsGlamsSums = new ArrayList<>(size);

        List<TAmsGlamsDTO> upddateGlAms = new ArrayList<>(size);



        List<AccountantGlvcherAbs> accountantGlvcherAbsArrayList = new ArrayList<>(littleSize);

        List<AccountantGlvatbal> accountantGlvatbalArrayList = new ArrayList<>(littleSize);

        List<TAmsGlvatbalDTO> oldTamsGlVatBalList = new ArrayList<>(littleSize);




        items.forEach(o -> {

            // 生成会计流水汇总
            if (CollectionUtils.isNotEmpty(o.getNewAmsGlamsSums())) {
                List<AccountantGlamsSum> tAmsGlamsSums = BeanMapping.copyList(o.getNewAmsGlamsSums(), AccountantGlamsSum.class).stream().peek(x -> {
                    // 生成新的id
                    if (StringUtils.isEmpty(x.getId())) {
                        x.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                    }
                }).collect(Collectors.toList());

                accountantGlamsSumArrayList.addAll(tAmsGlamsSums);


                /*if (tAmsGlamsSums.size() > maxInsert) {
                    List<List<AccountantGlamsSum>> partition = Lists.partition(tAmsGlamsSums, maxInsert);
                    partition.forEach(amsGlamsSumSelfMapper:insertBatch);
                } else {
                    amsGlamsSumSelfMapper.insertBatch(tAmsGlamsSums);
                }*/
            }

            // 生成会计传票
            if (CollectionUtils.isNotEmpty(o.getTAmsGlvchers())) {
                List<AccountantGlvcher> tAmsGlvchers = BeanMapping.copyList(o.getTAmsGlvchers(), AccountantGlvcher.class).stream().peek(x -> {
                    // 生成新的id
                    if (StringUtils.isEmpty(x.getId())) {
                        x.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                    }
                }).collect(Collectors.toList());

                accountantGlvcherArrayList.addAll(tAmsGlvchers);
                /*if (tAmsGlvchers.size() > maxInsert) {
                    List<List<AccountantGlvcher>> partition = Lists.partition(tAmsGlvchers, maxInsert);
                    partition.forEach(amsGlvcherSelfMapper:insertBatch);
                } else {
                    amsGlvcherSelfMapper.insertBatch(tAmsGlvchers);
                }*/
            }

            // 生成abs会计传票
            if (CollectionUtils.isNotEmpty(o.getAmsGlvcherAbs())) {
                List<AccountantGlvcherAbs> tAmsGlvcherAbs = BeanMapping.copyList(o.getAmsGlvcherAbs(), AccountantGlvcherAbs.class).stream().peek(x -> {
                    // 生成新的id
                    if (StringUtils.isEmpty(x.getId())) {
                        x.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                    }
                }).collect(Collectors.toList());

                accountantGlvcherAbsArrayList.addAll(tAmsGlvcherAbs);
               /* if (tAmsGlvcherAbs.size() > maxInsert) {
                    List<List<AccountantGlvcherAbs>> partition = Lists.partition(tAmsGlvcherAbs, maxInsert);
                    partition.forEach(amsGlvcherAbsSelfMapper:insertBatch);
                } else {
                    amsGlvcherAbsSelfMapper.insertBatch(tAmsGlvcherAbs);
                }*/
            }

            // 生成新增值税分户余额
            if (CollectionUtils.isNotEmpty(o.getNewTamsGlVatBals())) {
                List<AccountantGlvatbal> tAmsGlvatbals = BeanMapping.copyList(o.getNewTamsGlVatBals(), AccountantGlvatbal.class).stream().peek(x -> {
                    if (StringUtils.isEmpty(x.getId())) {
                        x.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                    }
                }).collect(Collectors.toList());

                accountantGlvatbalArrayList.addAll(tAmsGlvatbals);
                /*if (tAmsGlvatbals.size() > maxInsert) {
                    List<List<AccountantGlvatbal>> partition = Lists.partition(tAmsGlvatbals, maxInsert);
                    partition.forEach(amsGlvatbalSelfMapper:insertBatch);
                } else {
                    amsGlvatbalSelfMapper.insertBatch(tAmsGlvatbals);
                }*/
            }

            // 更新旧增值税分户余额
            if (CollectionUtils.isNotEmpty(o.getOldTamsGlVatBals())) {
                oldTamsGlVatBalList.addAll(o.getOldTamsGlVatBals());
                //.forEach(amsGlvatbalMapper:updateByPrimaryKeySelective);
            }

            // 更新旧流水汇总
            if (CollectionUtils.isNotEmpty(o.getOldAmsGlamsSums())) {
                oldAmsGlamsSums.addAll(o.getOldAmsGlamsSums());
                //forEach(amsGlamsSumMapper:updateByPrimaryKeySelective);
            }

            // 更新会计流水
            if (CollectionUtils.isNotEmpty(o.getUpddateGlAms())) {
                upddateGlAms.addAll(o.getUpddateGlAms());
                //forEach(tAmsGlamsMapper:updateByPrimaryKeySelective);
            }

        });

        batchUpddateGlAms(upddateGlAms);
        //batchUpdateoldAmsGlamsSums(oldAmsGlamsSums);


        //用jdbc batch 更新数据库
        batchInsertAccountantGlamsSumArrayList(accountantGlamsSumArrayList);

        batchInsertAccountantGlvcherArrayList(accountantGlvcherArrayList);

        batchInsertAccountantGlvcherAbsArrayList(accountantGlvcherAbsArrayList);

        batchInserAccountantGlvatbalArrayList(accountantGlvatbalArrayList);


        clearList(accountantGlamsSumArrayList,accountantGlvcherArrayList,
                accountantGlvcherAbsArrayList,accountantGlvatbalArrayList,
                oldTamsGlVatBalList,oldAmsGlamsSums,upddateGlAms
        );
    }


    private void batchUpdateoldAmsGlamsSums(List<TAmsGlamsSumDTO> oldAmsGlamsSums) {
        if(CollectionUtils.isEmpty(oldAmsGlamsSums)){
            return;
        }

        StringBuilder sql = new StringBuilder();
        sql.append(
                "update  "
        );

        bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(oldAmsGlamsSums.toArray()));

    }






    private void batchUpddateGlAms(List<TAmsGlamsDTO> upddateGlAms) {
        if(CollectionUtils.isEmpty(upddateGlAms)){
            return;
        }

        StringBuilder sql = new StringBuilder();
        sql.append(
                "update ACCOUNTANT_GLAMS set PROCESS_IND = :processInd,UPDATE_TIME = :updateTime ," +
                " VERSION_NUMBER  = :versionNumber, UPDATE_BY = :updateBy, RULE_ID = :ruleId, SCHEME = :scheme  where ID =  :id "
        );

        bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(upddateGlAms.toArray()));


    }


    private void batchInserAccountantGlvatbalArrayList(List<AccountantGlvatbal> accountantGlvatbalArrayList) {

        if(CollectionUtils.isEmpty(accountantGlvatbalArrayList)){
            return;
        }

        StringBuilder sql = new StringBuilder();
        sql.append(
                "insert into ACCOUNTANT_GLVATBAL (ID, ORGANIZATION_NUMBER, " +
                "BRANCHID, ACCOUNT_MANAGEMENT_ID, " +
                "GL_ACCT, DBAL, CBAL, " +
                "CREATE_TIME, UPDATE_TIME, " +
                "UPDATE_BY, VERSION_NUMBER, " +
                "CURR_CODE, UPDATE_DATE, " +
                "DR_CURR, CR_CURR) VALUES ( " +
                ":id, :organizationNumber, " +
                ":branchid, :accountManagementId, " +
                ":createTime, :updateTime, " +
                ":updateBy, :versionNumber, " +
                ":currCode, :updateDate, " +
                ":drCurr, :crCurr ) "
        );

        bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(accountantGlvatbalArrayList.toArray()));

    }


    private void batchInsertAccountantGlvcherAbsArrayList(List<AccountantGlvcherAbs> accountantGlvcherAbsArrayList) {

        if(CollectionUtils.isEmpty(accountantGlvcherAbsArrayList)){
            return;
        }


        StringBuilder sql = new StringBuilder();
        sql.append( 
                "insert into ACCOUNTANT_GLVCHER_ABS (ID, ORGANIZATION_NUMBER, " +
                "BRANCHID, ACCOUNT_MANAGEMENT_ID, " +
                "ACCT_LOGO, GLOBAL_FLOW_NO, " +
                "MODULE_FLAG, VP_TXN_CODE, " +
                "CURR_CODE, GL_ACCT, " +
                "DRCR, GL_AMOUNT, GL_CNT, POSTING_DATE, " +
                "PROCESS_TYPE, ORDER_ID, " +
                "ASSET_NO, ABS_STATUS, " +
                "TXN_CODE_ORIG, CREATE_TIME, " +
                "UPDATE_TIME, UPDATE_BY, " +
                "VERSION_NUMBER, CHANNEL_ID, " +
                "SUBCHANNEL_ID, FUND_ID, " +
                "PLATFORM_ID, MERCHANT_NUMBER, " +
                "FIVE_TYPE_INDICATOR, ABS_TYPE) VALUES ( " +
                ":id, :organizationNumber," +
                ":branchid, :accountManagementId," +
                ":acctLogo, :globalFlowNo," +
                ":moduleFlag, :vpTxnCode," +
                ":currCode, :glAcct," +
                ":drcr, :glAmount, :glCnt, :postingDate," +
                ":processType, :orderId," +
                ":assetNo, :absStatus," +
                ":txnCodeOrig, :createTime," +
                ":updateTime, :updateBy," +
                ":versionNumber, :channelId," +
                ":subchannelId, :fundId," +
                ":platformId, :merchantNumber," +
                ":fiveTypeIndicator, :absType ) "
        
        );

        bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(accountantGlvcherAbsArrayList.toArray()));
    }


    private void batchInsertAccountantGlvcherArrayList(List<AccountantGlvcher> accountantGlvcherArrayList) {

        if(CollectionUtils.isEmpty(accountantGlvcherArrayList)){
            return;
        }

        StringBuilder sql = new StringBuilder();
        sql.append(
                "insert into ACCOUNTANT_GLVCHER (ID, ORGANIZATION_NUMBER, " +
                "BRANCHID, ACCOUNT_MANAGEMENT_ID, " +
                "ACCT_LOGO, GLOBAL_FLOW_NO, " +
                "MODULE_FLAG, VP_TXN_CODE, " +
                "CURR_CODE, GL_ACCT, GL_CNT, " +
                "DRCR, GL_AMOUNT, POSTING_DATE, " +
                "PROCESS_TYPE, ORDER_ID, " +
                "ASSET_NO, ABS_STATUS, " +
                "TXN_CODE_ORIG, CREATE_TIME, " +
                "UPDATE_TIME, UPDATE_BY, " +
                "VERSION_NUMBER, CHANNEL_ID, " +
                "SUBCHANNEL_ID, FUND_ID, " +
                "PLATFORM_ID, MERCHANT_NUMBER, FIVE_TYPE_INDICATOR, ABS_TYPE,PARTITION_KEY, " +
                "CRD_NUMBER, CRD_PRODUCT_NUMBER, CRD_TYPE, TXN_AMT, TXN_CURRENCY, TXN_DATE, TXN_SOURCE,TXN_DESCRIPTION,CURRENCY_RATE, " +
                "RRN,ACQ_ID,VATA_COUPLE_INDICATOR,VA_NUMBER )  VALUES (  " +
                ":id, :organizationNumber ," +
                ":branchid, :accountManagementId , " +
                ":acctLogo, :globalFlowNo, " +
                ":moduleFlag, :vpTxnCode, " +
                ":currCode, :glAcct, :glCnt, " +
                ":drcr, :glAmount, :postingDate, " +
                ":processType, :orderId, " +
                ":assetNo, :absStatus, " +
                ":txnCodeOrig, :createTime, " +
                ":updateTime, :updateBy, " +
                ":versionNumber, :channelId, " +
                ":subchannelId, :fundId, " +
                ":platformId, :merchantNumber, " +
                ":fiveTypeIndicator, :absType, :partitionKey," +
                ":crdNumber,:crdProductNumber, :crdType," +
                ":txnAmt, :txnCurrency, :txnDate," +
                ":txnSource,:txnDescription, :currencyRate," +
                ":rrn, :acqId, :vataCoupleIndicator, :vaNumber )"
        );

        bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(accountantGlvcherArrayList.toArray()));
    }




    private void batchInsertAccountantGlamsSumArrayList(List<AccountantGlamsSum> accountantGlamsSumArrayList) {

        if(CollectionUtils.isEmpty(accountantGlamsSumArrayList)){
            return;
        }

        StringBuilder sql = new StringBuilder();
        sql.append(
                "insert into ACCOUNTANT_GLAMS_SUM (" +
                "ID, ORGANIZATION_NUMBER, " +
                "BRANCHID, ACCOUNT_MANAGEMENT_ID, " +
                "ACCT_LOGO, GLOBAL_FLOW_NO, " +
                "MODULE_FLAG, TXN_CODE, " +
                "POSTING_DATE, POSTING_CURRENCY_CODE, " +
                "POSTING_AMT, POSTING_CNT ,FINANCE_STATUS, " +
                "INTEREST_IND, PRICE_TAX_FLG, " +
                "TXN_CODE_ORIG, BAL_TYPE, " +
                "TXN_IND, BAL_PROCESS_IND, " +
                "ABS_STATUS, ASSET_NO, " +
                "ORIG_TXN_ABS_IND, ORDER_ID, " +
                "PROCESS_IND, CREATE_TIME, " +
                "UPDATE_TIME, UPDATE_BY, " +
                "VERSION_NUMBER, CHANNEL_ID, " +
                "SUBCHANNEL_ID, FUND_ID, " +
                "PLATFORM_ID, MERCHANT_NUMBER," +
                "TRANSACTION_ATTRIBUTE,DEBIT_CREDIT_INDICATOR," +
                "AMORTIZE_IND,TRANSACTION_TYPE_CODE,RULE_ID,NPAS_FIRST," +
                "FIVE_TYPE_INDICATOR, ABS_TYPE, " +
                "CRD_NUMBER, CRD_PRODUCT_NUMBER, CRD_TYPE, TXN_AMT, TXN_CURRENCY, TXN_DATE, TXN_SOURCE,TXN_DESCRIPTION,CURRENCY_RATE, " +
                "RRN,ACQ_ID,MK_UP_FEE_IND,VATA_COUPLE_INDICATOR,VA_NUMBER ) VALUES (  " +
                        ":id, :organizationNumber, " +
                        ":branchid, :accountManagementId, " +
                        ":acctLogo, :globalFlowNo, " +
                        ":moduleFlag,  :txnCode, " +
                        ":postingDate, " +
                        ":postingCurrencyCode, " +
                        ":postingAmt, " +
                        ":postingCnt," +
                        ":financeStatus, " +
                        ":interestInd, " +
                        ":priceTaxFlg, " +
                        ":txnCodeOrig, " +
                        ":balType, " +
                        ":txnInd," +
                        ":balProcessInd, " +
                        ":absStatus, " +
                        ":assetNo, " +
                        ":origTxnAbsInd, " +
                        ":orderId, " +
                        ":processInd, " +
                        ":createTime, " +
                        ":updateTime, " +
                        ":updateBy, " +
                        ":versionNumber, " +
                        ":channelId, " +
                        ":subchannelId, " +
                        ":fundId, " +
                        ":platformId," +
                        ":merchantNumber," +
                        ":transactionAttribute," +
                        ":debitCreditIndicator," +
                        ":amortizeInd," +
                        ":transactionTypeCode," +
                        ":ruleId," +
                        ":npasFirst," +
                        ":fiveTypeIndicator, " +
                        ":absType," +
                        ":crdNumber," +
                        ":crdProductNumber, " +
                        ":crdType," +
                        ":txnAmt," +
                        ":txnCurrency," +
                        ":txnDate," +
                        ":txnSource," +
                        ":txnDescription, " +
                        ":currencyRate," +
                        ":rrn, " +
                        ":acqId, :mkUpFeeInd, :vataCoupleIndicator, :vaNumber )"
        );

        bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(accountantGlamsSumArrayList.toArray()));

    }




    private void clearList(List<?> accountantGlamsSumArrayList,
                           List<?> accountantGlvcherArrayList,
                           List<?> accountantGlvcherAbsArrayList,
                           List<?> accountantGlvatbalArrayList,
                           List<?> oldTamsGlVatBalList,
                           List<?> oldAmsGlamsSums,
                           List<?> upddateGlAms) {


        accountantGlamsSumArrayList.clear();
        accountantGlvcherArrayList.clear();
        accountantGlvcherAbsArrayList.clear();
        accountantGlvatbalArrayList.clear();
        oldTamsGlVatBalList.clear();
        oldAmsGlamsSums.clear();
        upddateGlAms.clear();


    }

}
