package com.anytech.anytxn.accounting.batch.job.report.step.outcustdetail.dto;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.outcustdetail.model.OutCustomerDetail;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/9/15 14:16
 */
@Getter
@Setter
public class CrossTransactionDetail {

    private String bankNo;

    private String source;

    private String transArea;
    //客户ID
    private String customerId;
    //客户名称
    private String customerName;
    //管理账户ID
    private String accountManagementId;
    //账户产品ID
    private String productId;
    //币种
    private String currencyCode;
    //入账日期
    private String postDate;
    //科目号
    private String subjectNumber;
    //科目名称
    private String subjectName;
    //借贷方向
    private String debitCreditIndcator;
    //发生额
    private BigDecimal createAmount;

    public static List<CrossTransactionDetail> resetReportData(List<OutCustomerDetail> data, TimeTypeEnum timeTypeEnum){

        return data.parallelStream().map(x-> {
            CrossTransactionDetail detail = new CrossTransactionDetail();
            detail.setBankNo(x.getBranchId());
            detail.setSource(x.getSource());
            detail.setTransArea(StringUtils.isNotBlank(x.getCountryCode()) ? x.getCountryCode() : "");
            detail.setCustomerId(x.getCustomerId());
            detail.setCustomerName(x.getCustomerName());
            detail.setAccountManagementId(x.getManagementId());
            detail.setProductId(x.getProductNumber());
            detail.setCurrencyCode(x.getCurrCode());
            detail.setPostDate(TimeUtils.dateToStr(x.getPostDate(), timeTypeEnum));
            detail.setSubjectNumber(x.getGlAcct());
            detail.setSubjectName(x.getGlAcctName());
            detail.setDebitCreditIndcator(x.getDrcr());
            detail.setCreateAmount(x.getGlAmount());

            return detail;
        }).sorted(Comparator.comparing(x-> x.getCustomerId() + x.getPostDate())).collect(Collectors.toList());
    }
}
