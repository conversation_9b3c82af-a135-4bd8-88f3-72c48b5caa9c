package com.anytech.anytxn.account.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 自动扣款文件
 *
 * <AUTHOR>
 * @date 2021/1/13
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.account.auto.payment")
public class AutoPaymentFileConfigurer {
    private String commonFile;

    private String shardingFile;

    private String commonBackFile;

    private String shardingBackFile;

    private String fileSplit;

    private String fileName;
}
