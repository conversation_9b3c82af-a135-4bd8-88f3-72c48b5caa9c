package com.anytech.anytxn.accounting.base.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 模块标识
 * @date 2020-02-16 14:06
 **/
public enum OrigTxnAbsIndEnum {
    /**
     * 0：原始交易非借贷
     * 1：原始交易为ABS借
     * 2：原始交易为ABS贷
     * 3：原始交易为非ABS借
     * 4：原始交易为非ABS贷
     */
    CORE("0", "原始交易非借贷"),
    CHARGE_OFF("1", "原始交易为ABS借"),
    TAX("2", "原始交易为ABS贷"),
    YEAR_END("3", "原始交易为非ABS借"),
    INST("4", "原始交易为非ABS贷"),
    ;
    private String code;
    private String desc;


    OrigTxnAbsIndEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String getValue(String code) {
        if (code == null) {
            return null;
        }
        OrigTxnAbsIndEnum[] var2 = values();
        int var3 = var2.length;
        for (int var4 = 0; var4 < var3; ++var4) {
            OrigTxnAbsIndEnum moduleFlagEnum = var2[var4];
            if (Objects.equals(code, moduleFlagEnum.getCode())) {
                return moduleFlagEnum.getDesc();
            }
        }
        return null;
    }
}
