package com.anytech.anytxn.account.base.constants;

/**
 * 入账方式
 *
 * <AUTHOR>
 * @date 2019-08-28
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 */
public enum PostMethod {
    /**
     * 入账方式
     * 0=实时入账
     * 1=批量入账
     */
    REAL_TIME("0", "实时入账"),
    BATCH("1", "批量入账")
    ;
    private String code;
    private String desc;


    PostMethod(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
