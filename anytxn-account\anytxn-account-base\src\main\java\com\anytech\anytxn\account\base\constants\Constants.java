package com.anytech.anytxn.account.base.constants;


import java.time.LocalDate;

/**
 * 常量类
 *
 * <AUTHOR>
 * @date 2018/8/27.
 *
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 */
public final class Constants {

    /**
     * 参数表数据存在
     */
    public static final String DEFAULT_UPDATE_BY = "SYSTEM";

    public static final String PARAMETER_CODE_TRUE = "200";

    public static final String PARAMETER_CODE_FALSE = "500";

    public static final String PARAMETER_CODE = "code";

    public static final String PARAMETER_SUCCESS = "SUCCESS";

    public static final String PARAMETER_MESSAGE = "message";

    public static final String PARAMETER_FALSE = "FALSE";
    /*=========================start=====================================*/

    /**
     * 产品参数表中的卡片封锁码参数表ID
     */
    public static final String PLASTIC_BLOCK_CODE_TABLE_ID =
            "plasticBlockCodeTableId";
    /*=========================end=======================================*/

    /*=========================start=====================================*/

    //读取参数表中信息的常量设置
    /**
     * 产品信息表中的利率参数表ID
     */
    public static final String INTEREST_TABLE_ID = "interestTableId";
    /**
     * 产品信息表中的交易费用表ID
     */
    public static final String TRANSACTION_FEE_TABLE_ID = "transactionFeeTableId";
    /**
     * 产品信息表中的最低还款比例参数表ID
     */
    public static final String MINIMUM_PAYMENT_TABLE_ID = "minimumPaymentTableId";
    /**
     * 产品参数表(parm_product_info)中的账单处理参数表id（statement_processing_table_id）
     */
    public static final String STATEMENT_PROCESSING_TABLE_ID =
            "statementProcessingTableId";

    /**
     * 容差
     */
    public static final String PAYMENT_VARIANCE = "paymentVariance";
    /**
     * 最小值
     */
    public static final String MINIMUM_PAYMENT = "minimumPayment";
    /**
     * 最小还款比例
     */
    public static final String MINIMUM_PAYMENT_PERCENT = "minimumPaymentPercentage";
    /**
     * 还款日计算
     */
    public static final String DUE_DAYS = "dueDays";
    /**
     * 滞纳金收取日
     */
    public static final String LATE_DAYS = "lateDays";
    /**
     * 利息宽限日
     */
    public static final String GRACE_DAYS = "graceDays";

    /**
     * 产品参数中的消费利率参数表ID
     */
    public static final String RETAIL_INTEREST_TABLE_ID = "retailInterestTableId";
    /**
     * 产品参数中的现金利率参数表ID
     */
    public static final String CASH_INTEREST_TABLE_ID = "cashInterestTableId";

    /**
     * 交易码记录中的借贷记属性
     */
    public static final String DEBIT_CREDIT_INDICATOR = "debitCreditIndicator";

    /**
     * 交易码记录中的还款还原交易码
     */
    public static final String PAYMENT_REVERSAL_TXN_CODE = "paymentReversalTxnCode";

    /**
     * 交易码记录中的交易属性
     */
    public static final String TRANSACTION_ATTRIBUTE = "transactionAttribute";

    /**
     * 交易类型参数表的交易类型
     */
    public static final String TRANSACTION_TYPE_CODE = "transactionTypeCode";
    /**
     * 交易类型参数表的创建方式
     */
    public static final String CREATE_METHOD = "createMethod";
    /**
     * 机构参数表中的上一系统日期
     */
    public static final String LAST_PROCESSING_DAY = "lastProcessingDay";
    /**
     * 机构参数表中的下一系统日期
     */
    public static final String NEXT_PROCESSING_DAY = "nextProcessingDay";
    /**
     * 授权处理参数表中的authorization_remain_days
     */
    public static final String AUTHORIZATION_REMAIN_DAYS = "authorizationRemainDays";
    /**
     * 费用参数中费用产生利息标识
     */
    public static final String GENERATE_INTEREST_OPTION = "generateInterestOption";
    /**
     * 利率参数表（parm_interest）中的起息日选项
     */
    public static final String START_DATE_OPTION = "startDateOption";
    /**
     * 利率参数中利息产生利息标识
     */
    public static final String INTEREST_ON_INTEREST_OPTION =
            "interestOnInterestOption";
    /**
     * 利率参数表（parm_interest）中的基准利率
     */
    public static final String BASE_RATE = "baseRate";
    public static final String ACCOUNT_BLOCK_CODE_TABLE_ID =
            "accountBlockCodeTableId";
    /**
     * 利率参数（parm_interest）中计息标志
     */
    public static final String ACCURAL_OPTION = "accuralOption";
    /**
     * 利率参数（parm_interest）中免息标志
     */
    public static final String GRACE_OPTION = "graceOption";
    /**
     * 利率参数（parm_interest）中免收标志
     */
    public static final String WAVIE_OPTION = "wavieOption";
    /**
     * 利率参数（parm_interest）中结息交易码
     */
    public static final String INTEREST_BILLING_TXN_CODE = "interestBillingTxnCode";
    /**
     * 账单处理参数表(parm_statement_process)中的全额还款容差
     */
    public static final String PAYOFF_VARIANCE = "payoffVariance";
    /**
     * 账单处理参数表(parm_statement_process)中的利息计算方式
     */
    public static final String INTEREST_CALCULATE_OPTION = "interestCalculateOption";

    public static final String ON_STATEMENT_INDICATOR = "onStatementIndicator";

    /**
     * 机构参数表中的上次累计日期
     */
    public static final String LAST_ACCRUED_THRU_DAY = "lastAccruedThruDay";
    /**
     * 机构参数表中的当前累计日期
     */
    public static final String ACCRUED_THRU_DAY = "accruedThruDay";

    /**
     * 交易码内部配置表中的交易码
     */
    public static final String TRANSACTION_CODE = "transactionCode";

    /**
     * 汇率参数表中的汇率
     */
    public static final String RATE_VALUE = "rateValue";
    /**
     * 汇率参数表中的汇率小数位
     */
    public static final String EXPONENT = "exponent";
    /*=========================end=======================================*/

    public static final String DELINQUENT_CONTROL_TABLE_ID =
            "delinquentControlTableId";
    public static final String BLOCK_CODE = "blockCode";
    public static final LocalDate INITIAL_DATE = null;
    public static final LocalDate INITIAL_LOCALDATE = LocalDate.of(1, 1, 1);
    /**
     * 入账控制标志
     */
    public static final String POSTING_INDICATOR = "postingIndicator";
    /**
     * 封锁码优先级
     */
    public static final String BLOCK_CODE_PRIORITY = "priority";
    /**
     * 借记/贷记还原类交易
     */
    public static final String DEBIT_TRANSACTION = "debitTransaction";
    /**
     * 贷记/借记还原类交易
     */
    public static final String CREDIT_TRANSACTION = "creditTransaction";
    /**
     * 还款类交易
     */
    public static final String REPAYMENT_TRANSACTION = "repaymentTransaction";
    /**
     * 还款顺序表ID
     */
    public static final String REPAYMENT_SEQUENCE_TABLE_ID =
            "paymentSequenceTableId";

    /**
     * tableId
     */

    public static final String TABLE_ID = "tableId";
    /**
     * 还款还原类交易
     */
    public static final String REPAYMENT_REDUCTION_TRANSACTION =
            "repaymentReductionTransaction";
    /**
     * 争议类交易
     */
    public static final String DISPUTE_TRANSACTION = "disputeTransaction";
    /**
     * MEMO类交易
     */
    public static final String MEMO_TRANSACTION = "memoTransaction";
    /**
     * 溢缴款交易类型
     */
    public static final String TRANSACTION_TYPE_OVERFLOW = "DCS0O";

    public static final String POST_METHOD_REAL_TIME = "0";

    /**
     * 汇总交易类型
     */
    public static final String TRANSACTION_TYPE_TOTAL = "00000";

    /**
     * 初始化时间
     */
    public static final String INIT_TIME_VALUE = "0000-00-00";
    /**
     * 延滞标志
     */
    public static final int CYCLE_DUE_FLAG = 4;

    /**
     * 非正常财务状态
     */
    public static final String NON_NORMAL_FINANCE_STATUS = "0";
    /**
     * 延滞滚动标志
     */
    public static final String DELINQUENCY_AGING_IND = "delinquencyAgingInd";
    /**
     * redis 项目前缀
     */
    public static final String TXN = "txn";
    /*=========================end=======================================*/
    /**
     * redis 号码前缀
     */
    public static final String NUMBER = "number";
    /**
     * 机器码
     */
    public static final String MACHINE_CODE = "machine_code";
    /**
     * 费用参数表id的状态
     */
    public static final String STATUS = "status";
    /**
     * 产品表中 取现手续费参数表ID
     */
    public static final String CASH_FEE_TABLE_ID = "cashFeeTableId";
    /**
     * 费用收取标志
     */
    public static final String CHARGE_FLAG_T = "chargeFlag";

    public static final String TODAY = "today";

    public static final String DATA = "data";

    public static final String DELINQ_PYMT_SEQUENCE_TABLE_ID =
            "delinqPymtSequenceTableId";

    public static final String NORMAL_PYMT_SEQUENCE_TABLE_ID =
            "normalPymtSequenceTableId";
    /*=========================end=======================================*/
    /**
     * 用表中的交易码
     */
    public static final String TXN_CODE = "txnCode";
    /**
     * 费用收取方式
     */
    public static final String CHARGE_OPTION = "chargeOption";

    /*=========================end=======================================*/

    /*=========================start=====================================*/
    /**
     * 费用处理常量
     */
    /**
     * 费用固定值
     */
    public static final String FIXED_AMNT = "fixedAmnt";
    /**
     * 溢缴款取现费用收取比例
     */
    public static final String CHARGE_PERCENT = "chargePercent";
    /**
     * 溢缴款取现最大费用值
     */
    public static final String MAX_AMNT = "maxAmnt";
    /**
     * 溢缴款取现最小费用值
     */
    public static final String MIN_AMNT = "minAmnt";
    /**
     *
     */
    public static final String DESCRIPTION = "description";
    /**
     * 优先级
     */
    public static final String PRIORITY = "priority";
    /**
     * 账单标志
     */
    public static final String STATEMENT_FLAG = "statementFlag";
    /**
     * 交易类型标志（finance_flag）
     */
    public static final String FINACE_FLA = "financeFlag";

    /**
     * 键值类型（3位数字型）
     */
    public static final String ORG_NUMBER = "organizationNumber";
    /**
     * 产品
     */
    public static final String PRO_NUMBER = "productNumber";
    /**
     * 账单展示标志
     */
    public static final String STATEMENT_DISPLAY_INDICATOR =
            "statementDisplayIndicator";

    /*=========================start=====================================*/
    /**
     * 系统封锁码-延滞
     */
    public static final String BLOCK_CODE_TYPE_CYCLE = "0";
    public static final String BLOCK_CODE_TYPE_OVER = "1";

    /*=========================end=======================================*/
    /**
     * 贷记交易处理
     */
    public static final String BLOCK_CODE_TYPE = "type";
    public static final short CYCLE_DUE_AMOUNT_FLAG = 2;
    /**
     * 原交易匹配标志
     */
    public static final String ORIGINAL_TRANS_MATCH_FLAG_Y = "Y";
    public static final String ORIGINAL_TRANS_MATCH_FLAG_N = "N";

    /**
     * 统计账户更新标志
     */
    public static final String STATICS_ACCOUNT_UPDATE_FLAG_N = "N";

    /**
     * 入账明细
     */
    public static final int CYCLE_DAY = 28;

    /**
     * 币种
     */
    public static final String CURRENCY_CODE = "156";

    /**
     * 主卡年费参数表ID
     */
    public static final String PRIMARY_ANNUAL_FEE_TAB_ID = "primaryAnnualFeeTableId";

    /**
     * 副卡年费参数表ID
     */
    public static final String SUPPLEMENT_ANNUAL_FEE_TAB_ID =
            "supplementAnnualFeeTableId";

    /**
     * 费用生成方式
     */
    public static final String GENERATE_OPTION = "generateOption";



    /*=========================end=======================================*/

    /*=========================start=====================================*/
    /**
     * 滞纳金计算方式
     */
    public static final String CALCULATE_METHOD = "calculateMethod";

    /**
     * 衍生的费用交易
     */
    public static final String DERIVE_FEE = "deriveFee";

    /**
     * 消费本金利息类交易
     */
    public static final String CONSUMER = "consumer";

    /**
     * 现金本金利息类交易
     */
    public static final String CASH = "cash";
    /**
     * 产品信息表中的卡BIN参数表ID
     */
    public static final String CARD_BIN_TABLE_ID = "cardBinTableId";

    /*=========================end=======================================*/

    /*=========================start=====================================*/
    /**
     * 开户申请
     */
    /**
     * BIN顺序号
     */
    public static final String BIN_SEQUENCE = "binSequence";
    /**
     * 终止卡号
     */
    public static final String END_CARD_NUMBER = "endCardNumber";
    /**
     * 卡号用完标志
     */
    public static final String FINISH_INDICATOR = "finishedIndicator";
    /**
     * 上次使用卡号
     */
    public static final String LAST_USED_NUMBER = "lastUsedNumber";
    /**
     * 已签约
     */
    public static final String AUTO_PAYMENT_FLAG = "1";
    /**
     * 未签约
     */
    public static final String AUTO_PAYMENT_FLAG_ZERO = "0";

    /**
     * 币种
     */
    public static final String RMB = "156";
    public static final String SGD = "702";

    /**
     * 争议处理
     */
    /*=========================start=====================================*/
    /**
     * 争议登记交易码
     */
    public static final String DISPUTE_REG_TXN_CODE = "disputeRegisterTxnCode";
    /**
     * 系统处理日
     */
    public static final String DISPUTE_RELEASE_TXN_CODE = "disputeReleaseTxnCode";
    /**
     * 贷记调整交易码
     */
    public static final String CREDIT_ADJUST_TXN_CODE = "creditAdjustTxnCode";
    /**
     * 允许争议标志
     */
    public static final String DISPUTE_ALLOW_CODE = "disputeAllowCode";
    /**
     * 对持卡人有利的争议释放方式
     */
    public static final String RELEASE_C = "ReleaseC";
    /**
     * 对发卡行有利的争议释放方式
     */
    public static final String RELEASE_B = "ReleaseB";
    public static final String AUTO_PAYMENT_TABLE_ID = "autoPaymentTableId";
    public static final String AUTO_PAYMENT_FIRST_DAYS = "autoPaymentFirstDays";
    public static final String AUTO_PAYMENT_SECOND_DAYS = "autoPaymentSecondDays";
    public static final String EXCEPTION_CODE = "500";
    public static final String SUCCESS_CODE = "200";

    private Constants() {
    }
    /*=========================end=======================================*/

    /**
     * 拒绝交易重入账
     */
    /*=========================start=====================================*/
    /**
     * 入账拒绝交易ID
     */
    public static final String REJECTED_TRANSACTION_ID = "rejectedTransactionId";

    /**
     * 管理账户号ID
     */
    public static final String ACCOUNT_MANAGEMENT_ID = "accountManagementId";

    /**
     * 交易码
     */
    public static final String POSTING_TRANSACTION_CODE = "postingTransactionCode";

    /**
     * 卡号
     */
    public static final String CARD_NUMBER = "cardNumber";

    /**
     * 交易日期
     */
    public static final String TRANSACTION_DATE = "transactionDate";
    /*=========================end=======================================*/

    /**
     * 购汇标志
     */
    public static final class PfExchangeFlag {
        /**
         * 1：购汇  0：非购汇
         */
        public static final String PF_EXCHANGE_FLAG_Y = "1";
        /**
         * 1：购汇  0：非购汇
         */
        public static final String PF_EXCHANGE_FLAG_N = "0";

    }

    /**
     * 默认更新人
     */
    public static final String DEFAULT_USER = "admin";
    /**
     * 默认机构编号
     */
    public static final String DEFAULT_ORG_NUMBER = "0001";

    /**
     * 默认系统编号
     */
    public static final String DEFAULT_SYSTEMID = "0000";

    /** 禁用 */
    public static final String DISABLED = "0";
    /** 启用 */
    public static final String ENABLED = "1";
    /**
     * 通用
     */
    /*=========================start=====================================*/
    /**
     * 1
     */
    public static final Integer ONE = 1;
    /**
     * 2
     */
    public static final Integer TWO = 2;
    /**
     * 3
     */
    public static final Integer THREE = 3;

    /**
     * "1"
     */
    public static final String S_ONE = "1";
    /**
     * "2"
     */
    public static final String S_TWO = "2";
    /**
     * "3"
     */
    public static final String S_THREE = "3";
    /*=========================end=======================================*/
    /**
     * 额度类型SC99-客户层
     */
    public static final String LIMIT_TYPE_SC99 = "SC99";
    /**
     * 额度类型SC01-个人卡账户层消费
     */
    public static final String LIMIT_TYPE_SC01 = "SC01";
    /**
     * 额度类型SC04-公司卡账户层消费
     */
    public static final String LIMIT_TYPE_SC04 = "SC04";
    /**
     * 额度类型SC01-个人卡账户层溢缴款
     */
    public static final String LIMIT_TYPE_SA00 = "SA00";
    /**
     * 额度类型SAC0-公司卡账户层溢缴款
     */
    public static final String LIMIT_TYPE_SAC0 = "SAC0";
    /**
     * VA账产品号
     */
    public static final String ACCOUNT_PRODUCT_VA = "VA0001";
}
