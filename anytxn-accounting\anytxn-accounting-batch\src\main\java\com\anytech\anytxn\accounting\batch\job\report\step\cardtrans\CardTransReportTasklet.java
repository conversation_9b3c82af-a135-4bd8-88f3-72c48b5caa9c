package com.anytech.anytxn.accounting.batch.job.report.step.cardtrans;

import com.anytech.anytxn.accounting.base.enums.ChannelEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.model.CardTrans;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmProductInfoSelfMapper;
import com.google.common.collect.Maps;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.dto.TransTotal;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/15
 */
public class CardTransReportTasklet extends AbstractReportTasklet<PostedTransaction, TransTotal> {

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;
    @Autowired
    private ParmTransactionCodeSelfMapper parmTransactionCodeSelfMapper;
    @Autowired
    private ParmProductInfoSelfMapper parmProductInfoSelfMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;


    public CardTransReportTasklet(BaseConfigurer baseConfigurer) {
        super(baseConfigurer);
    }

    @Override
    protected List<PostedTransaction> loadModels() {
        OrganizationInfoResDTO organizationInfo =
                organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        organizationInfo.setToday(LocalDate.of(2020,4,23));
        return postedTransactionSelfMapper.selectByPostDate(organizationInfo.getOrganizationNumber(), organizationInfo.getToday());
    }

    @Override
    protected List<TransTotal> modelToReport(List<PostedTransaction> models) {

        Map<String, ParmTransactionCode> cache = Maps.newHashMap();
        Map<String, AccountManagementInfo> cache2 = Maps.newHashMap();
        Map<String, ParmAcctProductMainInfo> cache3 = Maps.newHashMap();
        // 筛选
        return TransTotal.resetReportData(models.stream().filter(x-> {
            if (StringUtils.equalsAny(x.getPostingTransactionCode(),  "40100")) {
                return true;
            }

            String code = x.getPostingTransactionCode();
            if (!cache.containsKey(code)) {
                ParmTransactionCode transactionCode = parmTransactionCodeSelfMapper.selectByOrgNumberAndCode(OrgNumberUtils.getOrg(), code);
                cache.put(code, transactionCode);
            }

            ParmTransactionCode transactionCode = cache.get(code);
            if (transactionCode != null && StringUtils.equalsAny(transactionCode.getTransactionAttribute(), "1","2") && transactionCode.getDebitCreditIndicator().equals("D")) {
                return true;
            }
                return false;
        }).map(x-> {
            CardTrans result = new CardTrans();
            result.setCurrencyCode(x.getPostingCurrencyCode());
            result.setTransactionCode(x.getPostingTransactionCode());
            if (!cache.containsKey(x.getPostingTransactionCode())) {
                ParmTransactionCode transactionCode = parmTransactionCodeSelfMapper.selectByOrgNumberAndCode(OrgNumberUtils.getOrg(), x.getPostingTransactionCode());
                cache.put(x.getPostingTransactionCode(), transactionCode);
            }
            ParmTransactionCode parmTransactionCode = cache.get(x.getPostingTransactionCode());
            if (parmTransactionCode != null) {
                result.setDescription(parmTransactionCode.getDescription());
            }

            if (StringUtils.isEmpty(x.getAccountManagementId())) {
                return null;
            }

            AccountManagementInfo managementInfo = cache2.get(x.getAccountManagementId());
            if (!cache2.containsKey(x.getAccountManagementId())) {
                managementInfo = accountManagementInfoMapper.selectByPrimaryKey(x.getAccountManagementId());
                cache2.put(x.getAccountManagementId(), managementInfo);
            }

           /* ParmProductInfo info2 = managementInfo != null ? cache3.get(managementInfo.getProductNumber()) : null;
            if (managementInfo != null && !cache3.containsKey(managementInfo.getProductNumber())) {
                List<ParmProductInfo> info2s = parmProductInfoSelfMapper.selectByProdNumber(managementInfo.getProductNumber());
                if (CollectionUtils.isNotEmpty(info2s)) {
                    info2 = info2s.get(0);
                    cache3.put(managementInfo.getProductNumber(), info2);

                }
            }*/

            ParmAcctProductMainInfo info2 = managementInfo != null ? cache3.get(managementInfo.getProductNumber()) : null;
            if (managementInfo != null && !cache3.containsKey(managementInfo.getProductNumber())) {
                ParmAcctProductMainInfo acctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(managementInfo.getOrganizationNumber(), managementInfo.getProductNumber());
                if (null != acctProductMainInfo) {
                    cache3.put(managementInfo.getProductNumber(), acctProductMainInfo);

                }
            }



            if (info2 == null) {
                return null;
            }
            result.setCardType(info2.getAttribute().equals("U") ? "贷记卡" : "准贷记卡");
            result.setTransactionSource(ChannelEnum.getDes(x.getTransactionSource()));
            result.setTransactionAmount(x.getPostingAmount());
            result.setTransactionCurrency(x.getTransactionCurrencyCode());
            result.setTransactionAmount2(x.getTransactionAmount());
            result.setSettlementAmount(x.getSettlementAmount());
            result.setSettlementCurrency(x.getSettlementCurrencyCode());
            result.setResult("成功");
            result.setCglAcct(StringUtils.equalsAny(x.getPostingTransactionCode(),"30100", "40100") ? "12510201" : "41010501");
            result.setDglAcct(StringUtils.equalsAny(x.getPostingTransactionCode(),"30100", "40100") ? "41010501" : "12510201");

            return result;
        }).filter(Objects::nonNull).collect(Collectors.toList()));
    }
}
