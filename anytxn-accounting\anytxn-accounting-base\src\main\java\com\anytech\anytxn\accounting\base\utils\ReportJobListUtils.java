/**
 * Copyright (c) 2020. 北京江融信科技有限公司 版权所有
 * Created on 2020-03-10.
 */

package com.anytech.anytxn.accounting.base.utils;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-03-10
 */
public class ReportJobListUtils {
    /**
     * 将一组数据固定分组，每组n个元素
     * @param source 要分组的数据源
     * @param n      每组n个元素
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> fixedGrouping(List<T> source, int n) {
        if(null == source || source.size() == 0 || n <= 0){
            return null;
        }
        List<List<T>> result = new ArrayList<List<T>>();

        int sourceSize = source.size();
        int size = (source.size() / n) + 1;
        for (int i = 0; i < size; i++) {
            List<T> subset = new ArrayList<T>();
            for (int j = i * n; j < (i + 1) * n; j++) {
                if (j < sourceSize) {
                    subset.add(source.get(j));
                }
            }
            if (!CollectionUtils.isEmpty(subset)){
                result.add(subset);
            }
        }
        return result;
    }

}
