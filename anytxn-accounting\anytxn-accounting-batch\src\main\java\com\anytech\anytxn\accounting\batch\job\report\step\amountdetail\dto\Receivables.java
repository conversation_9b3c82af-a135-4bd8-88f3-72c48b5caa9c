package com.anytech.anytxn.accounting.batch.job.report.step.amountdetail.dto;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.amountdetail.model.AmountDetail;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应收账款报表
 * <AUTHOR>
 * @Date 2020/9/11 11:43
 */
@Getter
@Setter
@ToString
public class Receivables {

    private List<ReceivablesSub> list;
    //总数
    private Integer total;

    public static Receivables resetReportData(List<AmountDetail> data, TimeTypeEnum timeTypeEnum){
        Receivables receivables = new Receivables();
        receivables.setTotal(data.size());
        List<ReceivablesSub> subs = new ArrayList<>();
        receivables.setList(subs);

        Map<String, List<AmountDetail>> group = data.stream().collect(Collectors.groupingBy(x -> x.getBranchId() + x.getCurrCode() + x.getAcctLogo()));
        int i = 0;
        for (Map.Entry<String, List<AmountDetail>> entry : group.entrySet()) {
            List<AmountDetail> value = entry.getValue();

            ReceivablesSub sub = new ReceivablesSub();
            subs.add(sub);
            sub.setEnd(++i == group.size());

            List<ReceivablesSubTotal> totals = Lists.newArrayList();
            sub.setSubTotalList(totals);
            groupTotal(value, totals);

            List<ReceivablesDetail> details = Lists.newArrayList();
            sub.setDetailList(details);
            groupDetail(value, details, timeTypeEnum);

        }

        return receivables;
    }

    private static void groupTotal(List<AmountDetail> data, List<ReceivablesSubTotal> totals){
        Map<String, List<AmountDetail>> group = data.stream().collect(Collectors.groupingBy(x -> x.getDrcr() + x.getGlAcct()));
        for (Map.Entry<String, List<AmountDetail>> entry : group.entrySet()) {
            List<AmountDetail> value = entry.getValue();
            AmountDetail temple = value.get(0);

            ReceivablesSubTotal total = new ReceivablesSubTotal();
            totals.add(total);

            total.setBankNumber(temple.getBranchId());
            total.setCurrencyCode(temple.getCurrCode());
            total.setProductId(temple.getAcctLogo());
            total.setDebitCreditIndcator(temple.getDrcr());
            total.setSubjectNumber(temple.getGlAcct());
            total.setCreateAmountTotal(value.stream().map(AmountDetail::getGlAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        }
    }

    private static void groupDetail(List<AmountDetail> data, List<ReceivablesDetail> details, TimeTypeEnum timeTypeEnum){
        for (AmountDetail datum : data) {
            ReceivablesDetail detail = new ReceivablesDetail();
            details.add(detail);

            detail.setBankNumber(datum.getBranchId());
            detail.setCreateAmount(datum.getGlAmount());
            detail.setCurrencyCode(datum.getCurrCode());
            detail.setCustomerId(datum.getCustomerId());
            detail.setCustomerName(datum.getCustomerName());
            detail.setDebitCreditIndcator(datum.getDrcr());
            detail.setDesc(datum.getDescription());
            detail.setPostDate(TimeUtils.dateToStr(datum.getPostDate(), timeTypeEnum));
            detail.setProductId(datum.getAcctLogo());
            detail.setSubjectNumber(datum.getGlAcct());
            detail.setSubjectName(datum.getGlAcctName());
        }
    }

}
