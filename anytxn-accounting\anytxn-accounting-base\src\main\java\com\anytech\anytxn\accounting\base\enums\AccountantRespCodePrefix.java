package com.anytech.anytxn.accounting.base.enums;

import com.anytech.anytxn.common.core.constants.RespCodePrefix;

/**
 * <AUTHOR>
 * @date 2020/3/16
 *
 * 会计响应码常量定义，遵循AntTxn响应码规范
 * 3位业务服务编码 + 2位功能分类响应码 + 5位具体响应码
 *
 * 统一异常码使用RespCodePrefix 禁止自定义！  此类只定义会计业务具体异常码和组合码
 */
@SuppressWarnings("ALL")
public interface AccountantRespCodePrefix extends RespCodePrefix {

    static final String P_PARAM_EMPTY= PREFIX_ACCOUNTANT_D + "10000";
    static final String P_DATE_FORMAT= PREFIX_ACCOUNTANT_D + "10001";
    static final String P_PARAM_FORMAT= PREFIX_ACCOUNTANT_D + "10001";
    static final String P_BATCHMODE_0 = PREFIX_ACCOUNTANT_D + "10010";
    static final String P_GLACGN_EMPTY = PREFIX_ACCOUNTANT_D + "10011";
    static final String P_LOAN_CURRCOD = PREFIX_ACCOUNTANT_D + "10012";

    static final String D_DATABASE_ERROR = PREFIX_ACCOUNTANT_D + "20001";
}
