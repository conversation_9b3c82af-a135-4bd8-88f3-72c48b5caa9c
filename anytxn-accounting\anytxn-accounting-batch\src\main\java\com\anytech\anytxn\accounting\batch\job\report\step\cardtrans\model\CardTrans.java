package com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/10/15
 */
@Getter
@Setter
public class CardTrans {

    private String currencyCode;

    private String transactionCode;

    private String transactionCurrency;

    private String description;

    private String cardType;

    private BigDecimal transactionAmount;

    private BigDecimal transactionAmount2;

    private String transactionSource;

    private String settlementCurrency;

    private BigDecimal settlementAmount;

    private String result;

    private String cglAcct;

    private String dglAcct;
}
