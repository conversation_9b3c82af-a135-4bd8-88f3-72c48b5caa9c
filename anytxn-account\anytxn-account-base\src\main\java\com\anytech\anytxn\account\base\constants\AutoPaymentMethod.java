package com.anytech.anytxn.account.base.constants;

/**
 * 约定扣款方式
 *
 * <AUTHOR>
 * @date 2018-9-22
 *
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 *
 */
public enum AutoPaymentMethod {

    /**
     * 1: 按最小还款额进行约定扣款
     * 2: 按全额进行约定扣款
     * 3：按最小还款额进行约定扣款
     * 4：按全额进行约定扣款
     */
    MIN_ONE("1", "按最小还款额进行约定扣款"),
    ALL_TWO("2", "按全额进行约定扣款"),
    MIN_THREE("3", "按最小还款额进行约定扣款"),
    ALL_FOUR("4", "按全额进行约定扣款");

    private String code;
    private String reason;


    AutoPaymentMethod(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }
}
