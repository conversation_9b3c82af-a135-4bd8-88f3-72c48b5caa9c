package com.anytech.anytxn.accounting.batch.job.glvchersum.steps;

import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.mapper.AccountantGlvcherSumSelfMapper;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlvcherSumC;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/3
 */
@Slf4j
public class GlvcherSumTasklat2 implements Tasklet {

    @Autowired
    private AccountantGlvcherSumSelfMapper accountantGlvcherSumSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Override
    @Transactional
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        List<TAmsGlvcherSumC> tAmsGlvcherSums = accountantGlvcherSumSelfMapper.selectAllCopy(OrgNumberUtils.getOrg());
        if (!CollectionUtils.isEmpty(tAmsGlvcherSums)){

            tAmsGlvcherSums.forEach(x-> {
                x.setCreateTime(LocalDateTime.now());
                x.setUpdateTime(LocalDateTime.now());
                x.setUpdateBy("admin");
                x.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                x.setVersionNumber(1L);
                x.setProcessInd("0");
            });

            // 更新汇总
            accountantGlvcherSumSelfMapper.insertBatch0(Lists.newArrayList(tAmsGlvcherSums));
            // 删除
            accountantGlvcherSumSelfMapper.deleteAllCopy(OrgNumberUtils.getOrg());
        }

        return RepeatStatus.FINISHED;
    }
}
