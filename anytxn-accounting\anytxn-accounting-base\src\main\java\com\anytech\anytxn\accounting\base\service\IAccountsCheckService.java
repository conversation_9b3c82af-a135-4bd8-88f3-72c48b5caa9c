package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkResDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
/**
 * <AUTHOR>
public interface IAccountsCheckService {
    /**
     * TODO
      * @param pmsGlacgnDTO
     * @return com.anytech.anytxn.accountant.dto.TAmsGlbalchkDTO
     * <AUTHOR> @date 2020-7-28
     */
    TAmsGlbalchkDTO accountCheckHandle(TPmsGlacgnDTO pmsGlacgnDTO);
    /**
     * TODO
      * @param page
     * @param rows
     * @param postingDate
     * @return com.anytech.anytxn.biz.common.dto.PageResultDTO<com.anytech.anytxn.accountant.dto.TAmsGlbalchkResDTO>
     * <AUTHOR> @date 2020-7-28
     */
    PageResultDTO<TAmsGlbalchkResDTO> getBalChkByPage(Integer page, Integer rows, String postingDate);
    /**
     * TODO
      * @param pmsGlacgnDTO
 * @param today
 * @param accountManagementId
     * @return java.math.BigDecimal
     * <AUTHOR> @date 2020-7-28
     */
    BigDecimal getGlBalance(TPmsGlacgnDTO pmsGlacgnDTO, LocalDate today, String accountManagementId);
}
