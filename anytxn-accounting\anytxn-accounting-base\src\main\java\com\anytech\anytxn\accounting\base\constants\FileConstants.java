package com.anytech.anytxn.accounting.base.constants;

import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2021-02-19
 */
public final class FileConstants {

    private static String systemName = System.getProperties().getProperty("os.name");
    public static DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final String CARD_INFO = "card_info_";
    public static final String ACCOUNT_INFO = "account_info_";
    public static final String CUSTOMER_INFO = "customer_info_";
    public static final String STATEMENT_INFO = "statement_info_";
    public static final String STATEMENT_DETAIL_INFO = "statement_detail_info_";
    public static final String SUFFIX = ".txt";
    public static final String OK_SUFFIX = ".ok";
    public static final String BAK = "bak";

    public static String getFileName(String preFix, LocalDate bizDate) {
        return preFix + YYYYMMDD.format(bizDate) + "_" + OrgNumberUtils.getOrg() + SUFFIX;
    }
    public static String getFileName(String preFix, LocalDate bizDate, String num) {
        return preFix + YYYYMMDD.format(bizDate) + num + SUFFIX;
    }

    public static String getPathForSystem() {
        return systemName.contains("Windows") ? "\\" : "/";
    }
}
