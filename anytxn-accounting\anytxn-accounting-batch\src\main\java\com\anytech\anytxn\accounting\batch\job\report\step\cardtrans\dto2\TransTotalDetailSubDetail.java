package com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.dto2;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2020/10/15 18:11
 */
@Getter
@Setter
@ToString
public class TransTotalDetailSubDetail {

    private String transCode;//交易代码

    private String credDesc;//描述 贷记卡

    private String cardType;//准贷记卡

    private String settlementDesc = "RMB 4983.00";//结算金额

    private String transSource;//交易来源

    private String transCount = "934";//交易笔数

    private String clearAmountDesc ="54343.00";//卡组织清算金额

    private String transResult = "交易结果";//交易结果

    private String debitSubject = "借方科目";//借方科目

    private String credSubject = "贷方科目";//贷方科目
}
