package com.anytech.anytxn.account.service;

import com.anytech.anytxn.account.base.service.IAutoPaymentLogService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest
@EnableAutoConfiguration(exclude= {DataSourceAutoConfiguration.class})
@TestPropertySource(locations = "classpath:application.properties")
public class AutoPaymentLogServiceTest {

    @Autowired
    private IAutoPaymentLogService autoPaymentLogService;


    @Test
    public void search(){
        autoPaymentLogService.selectById("1");
    }

}
