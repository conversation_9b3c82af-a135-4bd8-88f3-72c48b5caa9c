package com.anytech.anytxn.account.base.domain.bo;

import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AutoPaymentLog;
import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GiroAutoPayBO extends BaseEntity {
    private AccountManagementInfo accountManagementInfo;
    private AutoPaymentLog autoPaymentLog;
}
