package com.anytech.anytxn.accounting.base.exception;

import com.anytech.anytxn.accounting.base.enums.AccountRepDetailEnum;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import com.anytech.anytxn.common.core.exception.AnyTxnException;

/**
 * <AUTHOR>
 * @date 2020/3/16
 *
 * Accountant自定义异常，遵循AnyTxn异常规范
 */
public class AnyTxnAccountantException extends AnyTxnException {

    public AnyTxnAccountantException(AccountantRespCodeEnum e) {
        this(e, null);
    }

    public AnyTxnAccountantException(AccountantRespCodeEnum e, AccountRepDetailEnum detail, Object... params) {
        this(e, detail, null, params);
    }

    public AnyTxnAccountantException(AccountantRespCodeEnum e, Throwable cause) {
        this(e, null, cause);
    }

    public AnyTxnAccountantException(AccountantRespCodeEnum e, AccountRepDetailEnum detail, Throwable cause, Object... params) {
        super(e.getCode(), e.getMsg(), detail(detail, params), cause);
    }

    private static String detail(AccountRepDetailEnum detail, Object... params){
        if (detail == null ) {
            return null;
        }

        return params == null ? detail.message() : String.format(detail.message(), params);
    }
}
