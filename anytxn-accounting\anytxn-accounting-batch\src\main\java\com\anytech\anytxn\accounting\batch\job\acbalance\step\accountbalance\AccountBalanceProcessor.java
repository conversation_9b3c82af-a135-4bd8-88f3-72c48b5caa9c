package com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance;

import com.anytech.anytxn.accounting.base.service.IBalService;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlactbalDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2020/4/3
 *
 * 生成分户额度
 */
public class AccountBalanceProcessor implements ItemProcessor<AccountBalanceInfoDTO, AccountantGlactbalDTO> {

    @Autowired
    private IBalService balService;

    @Override
    public AccountantGlactbalDTO process(AccountBalanceInfoDTO item) throws Exception {
        return balService.accountBalanceSum(item);
    }
}
