package com.anytech.anytxn.accounting.base.domain.model;


import com.anytech.anytxn.common.core.base.BaseEntity;
import com.anytech.batch.file.SeparatedType;
import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TransferFileObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@TransferFileObject(separatedType = SeparatedType.SEPARATOR, lineTailSeparator = false,
        encoding = "UTF-8", description = "非法传票文件",postfix = ".csv",separator = ",")
public class AccountantGlvcherOut extends BaseEntity implements Comparable<AccountantGlvcherOut>, Serializable {

    /**
     * 分行号
     * 表字段:BRANCHID
     */
    @FileField(order = 1, length = 50)
    private String branchid;

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    @FileField(order = 2, length = 50)
    private String accountManagementId;

    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    @FileField(order = 3, length = 50)
    private String acctLogo;

    /**
     * 全局业务流水号
     * 表字段:GLOBAL_FLOW_NO
     */
    @FileField(order = 4, length = 50)
    private String globalFlowNo;

    /**
     * 模块标识
     * 表字段:MODULE_FLAG
     */
    @FileField(order = 5, length = 50)
    private String moduleFlag;

    /**
     * 交易码
     * 表字段:VP_TXN_CODE
     */
    @FileField(order = 6, length = 50)
    private String vpTxnCode;

    /**
     * 币种
     * 表字段:CURR_CODE
     */
    @FileField(order = 7, length = 50)
    private String currCode;

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    @FileField(order = 8, length = 50)
    private String glAcct;

    /**
     * 借贷方向
     * 表字段:DRCR
     */
    @FileField(order = 9, length = 50)
    private String drcr;

    /**
     * 交易金额
     * 表字段:GL_AMOUNT
     */
    @FileField(order = 10, length = 50)
    private BigDecimal glAmount;

    /**
     * 入账笔数
     * 表字段:POSTING_CNT
     */
    @FileField(order = 11, length = 50)
    private BigDecimal glCnt;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    @FileField(order = 12, length = 50)
    private String postingDate;

    /**
     * 处理标识
     * 表字段:PROCESS_TYPE
     */
    @FileField(order = 13, length = 50)
    private String processType;

    /**
     * 分期订单号
     * 表字段:ORDER_ID
     */
    @FileField(order = 14, length = 50)
    private String orderId;

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    @FileField(order = 15, length = 50)
    private String absStatus;

    /**
     * 原始交易码
     * 表字段:TXN_CODE_ORIG
     */
    @FileField(order = 16, length = 50)
    private String txnCodeOrig;

    /**
     * 渠道ID
     * 表字段:CHANNEL_ID
     */
    @FileField(order = 17, length = 50)
    private String channelId;

    /**
     * 子渠道ID
     * 表字段:SUBCHANNEL_ID
     */
    @FileField(order = 18, length = 50)
    private String subchannelId;

    /**
     * 资金源ID
     * 表字段:FUND_ID
     */
    @FileField(order = 19, length = 50)
    private String fundId;

    /**
     * 平台ID
     * 表字段:PLATFORM_ID
     */
    @FileField(order = 20, length = 50)
    private String platformId;

    /**
     * 商户ID
     * 表字段:MERCHANT_NUMBER
     */
    @FileField(order = 21, length = 50)
    private String merchantNumber;

    /**
     * 五级分类标识
     */
    @FileField(order = 22, length = 50)
    private String fiveTypeIndicator;

    /**
     * abs类型
     */
    @FileField(order = 23, length = 50)
    private String absType;

    /**
     * crdNumber
     */
    @FileField(order = 24, length = 50)
    private String crdNumber;

    /**
     * crdProductNumber
     */
    @FileField(order = 25, length = 50)
    private String crdProductNumber;

    /**
     * crdType
     */
    @FileField(order = 26, length = 50)
    private String crdType;

    /**
     * txnAmt
     */
    @FileField(order = 27, length = 50)
    private BigDecimal txnAmt;

    /**
     * txnCurrency
     */
    @FileField(order = 28, length = 50)
    private String txnCurrency;

    /**
     * settleAmt
     */
    @FileField(order = 29, length = 50)
    private BigDecimal settleAmt;

    /**
     * settleCurrency
     */
    @FileField(order = 30, length = 50)
    private String settleCurrency;

    /**
     * txnDate
     */
    @FileField(order = 31, length = 50)
    private LocalDateTime txnDate;

    /**
     * txnSource
     */
    @FileField(order = 32, length = 50)
    private String txnSource;

    /**
     * txnDescription
     */
    @FileField(order = 33, length = 50)
    private String txnDescription;

    /**
     * currencyRate
     */
    @FileField(order = 34, length = 50)
    private String currencyRate;

    /**
     * rrn
     */
    @FileField(order = 35, length = 50)
    private String rrn;

    /**
     * acqId
     */
    @FileField(order = 36, length = 50)
    private String acqId;

    /**
     * mkUpFeeInd
     */
    @FileField(order = 37, length = 50)
    private String mkUpFeeInd;

    /**
     * 产品类型
     */
    @FileField(order = 38, length = 50)
    private String productType;

    /**
     * 卡组织
     */
    @FileField(order = 39, length = 50)
    private String cardOrgNumber;

    /**
     *
     * va号:VA_NUMBER
     */
    @FileField(order = 40, length = 50)
    private String vaNum;

    @FileField(order = 41, length = 25)
    private String vataCoupleIndicator;

    @Override
    public int compareTo(AccountantGlvcherOut o) {
        return 0;
    }
}
