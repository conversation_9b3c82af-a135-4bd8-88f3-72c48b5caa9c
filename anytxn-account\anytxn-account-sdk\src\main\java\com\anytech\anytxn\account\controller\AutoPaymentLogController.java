package com.anytech.anytxn.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogService;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogSearchKeyDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019-11-16
 */
@RestController
@Tag(name = "约定扣款流水接口")
public class AutoPaymentLogController extends BizBaseController {
    @Autowired
    private IAutoPaymentLogService autoPaymentLogService;

    @Operation(summary = "根据搜索条件分页查询约定扣款流水")
    @PostMapping(path = "/account/autoPayment")
    AnyTxnHttpResponse<PageResultDTO<AutoPaymentLogDTO>> findAll(
            @RequestBody(required = false) AutoPaymentLogSearchKeyDTO searchKey){
            PageResultDTO<AutoPaymentLogDTO> result = autoPaymentLogService.findByCardNum(searchKey);
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "根据约定扣款id查询约定扣款流水明细")
    @GetMapping("/account/autoPayment/autoPaymentId/{autoPaymentId}")
    @Parameter(name = "autoPaymentId", required = true, description = "约定扣款id")
    AnyTxnHttpResponse<AutoPaymentLogDTO> getAutoPaymentById(
            @PathVariable("autoPaymentId") String autoPaymentId) {
            AutoPaymentLogDTO autoPaymentLogDTO = autoPaymentLogService.selectById(autoPaymentId);
        return AnyTxnHttpResponse.success(autoPaymentLogDTO);
    }
}
