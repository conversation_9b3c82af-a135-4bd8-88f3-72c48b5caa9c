package com.anytech.anytxn.account.job.createokfile;

import com.anytech.anytxn.file.utils.okfile.DownloadOkFileTasklet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;

/**
 * .ok文件生成批量
 *
 * <AUTHOR>
 * @date 2021-07-09 10:55
 **/
@Configuration
@Slf4j
public class DownloadOkConfig {
    @Autowired
    private StepBuilderFactory stepBuilderFactory;

    @Bean
    public Step okFileStep(
            @Qualifier("okFileTasklet") DownloadOkFileTasklet okFileTasklet) {
        return stepBuilderFactory
                .get("okFileStep")
                .tasklet(okFileTasklet)
                .build();
    }

    @Bean
    @StepScope
    public DownloadOkFileTasklet okFileTasklet(
            @Value("#{jobExecutionContext[filePath]}")String filePath,
            @Value("#{jobExecutionContext[okFileName]}")String okFileName,
            @Value("#{jobExecutionContext[flag]}") boolean flag) {
        return new DownloadOkFileTasklet(filePath + File.separator + okFileName, flag);
    }
}
