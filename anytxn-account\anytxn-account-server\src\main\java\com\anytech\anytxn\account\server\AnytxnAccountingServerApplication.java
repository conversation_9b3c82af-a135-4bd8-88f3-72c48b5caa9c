package com.anytech.anytxn.account.server;

import com.anytech.anytxn.account.EnableAccountingApi;
import com.anytech.anytxn.common.core.annotation.EnableCacheListenerAnnotation;
import io.micrometer.core.instrument.MeterRegistry;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.batch.BatchAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;


/**
 * <AUTHOR>
 */
@SpringBootApplication(
        exclude = {DataSourceAutoConfiguration.class, BatchAutoConfiguration.class,})
@ComponentScan(basePackages = {
        "com.anytech.anytxn.common",
        "com.anytech.anytxn.account",
        "com.anytech.anytxn.business",
        "com.anytech.anytxn.file.utils",
        "com.anytech.anytxn.central.service",
        "com.anytech.anytxn.central.config",
        "com.anytech.anytxn.central.base.utils",
        "com.anytech.anytxn.central.cache"
})
@MapperScan({
        "com.anytech.anytxn.common.rule.limit.mapper",
        "com.anytech.anytxn.limit.mapper",
        "com.anytech.anytxn.business.dao.**.mapper",
        "com.anytech.anytxn.central.mapper",
        "com.anytech.anytxn.parameter.**.mapper",})
@EnableAccountingApi
@EnableCacheListenerAnnotation
@EnableTransactionManagement
@EnableFeignClients(basePackages = {
        "com.anytech.anytxn.transaction.feign",
        "com.anytech.anytxn.account.feign",
        "com.anytech.anytxn.central.client"})
public class AnytxnAccountingServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(AnytxnAccountingServerApplication.class, args);
    }

    /**
     * 监控配置bean
     */
    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}") String applicatonName,
                                                      @Value("${spring.cloud.client.ip-address}") String serverAddr,
                                                      @Value("${server.port}") String port) {
        return (registry) -> registry.config().commonTags("application", applicatonName.concat("-")
                .concat(serverAddr).concat(":").concat(port));
    }
}
