package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.anytxn.common.core.base.BaseEntity;
import com.anytech.batch.file.SeparatedType;
import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TransferFileObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.ToString;

@Getter
@Setter
@TransferFileObject(separatedType = SeparatedType.SEPARATOR, lineTailSeparator = false,
        encoding = "UTF-8", description = "原始记账明细文件",postfix = ".csv",separator = ",")
@ToString
public class AccountantGlOriRecordDetail extends BaseEntity implements Comparable<AccountantGlOriRecordDetail>, Serializable {

    /**
     * CP_FINEXT.CB_EXT_REF_NO 8 汇总日期
     */
    @FileField(order = 1, length = 12)
    private String sumDate;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 科目号
     */
    @FileField(order = 2, length = 50)
    private String subjectCodeId;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 120 科目描述
     */
    @FileField(order = 3, length = 50)
    private String subjectDesc;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 2 借贷记标志
     */
    @FileField(order = 4, length = 50)
    private String debitCreditSide;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 汇总金额
     */
    @FileField(order = 5, length = 0, padChar = "0")
    private BigDecimal totalAmt;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 3 币种
     */
    @FileField(order = 6, length = 50)
    private String sumCurrency;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 交易码
     */
    @FileField(order = 7, length = 50)
    private String txnCode;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 120 交易描述
     */
    @FileField(order = 8, length = 50)
    private String txnDesc;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 6 帐产品
     */
    @FileField(order = 9, length = 50)
    private String accountProduct;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 卡号
     */
    @FileField(order = 10, length = 50)
    private String crdNumber;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 50 商户号
     */
    @FileField(order = 11, length = 50)
    private String merchantId;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 17 完整交易日期
     */
    @FileField(order = 12, length = 50)
    private String txnDate;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 交易来源
     */
    @FileField(order = 13, length = 50)
    private String source;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 3 交易币种
     */
    @FileField(order = 14, length = 50)
    private String txnCurrency;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 交易金额
     */
    @FileField(order = 15, length = 0, padChar = "0")
    private BigDecimal txnAmt;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 30 货币汇率
     */
    @FileField(order = 16, length = 50)
    private String currencyRate;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 32 参考号
     */
    @FileField(order = 17, length = 50)
    private String rrn;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 6 卡产品
     */
    @FileField(order = 18, length = 50)
    private String cardProduct;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 卡组织
     */
    @FileField(order = 19, length = 50)
    private String cardOrganization;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 模块标志
     */
    @FileField(order = 20, length = 50)
    private String moduleFlag;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 模块标志
     */
    @FileField(order = 21, length = 50)
    private String acqId;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 模块标志
     */
    @FileField(order = 22, length = 50)
    private String productType;

    /**
     * ACCOUNT_MANAGEMENT_ID 23 管理账户
     */
    @FileField(order = 23, length = 50)
    private String accountManagementInfo;

    /**
     *
     * va号:VA_NUMBER
     */
    @FileField(order = 24, length = 50)
    private String vaNum;

    @FileField(order = 25, length = 25)
    private String vataCoupleIndicator;

    @Override
    public int compareTo(AccountantGlOriRecordDetail o) {
        return 0;
    }
}
