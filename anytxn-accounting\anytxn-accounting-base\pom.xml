<?xml version="1.0"?>
<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.anytech</groupId>
        <artifactId>anytxn-accounting</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>anytxn-accounting-base</artifactId>
    <description>会计应用-公共基础</description>

    <dependencies>
        <!--        内部依赖start-->
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-parameter-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-business-core-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-business-core-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jrx.anytech</groupId>
            <artifactId>simplified-batch</artifactId>
        </dependency>
        <!--        内部依赖end-->
    </dependencies>
</project>
