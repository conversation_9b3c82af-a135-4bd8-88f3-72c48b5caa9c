package com.anytech.anytxn.account.base.constants;

/**
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 *
 * <AUTHOR>
 */
public enum AutoExchangePaymentType {

    /**
     * 1：直接借记账户自动扣款
     * 9：购汇扣款
     */
    MIN("1", "按最小还款额进行购汇"),

    MAX("2", "按全额进行购汇还款");

    private String code;
    private String reason;


    AutoExchangePaymentType(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }

}
