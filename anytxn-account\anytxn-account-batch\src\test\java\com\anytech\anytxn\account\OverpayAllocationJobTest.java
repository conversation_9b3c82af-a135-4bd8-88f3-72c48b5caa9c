package com.anytech.anytxn.account;

import com.anytech.anytxn.common.core.config.batch.BusinessDbConfiguration;
import com.anytech.anytxn.common.core.config.batch.CommonDbConfiguration;
import com.anytech.anytxn.file.EnableFilePathConfig;
import jrx.anyscheduler.batch.job.single.EnableSingleBatchJobApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;

@EnableSingleBatchJobApplication
@EnableAccountingService
@Import({BusinessDbConfiguration.class, CommonDbConfiguration.class})
@EnableFilePathConfig
public class OverpayAllocationJobTest {

	public static void main(String[] args) {
		ArrayList<String> list = new ArrayList<>();
		list.add("--t.job=overpayAllocationJob");
		list.add("--job.organizationNumber=0001");
		list.add("--debug");
		new SpringApplicationBuilder(OverpayAllocationJobTest.class)
				.web(WebApplicationType.NONE)
				.run(list.toArray(new String[]{}));
	}
}
