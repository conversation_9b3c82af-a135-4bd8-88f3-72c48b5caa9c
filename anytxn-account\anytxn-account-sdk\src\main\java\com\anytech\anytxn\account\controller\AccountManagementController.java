package com.anytech.anytxn.account.controller;

import com.anytech.anytxn.account.base.domain.dto.AccountLimitInfoDTO;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.service.IAccountManageInfoService;
import com.anytech.anytxn.account.base.utils.AnyTxnHttpResponseHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理账户操作
 *
 * <AUTHOR>
 * @date 2018-10-09
 */
@RestController
@Tag(name = "管理账户接口")
public class AccountManagementController extends BizBaseController {
    @Autowired
    private IAccountManageInfoService accountManageInfoService;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;

    @Operation(summary = "更新管理账户信息(批处理)")
    @PutMapping("/account/management/batch")
    public AnyTxnHttpResponse modifyAccountManageInfoBatch(
            @RequestBody AccountManagementInfoDTO accountManagementInfoDTO) {
        accountManageInfoService.modifyAccountManagementInfo(accountManagementInfoDTO);
        return AnyTxnHttpResponseHelper.success(AccountingRepDetailEnum.US.getCnMsg());
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 更新管理账户信息, 前端使用, 不包括批处理
     * @Date 2020-02-18 22:38
     * @Param
     **/
    @Operation(summary = "更新管理账户信息(联机)")
    @PutMapping("/account/management")
    public AnyTxnHttpResponse modifyAccountManageInfo(
            @RequestBody AccountManagementInfoDTO accountManagementInfoDTO) {
        accountManageInfoService.modifyAccountManagementInfoCms(accountManagementInfoDTO);
        return AnyTxnHttpResponseHelper.success(AccountingRepDetailEnum.US.getCnMsg());
    }


    /**
     * 根据客户号查询管理账户
     */
    @Operation(summary = "根据客户号查询管理账户")
    @GetMapping("/account/managements/{customerId}")
    @Parameter(name = "customerId", required = true, description = "客户id")
    AnyTxnHttpResponse<List<AccountManagementInfoDTO>> getAmiByCid(
            @PathVariable("customerId") String customerId) {
        List<AccountManagementInfoDTO> list =
                accountManageInfoService.findListByCustomerId(customerId);
        return AnyTxnHttpResponse.success(list);
    }

    /**
     * 根据扩展参考号查询管理账户
     */
    @Operation(summary = "根据扩展参考号查询管理账户")
    @GetMapping("/account/management/findByExternalReferenceNumber")
    @Parameter(name = "externalReferenceNumber", required = true, description = "扩展参考号-持卡人首张卡")
    AnyTxnHttpResponse<AccountManagementInfoDTO> getAmiByErn(@RequestParam("externalReferenceNumber") String externalReferenceNumber) {
        AccountManagementInfoDTO accountManagementInfo = accountManageInfoService.findAccManInfoByErn(externalReferenceNumber, OrgNumberUtils.getOrg());
        return AnyTxnHttpResponse.success(accountManagementInfo);
    }

    /**
     * 根据客户号查询管理账户
     */
    @Operation(summary = "根据查询类型（卡号-C,证件号：I,管理账号：M）、查询值(卡号/证件号/)，查询管理账户")
    @GetMapping("/account/managements")
    AnyTxnHttpResponse<List<AccountManagementInfoDTO>> getAmiByCid(
            @Parameter(name = "searchType", required = true, description = "搜索类型(（卡号-C,证件号：I，管理账号：M）)")@RequestParam("searchType") String searchType,
            @Parameter(name = "idType", description = "证件类型")@RequestParam("idType") String idType,
            @Parameter(name = "number", required = true, description = "查询值（卡号/证件号/管理账号）")@RequestParam("number") String number,
            @Parameter(name = "partnerId", required = false, description = "第三方机构号")@RequestParam(value = "partnerId", required = false) String partnerId) {
        List<AccountManagementInfoDTO> list =
                accountManageInfoService.findListBySearchTypeAndNum(searchType, idType, number, partnerId);
        return AnyTxnHttpResponse.success(list);
    }

    /**
     * 根据管理账户id查询管理账户明细
     */
    @Operation(summary = "根据管理账户id查询管理c账户明细")
    @GetMapping("/account/management/{accountManagementId}")
    @Parameter(name = "accountManagementId", required = true, description = "管理账户id")
    AnyTxnHttpResponse<AccountManagementInfoDTO> getAmiById(
            @PathVariable("accountManagementId") String accountManagementId) {
        AccountManagementInfoDTO accountManagementInfo =
                accountManageInfoService.findAccManInfo(accountManagementId);
        if (StringUtils.isNotBlank(accountManagementInfo.getCentralBillingGiroCardNumber())){
           CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(accountManagementInfo.getCentralBillingGiroCardNumber(),accountManagementInfo.getOrganizationNumber());
           if (!ObjectUtils.isEmpty(cardAuthorizationInfo)){
               String loyaltyNumber = cardAuthorizationInfo.getCobrandLoyaltyNumber();
               accountManagementInfo.setLoyaltyNumber(loyaltyNumber);
           }
        }
        return AnyTxnHttpResponse.success(accountManagementInfo);

    }

    /**
     * 根据账户号查询客户授权信息
     *
     * @param accountManagementId 账户号码
     * @return HttpApiResponse
     */
    @Operation(summary = "根据账户号查询客户授权信息")
    @GetMapping("/account/management/customer/{accountManagementId}")
    public AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> getCustomerAuthorInfo(
            @Parameter(name = "accountManagementId", description = "账户号")@PathVariable("accountManagementId") String accountManagementId) {
        CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO = accountManageInfoService.findCustomerByAccountManagementId(accountManagementId);
        return AnyTxnHttpResponse.success(customerAuthorizationInfoDTO);
    }


    @Operation(summary = "根据附卡卡号查询账户信息")
    @GetMapping("/account/managements/{cardNumber}/{organizationNumber}/{relationshipIndicator}/{primaryCustomerId}")
    public AnyTxnHttpResponse<List<AccountManagementInfoDTO>> getCustomerAuthorizationInfoByIdAndNumber(
            @Parameter(name = "cardNumber", description = "卡号")@PathVariable("organizationNumber") String organizationNumber,
            @Parameter(name = "organizationNumber", description = "机构号")@PathVariable("cardNumber") String cardNumber,
            @Parameter(name = "relationshipIndicator", description = "主附卡标志")@PathVariable("relationshipIndicator") String relationshipIndicator,
            @Parameter(name = "primaryCustomerId", description = "附卡的主客户号")@PathVariable("primaryCustomerId") String primaryCustomerId) {
        List<AccountManagementInfoDTO> list =
                accountManageInfoService.findsListByCustomerId(primaryCustomerId, relationshipIndicator, cardNumber);
        return AnyTxnHttpResponse.success(list);
    }

    /**
     * 根据机构号加公司客户号查询管理账户
     */
    @Operation(summary = "根据机构号和公司客户号查询管理账户")
    @GetMapping("/account/managementsByOrgAndCorpCusId")
    AnyTxnHttpResponse<List<AccountManagementInfoDTO>> getManageByOrgAndCorpCusId(
            @Parameter(name = "organizationNumber", description = "组织机构号")@RequestParam("organizationNumber") String organizationNumber,
            @Parameter(name = "corporateCustomerId", description = "公司客户号")@RequestParam("corporateCustomerId") String corporateCustomerId) {
        List<AccountManagementInfoDTO> list = accountManageInfoService.findByOrgNumAndCorpCusId(organizationNumber, corporateCustomerId);
        return AnyTxnHttpResponse.success(list);
    }

    @Operation(summary = "根据机构号和卡号查询管理账户额度信息")
    @GetMapping("/account/overview")
    AnyTxnHttpResponse<AccountLimitInfoDTO> getAccountLimitInfoByCardNumber(
            @Parameter(name = "cardNumber", description = "卡号")@RequestParam("cardNumber") String cardNumber) {
        String orgNumber = OrgNumberUtils.getOrg();
        if (StringUtils.isBlank(orgNumber)) {
            return AnyTxnHttpResponse.fail(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST.getCode(), AccountingRepDetailEnum.GMP.getEnMsg());
        }
        AccountLimitInfoDTO accountLimitInfoDTO = accountManageInfoService.getAccountLimitInfoByCardNumber(orgNumber, cardNumber);
        return AnyTxnHttpResponse.success(accountLimitInfoDTO);
    }
    /**
     * 查询额度信息
     */
    @Operation(summary = "根据查询类型和查询号码查询管理账户信息")
    @GetMapping("/account/corpmanagement")
    AnyTxnHttpResponse<List<AccountManagementInfoDTO>> findByCardAndLoyalty(
            @Parameter(name = "organizationNumber", description = "组织机构号")@RequestParam(value = "organizationNumber", required = false) String organizationNumber,
            @Parameter(name = "corporateCustomerId", description = "公司客户id")@RequestParam(value = "corporateCustomerId", required = false) String corporateCustomerId,
            @Parameter(name = "searchType", description = "查询类型")@RequestParam(value = "searchType", required = false) String searchType,
            @Parameter(name = "searchNumber", description = "查询号码")@RequestParam(value = "searchNumber", required = false) String searchNumber) {
        return AnyTxnHttpResponse.success(accountManageInfoService.findByCorpManagement(organizationNumber, corporateCustomerId, searchType, searchNumber));
    }
}
