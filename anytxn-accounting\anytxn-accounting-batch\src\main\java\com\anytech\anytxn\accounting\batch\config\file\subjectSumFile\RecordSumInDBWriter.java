package com.anytech.anytxn.accounting.batch.config.file.subjectSumFile;

import com.anytech.anytxn.business.dao.accounting.mapper.SubjectRecordSumMapper;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = {Exception.class,RuntimeException.class})
public class RecordSumInDBWriter implements ItemWriter<SubjectRecordSum> {
    @Autowired
    private SubjectRecordSumMapper subjectRecordSumMapper;

    @Override
    public void write(Chunk<? extends SubjectRecordSum> chunk) throws Exception {
        List<? extends SubjectRecordSum> items = chunk.getItems();
        //逐条插入筛选好的数据
        items.forEach(subjectRecord->{
            subjectRecordSumMapper.insertSelective(subjectRecord);
        });
    }

}
