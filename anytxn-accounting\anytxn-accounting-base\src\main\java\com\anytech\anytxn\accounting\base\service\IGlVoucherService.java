package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.bo.GenerateVoucherBO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherSumDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;


/**
 * <AUTHOR>
public interface IGlVoucherService {
    /**
     * 根据流水生成会计传票
     * @param accountManagementId
     * @return: java.lang.String
     * @Author: ZXL
     * @date: 2019/10/8
     */
    GenerateVoucherBO amsToVoucher(String accountManagementId, OrganizationInfoResDTO organizationInfo);

    /**
     *
     * 增值税月末结转生成传票
     * @param pmsGlacgn
     * @return: java.lang.String
     * @Author: ZXL
     * @date: 2019/10/9
     */
    TAmsGlvcherSumDTO geneVoucherByGlAcct(TPmsGlacgnDTO pmsGlacgn);
}
