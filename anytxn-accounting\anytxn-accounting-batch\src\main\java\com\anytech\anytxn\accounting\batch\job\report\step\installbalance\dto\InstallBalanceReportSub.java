package com.anytech.anytxn.accounting.batch.job.report.step.installbalance.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/16
 */
@Getter
@Setter
public class InstallBalanceReportSub {

    private List<InstallBalanceReportDetail>  detailList;
    //分期产品ID
    private String productID;
    //分行号
    private String branchNumber;
    //币种
    private String currencyCode;
    //合计笔数
    private Integer subCount;
    //合计金额
    private BigDecimal amountTotal;
    private boolean end = false;

}
