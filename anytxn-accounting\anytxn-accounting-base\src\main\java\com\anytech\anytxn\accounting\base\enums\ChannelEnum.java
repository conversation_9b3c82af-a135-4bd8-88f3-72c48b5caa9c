package com.anytech.anytxn.accounting.base.enums;

/**
 * <AUTHOR>
 * @date 2020/9/10
 */
public enum  ChannelEnum {
    C("C","银联"),
    V("V","Visa"),
    <PERSON>("M","Mater<PERSON>ard"),
    J("J","JCb"),
    A("A", "Amex"),
    L("L", "本地输入"),
    z("0", "本地输入"),
    o("1", "行内"),
    T("2", "内生交易");

    ChannelEnum(String code, String des) {
        this.code = code;
        this.des = des;
    }

    private String code;
    private String des;

    public String getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static String getDes(String code){
        for (ChannelEnum value : ChannelEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDes();
            }
        }
        return ChannelEnum.L.getDes();
    }
}
