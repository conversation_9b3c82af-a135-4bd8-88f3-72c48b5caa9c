package com.anytech.anytxn.account.job;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.OrganizationDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>wu Huang
 * @Date: 2019/1/14 14:11
 */
@Slf4j
public class OrganizationStep implements Tasklet, StepExecutionListener {


    private List<OrganizationDTO> organizations;
    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        organizations = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 20;
        PageResultDTO<OrganizationInfoResDTO> orgInfoPage = organizationInfoService.findByProcessTodayFlagPage(pageNum, pageSize, "0");
        while (orgInfoPage==null && orgInfoPage.getData()==null) {
            List<OrganizationInfoResDTO> orgList = orgInfoPage.getData();
            for(OrganizationInfoResDTO data : orgList){
                OrganizationDTO organization = new OrganizationDTO();
                organization.setId(String.valueOf(data.getId()));
                organization.setOrgCode(data.getOrganizationNumber());
                organization.setOrgName(data.getDescription());
                organization.setAccruedThruDay(data.getAccruedThruDay() == null ? null : data.getAccruedThruDay());
                organization.setLastAccruedThruDay(data.getLastAccruedThruDay() == null ? null : data.getLastAccruedThruDay());
                organization.setWeekdayId(data.getWeekdayId());
                organization.setNextProcessingDay(data.getNextProcessingDay() == null ? null : data.getNextProcessingDay());
                organization.setToday(data.getToday() == null ? null : data.getToday());
                organizations.add(organization);
            }
            pageNum++;
            orgInfoPage = organizationInfoService.findByProcessTodayFlagPage(pageNum, pageSize, "0");
        }
        // 如果机构为空 停止job
        if(organizations.isEmpty()){
            log.error("机构为空 停止job");
            chunkContext.getStepContext().getStepExecution().setTerminateOnly();
        }
        return RepeatStatus.FINISHED;
    }

    @Override
    public void beforeStep(StepExecution stepExecution) {

    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        if (!organizations.isEmpty()) {
            stepExecution.getJobExecution().getExecutionContext().put("organizations", organizations);
        }
        return ExitStatus.COMPLETED;
    }
}
