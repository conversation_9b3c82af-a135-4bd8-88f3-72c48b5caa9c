package com.anytech.anytxn.account.controller;

import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.service.IAccountOptinalInfoService;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.account.domain.dto.AccountOptionalReqDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountOptionalResDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账户配置信息
 *
 * <AUTHOR>
 * @date 2018-10-09
 */
@RestController
@Tag(name = "管理账户配置信息接口")
public class AccountOptinalInfoController extends BizBaseController {
    @Autowired
    private IAccountOptinalInfoService accountOptinalInfoService;


    /**
     * 根据accountManagementId查询一条数据
     * @param accountManagementId 管理账户Id
     * @return AccountOptionalResDTO 账户配置信息DTO
     */
    @Operation(summary = "根据taccountManagementId查询所有账户配置信息",description = "根据taccountManagementId查询所有账户配置信息")
    @GetMapping(value = "/account/optinal/findAccountOptinalByAccountManagementId/{accountManagementId}")
    public AnyTxnHttpResponse<AccountOptionalResDTO> findAccountOptinalByAccountManagementId(@PathVariable(value = "accountManagementId")String accountManagementId){
        AccountOptionalResDTO accountOptionalResDTO = accountOptinalInfoService.findByAccountManagementId(accountManagementId);
        return AnyTxnHttpResponse.success(accountOptionalResDTO);
    }

    /**
     * 新增一条账户配置信息
     * @param AccountOptionalReqDTO 管理账户Id
     * @return AccountOptionalResDTO 账户配置信息DTO
     */
    @Operation(summary = "新增enrollDate")
    @PostMapping(path = "/account/insertAccountOptinalInfo")
    public AnyTxnHttpResponse insertAccountOptinalInfo(@RequestBody AccountOptionalReqDTO AccountOptionalReqDTO) {
        accountOptinalInfoService.insertAccountOptinalInfo(AccountOptionalReqDTO);
        return AnyTxnHttpResponse.successDetail(AccountingRepDetailEnum.BLK_SUCCESS.getCnMsg());
    }


    /**
     * 修改账户配置信息中的cancelDate
     * @param AccountOptionalReqDTO 管理账户Id
     * @return AccountOptionalResDTO 账户配置信息DTO
     */
    @Operation(summary = "修改enrollDate")
    @PutMapping(path = "/account/modifyAccountOptinalInfo")
    public AnyTxnHttpResponse modifyAccountOptinalInfo(@RequestBody AccountOptionalReqDTO AccountOptionalReqDTO) {
        accountOptinalInfoService.modifyAccountOptinalInfo(AccountOptionalReqDTO);
        return AnyTxnHttpResponse.successDetail(AccountingRepDetailEnum.UN_BLK_SUCCESS.getCnMsg());
    }


    /**
     * 根据accountManagementId查询一条数据
     * @param condition C客农户层 A账户层
     * @return AccountOptionalResDTO 账户配置信息DTO
     */
    @Operation(summary = "根据accountManagementId查询所有账户配置信息",description = "根据taccountManagementId查询所有账户配置信息")
    @GetMapping(value = "/account/optinal/findAccountOptinal/{accountManagementId}/{condition}")
    public AnyTxnHttpResponse<List<AccountOptionalResDTO>> findAccountOptionalAccountOptinalByCondition(@PathVariable(value = "condition")String condition  ,@PathVariable(value = "accountManagementId")String id){
        List<AccountOptionalResDTO> accountOptionalResDTO = accountOptinalInfoService.findByCondition(condition ,id);
        return AnyTxnHttpResponse.success(accountOptionalResDTO);
    }





}
