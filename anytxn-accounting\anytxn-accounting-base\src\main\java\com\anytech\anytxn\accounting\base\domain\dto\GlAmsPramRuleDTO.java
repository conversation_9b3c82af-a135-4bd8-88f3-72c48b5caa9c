package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.io.Serializable;

/**
 * @description: 会计参数拆分规则
 * @author: ZXL
 * @create: 2020-02-14 20:24
 */
@Getter
@Setter
@ToString
public class GlAmsPramRuleDTO extends BaseEntity implements Serializable  {
    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    private String acctLogo;
    /**
     * 交易码
     * 表字段:TXN_CODE
     */
    private String txnCode;

    /**
     * 表内表外标识
     * 表字段:INTEREST_IND
     */
    private String interestInd;

    /**
     * 价税分离标识
     * 表字段:PRICE_TAX_FLG
     */
    private String priceTaxFlg;
    /**
     * 核算状态
     * 表字段:FINANCE_STATUS
     */
    private String financeStatus;

    /**
     * 交易属性
     */
    private String transactionAttribute;
    /**
     * 借贷记属性
     */
    private String debitCreditIndicator;

    /**
     * 递延标识
     */
    private String amortizeInd;

    private String balType;
    private String orderId;

    /**
     * 不良abs标记
     */
    private String npasFirst;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getBalType() {
        return balType;
    }

    public void setBalType(String balType) {
        this.balType = balType;
    }

    public String getTransactionAttribute() {
        return transactionAttribute;
    }

    public void setTransactionAttribute(String transactionAttribute) {
        this.transactionAttribute = transactionAttribute;
    }

    public String getDebitCreditIndicator() {
        return debitCreditIndicator;
    }

    public void setDebitCreditIndicator(String debitCreditIndicator) {
        this.debitCreditIndicator = debitCreditIndicator;
    }

    public String getAmortizeInd() {
        return amortizeInd;
    }

    public void setAmortizeInd(String amortizeInd) {
        this.amortizeInd = amortizeInd;
    }

    public String getAcctLogo() {
        return acctLogo;
    }

    public void setAcctLogo(String acctLogo) {
        this.acctLogo = acctLogo;
    }



    public String getTxnCode() {
        return txnCode;
    }

    public void setTxnCode(String txnCode) {
        this.txnCode = txnCode;
    }

    public String getInterestInd() {
        return interestInd;
    }

    public void setInterestInd(String interestInd) {
        this.interestInd = interestInd;
    }

    public String getPriceTaxFlg() {
        return priceTaxFlg;
    }

    public void setPriceTaxFlg(String priceTaxFlg) {
        this.priceTaxFlg = priceTaxFlg;
    }

    public String getFinanceStatus() {
        return financeStatus;
    }

    public void setFinanceStatus(String financeStatus) {
        this.financeStatus = financeStatus;
    }

    public String getNpasFirst() {
        return npasFirst;
    }

    public void setNpasFirst(String npasFirst) {
        this.npasFirst = npasFirst;
    }
}