package com.anytech.anytxn.accounting.batch.config;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/14
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.report.customer-detail")
public class CustomerDetailReportConfigurer extends BaseDMQYConfigurer{

    /**
     * 表头参数
     */
    public void putParam(Map<String, Object> params, TimeTypeEnum timeTypeEnum){
        String num = "";
        switch (timeTypeEnum) {
            case D: num = "R019";
                break;
            case M: num = "R020";
                break;
            case Q: num = "R021";
                break;
            case Y: num = "R022";
                break;
        }


        params.put("reportNumber", num);
        params.put("reportName", "每" + timeTypeEnum.getDes() + "客户落账明细报表");
    }
}
