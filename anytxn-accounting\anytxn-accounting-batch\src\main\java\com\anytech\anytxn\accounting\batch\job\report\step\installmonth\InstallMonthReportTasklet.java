package com.anytech.anytxn.accounting.batch.job.report.step.installmonth;

import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.installmonth.model.InstallMonthReport;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallProductInfo;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoSelfMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.job.report.step.installmonth.dto.OrderPlan;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 每月分期付款出账
 * <AUTHOR>
 * @date 2020/9/11
 */
@Slf4j
public class InstallMonthReportTasklet extends AbstractReportTasklet<InstallPlan, OrderPlan> {

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private InstallPlanSelfMapper installPlanSelfMapper;
    @Autowired
    private InstallProductInfoSelfMapper installProductInfoSelfMapper;
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    public InstallMonthReportTasklet(BaseConfigurer baseConfigurer) {
        super(baseConfigurer);
    }

    @Override
    protected List<InstallPlan> loadModels() {
        OrganizationInfoResDTO organizationInfo =
                organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

//        organizationInfo.setToday(LocalDate.of(2020,4,30));
        if (TimeUtils.isEndMonth(organizationInfo.getToday())) {
            Pair<LocalDate, LocalDate> mothRand = TimeUtils.mothRand(organizationInfo.getToday());
            return installPlanSelfMapper.selectByPostDate(mothRand.getKey(), mothRand.getValue());
        }
        return null;
    }

    @Override
    protected List<OrderPlan> modelToReport(List<InstallPlan> models) {
        List<InstallMonthReport> result = Lists.newArrayList();
        Map<String, InstallOrder> orderMap = Maps.newHashMap();
        for (InstallPlan installPlan : models) {

            InstallMonthReport reportDTO = new InstallMonthReport();
            reportDTO.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            // 卡号
            InstallOrder installOrder = orderMap.containsKey(installPlan.getOrderId()) ? orderMap.get(installPlan.getOrderId()) : installOrderMapper.selectByPrimaryKey(installPlan.getOrderId());
            orderMap.put(installPlan.getOrderId(), installOrder);
            reportDTO.setCardNumber(installOrder.getCardNumber());

            reportDTO.setOrderId(installPlan.getOrderId());
            reportDTO.setProductCode(installOrder.getProductCode());
            reportDTO.setOrganizationNumber(installPlan.getOrganizationNumber());
            reportDTO.setCurrCode(installOrder.getInstallmentCcy());
            reportDTO.setFreeAmount(installPlan.getFeeAmount());
            reportDTO.setTermAmount(installPlan.getTermAmount());
            //TODO 改成月
            reportDTO.setTermPostDate(installPlan.getTermPostDate());
            InstallProductInfo productInfo = installProductInfoSelfMapper.selectByIndex(installOrder.getOrganizationNumber(), installOrder.getProductCode());
            reportDTO.setDescription(productInfo.getProductDesc());

            result.add(reportDTO);
        }

        return Lists.newArrayList(OrderPlan.resetReportData(result));
    }
}
