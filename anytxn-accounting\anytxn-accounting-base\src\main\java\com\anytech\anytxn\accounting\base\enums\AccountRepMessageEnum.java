package com.anytech.anytxn.accounting.base.enums;

import com.anytech.anytxn.common.core.constants.MessageHandle;

/**
 * 响应描述
 * <AUTHOR>
 * @date 2021/2/26
 */
public enum AccountRepMessageEnum implements MessageHandle {
    SUCCESS("成功", "success"),
    BATCH_MODE_0("批中不支持调账", "running batch can not account adjustment"),
    PARAM_NULL("参数为空","param is null"),
    DATE_FORMAT("日期格式错误","date format error"),
    PARAM_FORMAT("参数格式化错误", "param format error"),
    GLACGN_NULL("科目号不存在", "subject not exist "),
    LOAN_CURRCODE("含有借贷不平币种","have loan currCode"),
    DATABASE_ERROR("数据库操作异常", "dataBase error"),
    NET_SUITE_ERROR("调用netsuite服务异常", "netsuite error"),
    ;

    private String CnMsg;
    private String EnMsg;

    AccountRepMessageEnum(String cnMsg, String enMsg) {
        CnMsg = cnMsg;
        EnMsg = enMsg;
    }

    @Override
    public String getCnMsg() {
        return CnMsg;
    }

    @Override
    public String getEnMsg() {
        return EnMsg;
    }
}
