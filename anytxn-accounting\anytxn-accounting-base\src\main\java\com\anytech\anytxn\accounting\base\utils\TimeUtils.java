package com.anytech.anytxn.accounting.base.utils;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 * @date 2020/9/13
 */
public class TimeUtils {

    /**
     * 判断是否月末
     */
    public static boolean isEndMonth(LocalDate localDate){
        return localDate.getDayOfMonth() == localDate.with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
    }

    /**
     * 当前月的开始日期-结束日期
     */
    public static Pair<LocalDate, LocalDate> mothRand(LocalDate localDate){
        return Pair.of(localDate.with(TemporalAdjusters.firstDayOfMonth()), localDate.with(TemporalAdjusters.lastDayOfMonth()));
    }

    /**
     * 是否是季度末
     */
    public static boolean isEndQuarter(LocalDate localDate){
        // 当前月是3/6/9/12 且是月末
        return StringUtils.equalsAny(String.valueOf(localDate.getMonthValue()), "3","6","9","12") && isEndMonth(localDate);
    }

    /**
     * 当前季度的开始日期-结束日期
     */
    public static Pair<LocalDate, LocalDate> quarterRand(LocalDate localDate){
        return Pair.of(localDate.minusMonths(2).with(TemporalAdjusters.firstDayOfMonth()), localDate.with(TemporalAdjusters.lastDayOfMonth()));
    }

    /**
     * 是否是年末
     */
    public static boolean isEndYear(LocalDate localDate){
        return localDate.getMonthValue() == 12 && localDate.getDayOfMonth() == 31;
    }

    /**
     * 当前年的开始日期-结束日期
     */
    public static Pair<LocalDate, LocalDate> yearRand(LocalDate localDate){
        return Pair.of(LocalDate.of(localDate.getYear(), 1, 1), LocalDate.of(localDate.getYear(), 12, 31));
    }

    /**
     * 获取日期字符串
     */
    public static String dateToStr(LocalDate date, TimeTypeEnum timeTypeEnum){
        switch (timeTypeEnum){
            case D:
                return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case M:
            case Q:
                return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            case Y:
                return date.format(DateTimeFormatter.ofPattern("yyyy"));
            default:
                return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
    }

//    public static void main(String[] args) {
//        LocalDate date = LocalDate.of(2020, 6, 3);
//
//        System.out.println(date.with(TemporalAdjusters.lastDayOfMonth()));
//        System.out.println(date);
//        System.out.println(date.with(TemporalAdjusters.firstDayOfMonth()));
//
//        System.out.println(date.minusMonths(2).with(TemporalAdjusters.firstDayOfMonth()));
//    }
}
