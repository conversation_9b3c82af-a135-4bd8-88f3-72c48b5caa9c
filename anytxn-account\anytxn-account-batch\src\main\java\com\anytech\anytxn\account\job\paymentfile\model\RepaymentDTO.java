package com.anytech.anytxn.account.job.paymentfile.model;

import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 还款文件格式
 * <AUTHOR>
 * @date 2021/1/8
 */
@Getter
@Setter
@NoArgsConstructor
public class RepaymentDTO extends BaseEntity {

    /**
     * 银行编号
     */
    private String bank;

    /**
     * 记录产生日期
     */
    private String dateCr;

    /**
     * 扣账账户号码
     */
    private String debitAcct;

    /**
     * 扣账金额
     */
    private String amtCr;

    /**
     * 扣账币种
     */
    private String curr;

    /**
     * 扣账日期
     */
    private String datePr;

    /**
     * 实际扣账金额
     */
    private String amtActual;

    /**
     * 信用卡卡号
     */
    private String cardNbr;

    /**
     * 处理响应码
     */
    private String code;

    /**
     * 借记卡证件类型
     */
    private String idType;

    /**
     * 借记卡证件号码
     */
    private String idNo;

    /**
     * 借记卡姓名
     */
    private String tranNo;

    /**
     * 处理字段，是否为异常行
     */
    private boolean errorLine;

    public BigDecimal getAmtCr(){
        if (StringUtils.isNotEmpty(amtCr)) {
            return new BigDecimal(amtCr).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
        }
        return new BigDecimal(0);
    }

    public BigDecimal getAmtActual(){
        if (StringUtils.isNotEmpty(amtActual)) {
            return new BigDecimal(amtActual).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
        }
        return new BigDecimal(0);
    }

    public RepaymentDTO(Boolean errorLine) {
        this.errorLine = errorLine;
    }

}
