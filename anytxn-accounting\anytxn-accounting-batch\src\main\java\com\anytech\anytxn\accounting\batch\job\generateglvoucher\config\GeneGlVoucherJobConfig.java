package com.anytech.anytxn.accounting.batch.job.generateglvoucher.config;

import com.anytech.anytxn.accounting.base.domain.bo.GenerateVoucherBO;
import com.anytech.anytxn.accounting.batch.job.generateglvoucher.partitioner.VoucherPartitioner;
import com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep.AmsToVoucherReader;
import com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep.AmsToVoucherWriter;
import com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep.AmsToVoucherProcessor;
import com.anytech.anytxn.common.core.constants.PropsConstant;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.partition.PartitionHandler;
import org.springframework.batch.core.partition.support.TaskExecutorPartitionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import javax.sql.DataSource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * 会计传票处理任务
 *
 * <AUTHOR>
 * @date 2019-09-18 16:56
 **/
@Configuration
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class GeneGlVoucherJobConfig {
    private static final Logger logger = LoggerFactory.getLogger(GeneGlVoucherJobConfig.class);

    @Autowired
    private JobBuilderFactory jobBuilderFactory;

    @Autowired
    private StepBuilderFactory stepBuilderFactory;
    @Resource
    private IOrganizationInfoService organizationInfoService;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.geneGlVoucherJob.amsToVoucherStep.chunk-limit:100}")
    private Integer amsToVoucherChunk;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.geneglvoucherJob.amsToVoucherStep.grid-size:1000}")
    private Integer amsGlamsGridSize;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.geneglvoucherJob.amsToVoucherPartition.grid-size:1}")
    private Integer masterVoucherBatchGridSize;

    /**
     * 会计传票相关处理任务
     * @param masterVoucherBatchStep    第一步传票分录与检查
     * @return
     */
    @Bean
    public Job geneGlVoucherJob(@Qualifier("masterVoucherBatchStep") Step masterVoucherBatchStep) {
        return jobBuilderFactory.get("geneGlVoucherJob")
                .start(masterVoucherBatchStep)
                .build();
    }

    @Bean
    public Step masterVoucherBatchStep(@Qualifier("amsToVoucherStep") Step amsToVoucherStep,
                                       @Qualifier("voucherPartitioner") VoucherPartitioner voucherPartitioner) {
        return stepBuilderFactory.get("masterVoucherBatchStep")
                .partitioner(amsToVoucherStep.getName(), voucherPartitioner)
                .partitionHandler(voucherBatchMasterSlaveHandler(amsToVoucherStep))
                .build();
    }

    /**
     * 会计传票处理
     * @param amsToVoucherReader
     * @param amsToVoucherProcessor
     * @param amsToVoucherWriter
     * @return
     */
    @Bean
    public Step amsToVoucherStep(@Qualifier("amsToVoucherReader") AmsToVoucherReader amsToVoucherReader,
                                 @Qualifier("amsToVoucherProcessor") AmsToVoucherProcessor amsToVoucherProcessor,
                                 @Qualifier("amsToVoucherWriter") AmsToVoucherWriter amsToVoucherWriter) {
        return stepBuilderFactory.get("amsToVoucherStep").<AccountantGlams, GenerateVoucherBO>chunk(amsToVoucherChunk)
                .reader(amsToVoucherReader)
                .processor(amsToVoucherProcessor)
                .writer(amsToVoucherWriter)
                .build();
    }

    @Bean
    @StepScope
    public AmsToVoucherReader amsToVoucherReader(
            @Qualifier("businessDataSource") DataSource dataSource,
            @Value("#{stepExecutionContext['fromId']}") String fromId,
            @Value("#{stepExecutionContext['endId']}") String endId,
            @Value("#{jobParameters['job.partitionKey']}") String partitionKey,
            @Value("#{jobParameters['job.today']}") String today) {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        if (StringUtils.isNotBlank(today)) {

            organizationInfo.setAccruedThruDay(LocalDate.of(Integer.parseInt(today.substring(0, 4)),
                    Integer.parseInt(today.substring(4, 6)), Integer.parseInt(today.substring(6, 8))));

        }


        AmsToVoucherReader reader = new AmsToVoucherReader(partitionKey, dataSource, fromId, endId,organizationInfo);
        reader.setDataSource(dataSource);
        // 设置页码大小
        reader.setPageSize(amsGlamsGridSize);
        // 记录断点
        reader.setSaveState(true);
        Map<String, Object> parameters = new HashMap<>(4);
        parameters.put("fromId", fromId);
        parameters.put("endId", endId);
        reader.setParameterValues(parameters);
        return reader;
    }

    @Bean
    @StepScope
    public AmsToVoucherProcessor amsToVoucherProcessor(@Value("#{jobParameters['job.today']}") String today) {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        if (StringUtils.isNotBlank(today)) {

            organizationInfo.setAccruedThruDay(LocalDate.of(Integer.parseInt(today.substring(0, 4)),
                    Integer.parseInt(today.substring(4, 6)), Integer.parseInt(today.substring(6, 8))));

        }

        return new AmsToVoucherProcessor(organizationInfo);
    }

    @Bean
    @StepScope
    public AmsToVoucherWriter amsToVoucherWriter() {
        return new AmsToVoucherWriter();
    }

    @Bean
    @StepScope
    public VoucherPartitioner voucherPartitioner() {
        return new VoucherPartitioner();
    }

    /**
     * 生成传票任务分区处理器
     * @param step
     * @return
     */
    private PartitionHandler voucherBatchMasterSlaveHandler(Step step) {
        TaskExecutorPartitionHandler handler = new TaskExecutorPartitionHandler();
        handler.setGridSize(masterVoucherBatchGridSize);
        handler.setTaskExecutor(taskExecutor());
        handler.setStep(step);
        try {
            handler.afterPropertiesSet();
        } catch (Exception e) {
            logger.error("method:voucherBatchMasterSlaveHandler,exception:{}", e);
        }
        return handler;
    }

    @Bean
    public TaskExecutor taskExecutor() {
        return new SimpleAsyncTaskExecutor("spring_batch");
    }

}
