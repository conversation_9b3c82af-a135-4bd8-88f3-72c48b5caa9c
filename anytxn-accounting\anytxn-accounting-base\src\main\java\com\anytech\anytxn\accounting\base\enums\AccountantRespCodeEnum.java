package com.anytech.anytxn.accounting.base.enums;


/**
 * <AUTHOR>
 * @date 2020/3/16
 *
 * Accountant响应码枚举类，遵循AntTxn响应码规范
 */
public enum AccountantRespCodeEnum implements AccountantRespCodePrefix {

    /**
     * 正常响应
     */
    REQ_SUCCESS(PREFIX_ACCOUNTANT_N, AccountRepMessageEnum.SUCCESS, "response success"),

    /**
     * 参数异常
     */
    P_BATCH_MODE_0_FAIL(P_BATCHMODE_0, AccountRepMessageEnum.BATCH_MODE_0, ""),
    P_PARAM_EMPTY_FAIL(P_PARAM_EMPTY, AccountRepMessageEnum.PARAM_NULL, "param empty"),
    P_DATE_FORMAT_FAIL(P_DATE_FORMAT, AccountRepMessageEnum.DATE_FORMAT, "date format error"),
    P_PARAM_FORMAT_FAIL(P_PARAM_FORMAT, AccountRepMessageEnum.PARAM_FORMAT, ""),
    P_GLACGN_EMPTY_FAIL(P_GLACGN_EMPTY, AccountRepMessageEnum.GLACGN_NULL,"glacgm is empty"),
    P_LOAN_CURRCODE_FAIL(P_LOAN_CURRCOD, AccountRepMessageEnum.LOAN_CURRCODE,""),

    P_NET_SUITE_FAIL(P_LOAN_CURRCOD, AccountRepMessageEnum.NET_SUITE_ERROR,"net suite error"),


    /**
     * 数据库操作异常
     */
    D_DATABASE_FAULT(D_DATABASE_ERROR, AccountRepMessageEnum.DATABASE_ERROR, "database exception");

    /**
     * 响应码编码,格式为 3位业务编码+2位响应码类型编码+5位具体响应码
     */
    private String code;
    /**
     * 通用描述
     */
    private AccountRepMessageEnum msg;
    /**
     * 异常详情描述
     */
    private String detail;

    AccountantRespCodeEnum(String code, AccountRepMessageEnum msg, String detail) {
        this.code = code;
        this.msg = msg;
        this.detail = detail;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg.message();
    }

    @Override
    public String getDetail() {
        return this.detail;
    }
}
