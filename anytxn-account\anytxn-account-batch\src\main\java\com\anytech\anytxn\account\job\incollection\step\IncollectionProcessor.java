package com.anytech.anytxn.account.job.incollection.step;

import com.anytech.anytxn.account.base.service.IAccountManageInfoService;
import com.anytech.anytxn.business.base.account.domain.dto.InCollectionDTO;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @date 2019-09-02
 **/
public class IncollectionProcessor implements ItemProcessor<AccountManagementInfo, InCollectionDTO> {
    @Autowired
    private IAccountManageInfoService accountManageInfoService;

    @Override
    public InCollectionDTO process(AccountManagementInfo accountManagementInfo) throws Exception {
        return accountManageInfoService.batchProcess4InCollection(accountManagementInfo);
    }
}
