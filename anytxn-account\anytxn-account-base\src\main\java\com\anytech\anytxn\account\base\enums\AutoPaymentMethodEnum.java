package com.anytech.anytxn.account.base.enums;

/**
 * 约定扣款方式
 *
 * <AUTHOR>
 * @date 2018-9-22
 *
 */
public enum AutoPaymentMethodEnum {

    /**
     * 0：按全额进行约定扣款
     * 1：按最小还款额进行约定扣款
     */
    ALL("0", "按全额进行约定扣款"),

    MIN("1", "按最小还款额进行约定扣款");

    private String code;
    private String reason;


    AutoPaymentMethodEnum(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }
}
