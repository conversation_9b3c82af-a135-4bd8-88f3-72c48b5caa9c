package com.anytech.anytxn.accounting.batch.job.journal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: sukang
 * @Date: 2022/11/22 14:43
 */
@Setter
@Getter
@ToString
public class SignatureDTO {

    String httpMethod;

    String url;

    String timestamp;

    String nonce;


    public static final class SignatureDTOBuilder {
        String httpMethod;
        String url;

        private SignatureDTOBuilder() {
        }

        public static SignatureDTOBuilder aSignatureDTO() {
            return new SignatureDTOBuilder();
        }

        public SignatureDTOBuilder withHttpMethod(String httpMethod) {
            this.httpMethod = httpMethod;
            return this;
        }

        public SignatureDTOBuilder withUrl(String url) {
            this.url = url;
            return this;
        }

        public SignatureDTO build() {
            SignatureDTO signatureDTO = new SignatureDTO();
            signatureDTO.setHttpMethod(httpMethod);
            signatureDTO.setUrl(url);
            return signatureDTO;
        }
    }
}
