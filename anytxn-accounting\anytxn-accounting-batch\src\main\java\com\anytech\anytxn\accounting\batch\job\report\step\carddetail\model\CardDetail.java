package com.anytech.anytxn.accounting.batch.job.report.step.carddetail.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2020/9/14
 */
@Getter
@Setter
public class CardDetail {

    /**
     * 卡组织
     */
    private String cardBin;

    /**
     * 卡产品
     */
    private String cardProductNumber;

    /**
     * 卡产品名称
     */
    private String cardProductName;

    /**
     * 币种
     */
    private String currCode;

    /**
     * 交易码
     */
    private String txnCodeOrig;

    /**
     * 描述
     */
    private String description;

    /**
     * 入账日期
     */
    private LocalDate postDate;

    /**
     * 科目号
     */
    private String glAcct;

    /**
     * 科目名称
     */
    private String glAcctName;

    /**
     * 借贷方向
     */
    private String drcr;

    /**
     * 发生额
     */
    private BigDecimal glAmount;
}
