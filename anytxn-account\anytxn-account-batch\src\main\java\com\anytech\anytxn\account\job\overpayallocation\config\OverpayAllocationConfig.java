package com.anytech.anytxn.account.job.overpayallocation.config;

import com.anytech.anytxn.account.job.overpayallocation.step.OverpayAllocationProcessor;
import com.anytech.anytxn.account.job.overpayallocation.step.OverpayAllocationReader;
import com.anytech.anytxn.account.job.overpayallocation.step.OverpayAllocationWriter;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * 原账户间溢缴款分配批量配置
 * 借贷分离改为：账户间/内贷方余额分配
 *
 * <AUTHOR>
 * @date 2020-10-20 14:56
 **/
@Configuration
public class OverpayAllocationConfig {
    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;

    @Value("${anytxn.batch.stmtProcessJob.stmtMasterStep.grid-size:1}")
    private Integer gridSize;

    @Value("${anytxn.batch.stmtProcessJob.stmtStep.chunk-limit:1}")
    private Integer chunkLimit;

    @Value("${anytxn.batch.stmtProcessJob.stmtStep.page-size:1}")
    private Integer pageSize;

    /**
     * 账户间溢缴款分配批量 Job配置
     * @param 
     * @return
     */
    @Bean
    public Job overpayAllocationJob(@Qualifier("overpayAllocationStep") Step overpayAllocationStep) {
        return jobs.get("overpayAllocationJob")
                .start(overpayAllocationStep)
                .build();
    }


    /**
     * 账户间溢缴款分配批量 slaveStep
     */
    @Bean
    public Step overpayAllocationStep(
            @Qualifier("overpayAllocationReader") OverpayAllocationReader overpayAllocationReader,
            @Qualifier("overpayAllocationProcess") OverpayAllocationProcessor overpayAllocationProcess,
            @Qualifier("overpayAllocationWriter") OverpayAllocationWriter overpayAllocationWriter
    ) {
        return steps.get("overpayAllocationStep").<CustReconciliationControlDTO, CustReconciliationControlDTO>chunk(chunkLimit)
                .reader(overpayAllocationReader).processor(overpayAllocationProcess)
                .writer(overpayAllocationWriter).build();
    }

    @Bean
    @StepScope
    public OverpayAllocationReader overpayAllocationReader(@Qualifier("businessDataSource") DataSource dataSource,
                                                       @Value("#{stepExecutionContext['fromId']}") String fromId,
                                                       @Value("#{stepExecutionContext['endId']}") String endId
                                                       ) {
        OverpayAllocationReader reader = new OverpayAllocationReader(dataSource);
        reader.setDataSource(dataSource);
        reader.setPageSize(pageSize);
        //记录状态信息和断点
        reader.setSaveState(true);
        return reader;
    }

    @Bean
    @StepScope
    public OverpayAllocationProcessor overpayAllocationProcess() {
        return new OverpayAllocationProcessor();
    }

    @Bean
    @StepScope
    public OverpayAllocationWriter overpayAllocationWriter() {
        return new OverpayAllocationWriter();
    }


}
