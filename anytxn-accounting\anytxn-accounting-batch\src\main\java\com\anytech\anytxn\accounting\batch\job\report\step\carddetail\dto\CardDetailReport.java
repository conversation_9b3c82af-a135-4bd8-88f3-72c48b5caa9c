package com.anytech.anytxn.accounting.batch.job.report.step.carddetail.dto;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.carddetail.model.CardDetail;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/16
 */
@Getter
@Setter
public class CardDetailReport {

    /**
     * 卡组织
     */
    private String cardOrganization;

    /**
     * 产品
     */
    private String productId;

    /**
     * 卡产品名称
     */
    private String productName;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 交易码
     */
    private String transactionCode;

    /**
     * 交易描述
     */
    private String transactionDescription;

    /**
     * 入账日期
     */
    private String postDate;

    /**
     * 科目号
     */
    private String subjectNumber;

    /**
     * 科目名称
     */
    private String subjectName;

    /**
     * 借贷方向
     */
    private String debitCreditIndcator;

    /**
     * 发生额
     */
    private BigDecimal createAmount;


    public static List<CardDetailReport> resetReportData(List<CardDetail> data, TimeTypeEnum timeTypeEnum){
        return data.parallelStream().map(x-> {
            CardDetailReport detail = new CardDetailReport();
            detail.setCardOrganization(x.getCardBin());
            detail.setProductId(x.getCardProductNumber());
            detail.setProductName(x.getCardProductName());
            detail.setCurrencyCode(x.getCurrCode());
            detail.setTransactionCode(x.getTxnCodeOrig());
            detail.setTransactionDescription(x.getDescription());
            detail.setPostDate(TimeUtils.dateToStr(x.getPostDate(), timeTypeEnum));
            detail.setSubjectNumber(x.getGlAcct());
            detail.setSubjectName(x.getGlAcctName());
            detail.setDebitCreditIndcator(x.getDrcr());
            detail.setCreateAmount(x.getGlAmount());

            return detail;
        }).sorted(Comparator.comparing(x-> x.getCardOrganization() + x.getProductId())).collect(Collectors.toList());
    }
}
