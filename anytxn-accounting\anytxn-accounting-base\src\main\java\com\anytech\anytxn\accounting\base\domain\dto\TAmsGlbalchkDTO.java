package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Getter
@Setter
public class TAmsGlbalchkDTO extends BaseEntity {

    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 币种
     * 表字段:CURR_CODE
     */
    private String currCode;

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    private String glAcct;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    private LocalDate postingDate;

    /**
     * 总账余额
     * 表字段:GL_BALANCE
     */
    private BigDecimal glBalance;

    /**
     * 分户余额
     * 表字段:AC_BALANCE
     */
    private BigDecimal acBalance;

    /**
     * 总分差额
     * 表字段:GL_GAP_BAL
     */
    private BigDecimal glGapBal;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;
}
