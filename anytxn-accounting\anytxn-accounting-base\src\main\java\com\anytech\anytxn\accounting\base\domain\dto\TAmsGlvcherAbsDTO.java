package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Getter
@Setter
public class TAmsGlvcherAbsDTO extends BaseEntity {
    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    private String accountManagementId;

    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    private String acctLogo;

    /**
     * 全局业务流水号
     * 表字段:GLOBAL_FLOW_NO
     */
    private String globalFlowNo;

    /**
     * 模块标识
     * 表字段:MODULE_FLAG
     */
    private String moduleFlag;

    /**
     * 交易码
     * 表字段:VP_TXN_CODE
     */
    private String vpTxnCode;

    /**
     * 币种
     * 表字段:CURR_CODE
     */
    private String currCode;

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    private String glAcct;

    /**
     * 借贷方向
     * 表字段:DRCR
     */
    private String drcr;

    /**
     * 交易金额
     * 表字段:GL_AMOUNT
     */
    private BigDecimal glAmount;

    /**
     * 入账笔数
     * 表字段:POSTING_CNT
     */
    private BigDecimal glCnt;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    private LocalDate postingDate;

    /**
     * 处理标识
     * 表字段:PROCESS_TYPE
     */
    private String processType;

    /**
     * 分期订单号
     * 表字段:ORDER_ID
     */
    private String orderId;

    /**
     * 资产包编号
     * 表字段:ASSET_NO
     */
    private String assetNo;

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    private String absStatus;

    /**
     * 原始交易码
     * 表字段:TXN_CODE_ORIG
     */
    private String txnCodeOrig;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

    /**
     * 渠道ID
     * 表字段:CHANNEL_ID
     */
    private String channelId;

    /**
     * 子渠道ID
     * 表字段:SUBCHANNEL_ID
     */
    private String subchannelId;

    /**
     * 资金源ID
     * 表字段:FUND_ID
     */
    private String fundId;

    /**
     * 平台ID
     * 表字段:PLATFORM_ID
     */
    private String platformId;

    /**
     * 商户ID
     * 表字段:MERCHANT_NUMBER
     */
    private String merchantNumber;

    /**
     * 五级分类标识
     */
    private String fiveTypeIndicator;

    /**
     * abs类型
     */
    private String absType;
}
