package com.anytech.anytxn.account.config;

import feign.RequestInterceptor;
//import feign.hystrix.HystrixFeign;
import org.springframework.context.annotation.Bean;

import java.util.HashMap;
import java.util.Map;

/**
 * feign调用客户端配置
 *
 * @<PERSON> zhen<PERSON>ng
 * @Date 2024/4/28  9:19
 * @Version 1.0
 */


public class FeignclientsConfig {
    @Bean
    public RequestInterceptor headerInterceptor(){
    Map<String, String> headers = new HashMap<>();
    headers.put("organizationNumber","101");
     return new FeignHeaderInterceptor(headers);
}

//    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
//    @Bean
//    public HystrixFeign.Builder feignBuilder() {
//        return HystrixFeign.builder();
//    }
}
