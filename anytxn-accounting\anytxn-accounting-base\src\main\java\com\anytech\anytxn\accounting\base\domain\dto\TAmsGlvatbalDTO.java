package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
@Getter
@Setter
public class TAmsGlvatbalDTO extends BaseEntity {

    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    private String accountManagementId;

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    private String glAcct;

    /**
     * 借方余额
     * 表字段:DBAL
     */
    private BigDecimal dbal;

    /**
     * 贷方余额
     * 表字段:CBAL
     */
    private BigDecimal cbal;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

    /**
     * 币种
     * 表字段:CURR_CODE
     */
    private String currCode;

    /**
     * 发生额更新日期
     * 表字段:UPDATE_DATE
     */
    private LocalDate updateDate;

    /**
     * 当日借
     * 表字段:DR_CURR
     */
    private BigDecimal drCurr;

    /**
     * 当日贷
     * 表字段:CR_CURR
     */
    private BigDecimal crCurr;

}
