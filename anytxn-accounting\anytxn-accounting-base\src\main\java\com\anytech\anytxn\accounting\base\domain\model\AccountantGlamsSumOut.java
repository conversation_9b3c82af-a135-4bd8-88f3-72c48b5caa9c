package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.anytxn.common.core.base.BaseEntity;
import com.anytech.batch.file.SeparatedType;
import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TransferFileObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@TransferFileObject(separatedType = SeparatedType.SEPARATOR, lineTailSeparator = false,
        encoding = "UTF-8", description = "非法流水汇总文件",postfix = ".csv",separator = ",")
public class AccountantGlamsSumOut extends BaseEntity implements Comparable<AccountantGlamsSumOut>, Serializable {

    /**
     * 分行号
     * 表字段:BRANCHID
     */
    @FileField(order = 1, length = 50)
    private String branchid;

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    @FileField(order = 2, length = 50)
    private String accountManagementId;

    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    @FileField(order = 3, length = 50)
    private String acctLogo;

    /**
     * 全局业务流水号
     * 表字段:GLOBAL_FLOW_NO
     */
    @FileField(order = 4, length = 50)
    private String globalFlowNo;

    /**
     * 模块标识
     * 表字段:MODULE_FLAG
     */
    @FileField(order = 5, length = 50)
    private String moduleFlag;

    /**
     * null
     * 表字段:TXN_CODE
     */
    @FileField(order = 6, length = 50)
    private String txnCode;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    @FileField(order = 7, length = 50)
    private String postingDate;

    /**
     * 入账币种
     * 表字段:POSTING_CURRENCY_CODE
     */
    @FileField(order = 8, length = 50)
    private String postingCurrencyCode;

    /**
     * 入账金额
     * 表字段:POSTING_AMT
     */
    @FileField(order = 9, length = 50)
    private BigDecimal postingAmt;

    /**
     * 入账笔数
     * 表字段:POSTING_CNT
     */
    @FileField(order = 10, length = 50)
    private BigDecimal postingCnt;

    /**
     * 核算状态
     * 表字段:FINANCE_STATUS
     */
    @FileField(order = 11, length = 50)
    private String financeStatus;

    /**
     * 表内表外标识
     * 表字段:INTEREST_IND
     */
    @FileField(order = 12, length = 50)
    private String interestInd;

    /**
     * 价税分离标识
     * 表字段:PRICE_TAX_FLG
     */
    @FileField(order = 13, length = 50)
    private String priceTaxFlg;

    /**
     * 原始交易码
     * 表字段:TXN_CODE_ORIG
     */
    @FileField(order = 14, length = 50)
    private String txnCodeOrig;

    /**
     * 余额类型
     * 表字段:BAL_TYPE
     */
    @FileField(order = 15, length = 50)
    private String balType;

    /**
     * 余额结转标志
     * 表字段:BAL_PROCESS_IND
     */
    @FileField(order = 16, length = 50)
    private String balProcessInd;

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    @FileField(order = 17, length = 50)
    private String absStatus;

    /**
     * 原始交易ABS借贷记标志
     * 表字段:ORIG_TXN_ABS_IND
     */
    @FileField(order = 18, length = 50)
    private String origTxnAbsInd;

    /**
     * 分期订单号
     * 表字段:ORDER_ID
     */
    @FileField(order = 19, length = 50)
    private String orderId;

    /**
     * 处理标识
     * 表字段:PROCESS_IND
     */
    @FileField(order = 20, length = 50)
    private String processInd;

    /**
     * 渠道ID
     * 表字段:CHANNEL_ID
     */
    @FileField(order = 21, length = 50)
    private String channelId;

    /**
     * 子渠道ID
     * 表字段:SUBCHANNEL_ID
     */
    @FileField(order = 22, length = 50)
    private String subchannelId;

    /**
     * 资金源ID
     * 表字段:FUND_ID
     */
    @FileField(order = 23, length = 50)
    private String fundId;

    /**
     * 平台ID
     * 表字段:PLATFORM_ID
     */
    @FileField(order = 24, length = 50)
    private String platformId;

    /**
     * 商户ID
     * 表字段:MERCHANT_NUMBER
     */
    @FileField(order = 25, length = 50)
    private String merchantNumber;
    /**
     * 交易属性
     */
    @FileField(order = 26, length = 50)
    private String transactionAttribute;
    /**
     * 借贷记属性
     */
    @FileField(order = 27, length = 50)
    private String debitCreditIndicator;

    /**
     * 递延标识
     */
    @FileField(order = 28, length = 50)
    private String amortizeInd;
    /**
     * 交易类型
     */
    @FileField(order = 29, length = 50)
    private String transactionTypeCode;

    /**
     * 规则ID
     */
    @FileField(order = 30, length = 50)
    private String ruleId;

    /**
     * 五级分类标识
     */
    @FileField(order = 31, length = 50)
    private String fiveTypeIndicator;

    /**
     * abs类型
     */
    @FileField(order = 32, length = 50)
    private String absType;

    /**
     * abs类型
     */
    @FileField(order = 33, length = 50)
    private String crdNumber;

    /**
     * abs类型
     */
    @FileField(order = 34, length = 50)
    private String crdProductNumber;

    /**
     * abs类型
     */
    @FileField(order = 35, length = 50)
    private String crdType;

    /**
     * abs类型
     */
    @FileField(order = 36, length = 50)
    private BigDecimal txnAmt;

    /**
     * abs类型
     */
    @FileField(order = 37, length = 50)
    private String txnCurrency;

    /**
     * abs类型
     */
    @FileField(order = 38, length = 50)
    private BigDecimal settleAmt;

    /**
     * abs类型
     */
    @FileField(order = 39, length = 50)
    private String settleCurrency;

    /**
     * abs类型
     */
    @FileField(order = 40, length = 50)
    private LocalDateTime txnDate;

    /**
     * abs类型
     */
    @FileField(order = 41, length = 50)
    private String txnSource;

    /**
     * 交易描述
     */
    @FileField(order = 42, length = 50)
    private String txnDescription;

    /**
     * abs类型
     */
    @FileField(order = 43, length = 50)
    private String currencyRate;

    /**
     * abs类型
     */
    @FileField(order = 44, length = 50)
    private String rrn;

    /**
     * abs类型
     */
    @FileField(order = 45, length = 50)
    private String acqId;

    /**
     * mkUpFeeInd
     */
    @FileField(order = 46, length = 50)
    private String mkUpFeeInd;

    /**
     * 产品类型
     */
    @FileField(order = 47, length = 50)
    private String productType;

    /**
     * 卡组织
     */
    @FileField(order = 48, length = 50)
    private String cardOrgNumber;

    /**
     *
     * va号:VA_NUMBER
     */
    @FileField(order = 49, length = 50)
    private String vaNum;

    @FileField(order = 50, length = 25)
    private String vataCoupleIndicator;

    @Override
    public int compareTo(AccountantGlamsSumOut o) {
        return 0;
    }
}
