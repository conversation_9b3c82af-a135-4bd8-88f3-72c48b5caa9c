package com.anytech.anytxn.account.job.paymentfile.config;

import com.anytech.anytxn.account.job.paymentfile.model.RepaymentDTO;
import com.anytech.anytxn.account.config.RePaymentFileConfigurer;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.job.paymentfile.step.repay.RepaymentFileReader;
import com.anytech.anytxn.account.job.paymentfile.step.repay.RepaymentFileWriter;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.AbstractLineTokenizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.FileSystemResource;

import jakarta.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * 还款文件分区处理（前提分区文件已生成）
 * <AUTHOR>
 * @date 2021/1/7
 */
@Configuration
@Slf4j
public class RepaymentFileConfig {

    private String tenantId;

    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;

    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Autowired
    private RePaymentFileConfigurer rePaymentFileConfigurer;

    @Value("${data-id:-1}")
    private String dataId;

    @Value("${anytxn.batch.repayment-file.chunk-limit:1}")
    private Integer chunkLimit;

    @Value("${anytxn.fileEncoding:GBK}")
    private String fileEncoding;

    public final static int[] condition = new int[]{4,8,20,12,3,8,12,19,2,2,18,30};
    @Resource(name = "paymentFileOutPutPathConfig")
    private AnytxnFilePathConfig pathConfigOutput;


    @Bean
    public Job repaymentFileJob(@Qualifier("repaymentFileStep") Step repaymentFileStep) {
        return jobs.get("repaymentFileJob")
                .start(repaymentFileStep)
                .build();
    }

    @Bean
    public Step repaymentFileStep(
            @Qualifier("repaymentFileReader") ItemReader repaymentFileReader,
            @Qualifier("repaymentFileWriter") ItemWriter repaymentFileWriter) {
        return steps.get("repaymentFileStep")
                .chunk(chunkLimit)
                .reader(repaymentFileReader)
                .writer(repaymentFileWriter)
                .build();
    }

    @Bean
    @StepScope
    public RepaymentFileReader repaymentFileReader(){
        return new RepaymentFileReader(fileReader(), tenantId);
    }

    @Bean
    @StepScope
    public RepaymentFileWriter repaymentFileWriter(){
        return new RepaymentFileWriter(tenantId);
    }

    public FlatFileItemReader<RepaymentDTO> fileReader(){
        FlatFileItemReader<RepaymentDTO> fileItemReader = new FlatFileItemReader<>();
        fileItemReader.setEncoding(fileEncoding);
        fileItemReader.setResource(new FileSystemResource(joinRePaymentFilePath()));

        fileItemReader.setLineMapper(new DefaultLineMapper<RepaymentDTO>() {{
            // 分隔符
            setLineTokenizer(new FixedLenLineTokenizer(condition) {{
                setNames("bank", "dateCr","debitAcct","amtCr","curr","datePr",
                        "amtActual","cardNbr","code","idType","idNo","tranNo");
            }});
            // 字段格式
            setFieldSetMapper(new BeanWrapperFieldSetMapper<RepaymentDTO>() {{
                setTargetType(RepaymentDTO.class);
            }});
        }});
        // 文件不存在不读取
        fileItemReader.setStrict(false);
        return fileItemReader;
    }

    private String joinRePaymentFileName(ParmOrganizationInfo org){
        // 文件名称由【机构号-文件名-日期】组成
        String today = org.getToday().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return org.getOrganizationNumber() + "-" + rePaymentFileConfigurer.getFileName() + "-" + today;
    }

    private String joinRePaymentFilePath(){
        String orgNum = OrgNumberUtils.getOrg();
        ParmOrganizationInfo org = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(orgNum);
        // 前缀/re/分片好/文件名
        return pathConfigOutput.getShardingPath(Integer.parseInt(getDusId())- 1) + joinRePaymentFileName(org);
    }

    public class FixedLenLineTokenizer<T> extends AbstractLineTokenizer{

        private int[] length;
        private long count;

        public FixedLenLineTokenizer(int[] length) {
            this.length = length;
            count = Arrays.stream(length).sum();
        }

        @Override
        protected List<String> doTokenize(String line) {
            if (StringUtils.isEmpty(line)) {
                return null;
            }
            if (line.length() < count) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.IN_FILE);
            }

            List<String> result = Lists.newArrayList();
            char[] chars = line.toCharArray();

            int ps = 0;
            for (int len : length) {
                result.add(new String(chars, ps, len).trim());
                ps += len;
            }

            return result;
        }
    }

    private String getDusId(){
        if (dataId.contains(".")) {
            dataId = dataId.split("\\.")[0];
        }

        if (dataId.contains("-")){
            dataId = dataId.split("-")[1];
        }

        return dataId;
    }
}
