package com.anytech.anytxn.accounting.batch.job.report.step.installmonth.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 具体数据
 * <AUTHOR>
 * @Date 2020/9/11 11:48
 */
@Getter
@Setter
public class OrderPlanDetail {
    //卡号
    private String cardNumber;
    //订单号
    private String orderNo;
    //产品ID
    private String productId;
    //产品描述
    private String productDesc;
    //币种
    private Integer currencyCode;
    //入账日期
    private String postDate;
    //本金
    private BigDecimal amount;
    //费用
    private BigDecimal fee;
}
