package com.anytech.anytxn.accounting.base.domain.bo;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherSumDTO;
import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
@Getter
@Setter
@ToString
public class GlvcherSumBO extends BaseEntity {
    /** 旧传票汇总 */
    private TAmsGlvcherSumDTO oldGlvcherSum;
    /** 新传票汇总 */
    private TAmsGlvcherSumDTO newGlvcherSum;
    /** 待更新传票 */
    private List<TAmsGlvcherDTO> glvcherList;
    /** 需要删除汇总id集合 */
    private List<String> delGlvcherSumIds;
}
