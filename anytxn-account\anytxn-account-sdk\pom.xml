<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.anytech</groupId>
        <artifactId>anytxn-account</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>anytxn-account-sdk</artifactId>

    <dependencies>
        <!--        内部依赖start-->
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-business-core-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-sequence</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-parameter-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-rule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-transaction-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-limit-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-account-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-central-processing-sdk</artifactId>
        </dependency>
        <!--        内部依赖end-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework</groupId>-->
<!--            <artifactId>spring-webmvc</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.github.pagehelper</groupId>-->
<!--            <artifactId>pagehelper-spring-boot-starter</artifactId>-->
<!--        </dependency>-->

    </dependencies>
</project>
