package com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep;

import com.anytech.anytxn.accounting.base.enums.AccountRepDetailEnum;
import com.anytech.anytxn.accounting.base.exception.AnyTxnAccountantException;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 读取交易会计流水
 * <AUTHOR>
public class AmsToVoucherReader extends JdbcPagingItemReader<AccountantGlams> {

    public AmsToVoucherReader(String partitionKey, DataSource dataSource, String fromId, String endId, OrganizationInfoResDTO organizationInfo) {
        super();
        this.setRowMapper(new BeanPropertyRowMapper<>(AccountantGlams.class));
        this.setQueryProvider(oraclePagingQueryProvider(partitionKey,dataSource, fromId, endId,organizationInfo));
    }

    private PagingQueryProvider oraclePagingQueryProvider(String partitionKey, DataSource dataSource,String fromId, String endId,OrganizationInfoResDTO organizationInfo) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();
        String whereSql = " ORGANIZATION_NUMBER = "+ OrgNumberUtils.getOrg()+" AND MODULE_FLAG IN ('0','M') "+" AND PROCESS_IND='0' AND POSTING_DATE = '"+organizationInfo.getToday()+"' ";

        if (fromId != null && endId != null) {
            whereSql = whereSql + " and PARTITION_KEY >= :fromId and PARTITION_KEY <= :endId  ";

        }
        providerFactoryBean.setWhereClause(whereSql);
        providerFactoryBean.setSelectClause("DISTINCT ACCOUNT_MANAGEMENT_ID");
        // 交易会计流水表
        providerFactoryBean.setFromClause("ACCOUNTANT_GLAMS");

        providerFactoryBean.setDataSource(dataSource);
        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("ACCOUNT_MANAGEMENT_ID", Order.ASCENDING);
        providerFactoryBean.setSortKeys(sortKey);
        providerFactoryBean.setDataSource(dataSource);
        try {
            return  providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("PagingQueryProvider exception",e);
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.D_DATABASE_FAULT, AccountRepDetailEnum.BATCH_PAGE_QUERY_ERROR);
        }
    }

}
