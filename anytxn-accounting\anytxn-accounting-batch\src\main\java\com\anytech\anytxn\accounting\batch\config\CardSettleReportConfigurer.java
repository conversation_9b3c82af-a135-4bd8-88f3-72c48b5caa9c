package com.anytech.anytxn.accounting.batch.config;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 每日/月/季/年发卡清算配置
 * <AUTHOR>
 * @date 2020/9/13
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.report.card-settle")
public class CardSettleReportConfigurer extends BaseConfigurer{

    /**
     * 通用汇率模版
     */
    private String crTempleFileName;
    /**
     * 通用非汇率模版
     */
    private String cnrTempleFileName;
    /**
     * 通用汇率模版文件
     */
    private String crTempleFilePath;
    /**
     * 通用非汇率模版文件
     */
    private String cnrTempleFilePath;
    /**
     * 通用汇率输出文件扩展路径
     */
    private String crFilePath;

    /**
     * 通用非汇率模版输出文件扩展路径
     */
    private String cnrFilePath;


    /**
     * 非汇率日清算文件名
     */
    private String noRateDayFileName = "每日发卡清算报表";
    /**
     * 非汇率日清算模版文件
     */
    private String noRateDayTempleFileName = "issue_card_settlement.jasper";
    /**
     * 非汇率日清算模版文件扩展路径
     */
    private String noRateDayTempleFilePath;
    /**
     * 非汇率日清算输出文件扩展路径
     */
    private String noRateDayExportFilePath;
    /**
     * 汇率日清算文件名
     */
    private String rateDayFileName = "每日发卡清算（汇率转换）报表";
    /**
     * 汇率日清算模版文件
     */
    private String rateDayTempleFileName = "issue_card_settlement_rate.jasper";
    /**
     * 汇率日清算模版文件扩展路径
     */
    private String rateDayTempleFilePath;
    /**
     * 汇率日清算输出文件扩展路径
     */
    private String rateDayExportFilePath;



    /**
     * 非汇率月清算文件名
     */
    private String noRateMonthFileName = "每月发卡清算报表";
    /**
     * 非汇率月清算模版文件
     */
    private String noRateMonthTempleFileName = "issue_card_settlement.jasper";
    /**
     * 非汇率月清算模版文件扩展路径
     */
    private String noRateMonthTempleFilePath;
    /**
     * 非汇率月清算输出文件扩展路径
     */
    private String noRateMonthExportFilePath;
    /**
     * 汇率月清算文件名
     */
    private String rateMonthFileName = "每月发卡清算（汇率转换）报表";
    /**
     * 汇率月清算模版文件
     */
    private String rateMonthTempleFileName = "issue_card_settlement_rate.jasper";
    /**
     * 汇率月清算模版文件扩展路径
     */
    private String rateMonthTempleFilePath;
    /**
     * 汇率月清算输出文件扩展路径
     */
    private String rateMonthExportFilePath;


    /**
     * 非汇率季度清算文件名
     */
    private String noRateQuarterFileName = "每季发卡清算报表";
    /**
     * 非汇率季度清算模版文件
     */
    private String noRateQuarterTempleFileName = "issue_card_settlement.jasper";
    /**
     * 非汇率季度清算模版文件扩展路径
     */
    private String noRateQuarterTempleFilePath;
    /**
     * 非汇率季度清算输出文件扩展路径
     */
    private String noRateQuarterExportFilePath;
    /**
     * 汇率季度清算文件名
     */
    private String rateQuarterFileName = "每季度发卡清算（汇率转换）报表";
    /**
     * 汇率季度清算模版文件
     */
    private String rateQuarterTempleFileName = "issue_card_settlement_rate.jasper";
    /**
     * 汇率季度清算模版文件扩展路径
     */
    private String rateQuarterTempleFilePath;
    /**
     * 非汇率季度清算输出文件扩展路径
     */
    private String rateQuarterExportFilePath;


    /**
     * 非汇率年清算文件名
     */
    private String noRateYearFileName = "每年发卡清算报表";
    /**
     * 非汇率年清算模版文件
     */
    private String noRateYearTempleFileName = "issue_card_settlement.jasper";
    /**
     * 非汇率年清算模版文件扩展路径
     */
    private String noRateYearTempleFilePath;
    /**
     * 非汇率年清算输出文件扩展路径
     */
    private String noRateYearExportFilePath;
    /**
     * 汇率年清算文件名
     */
    private String rateYearFileName= "每年发卡清算（汇率转换）报表";
    /**
     * 汇率年清算模版文件
     */
    private String rateYearTempleFileName = "issue_card_settlement_rate.jasper";
    /**
     * 汇率年清算模版文件扩展路径
     */
    private String rateYearTempleFilePath;
    /**
     * 汇率年清算输出文件扩展路径
     */
    private String rateYearExportFilePath;

    /**
     * 输出文件名称
     */
    public String outFileName(boolean ableRate, TimeTypeEnum timeTypeEnum){
        switch (timeTypeEnum){
            case D:
                return (ableRate ? rateDayFileName : noRateDayFileName) + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case M:
                return (ableRate ? rateMonthFileName : noRateMonthFileName) + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case Q:
                return (ableRate ? rateQuarterFileName : noRateQuarterFileName) + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case Y:
                return (ableRate ? rateYearFileName : noRateYearFileName) + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            default: return "";
        }
    }

    /**
     * 输出文件路径
     */
    public String outFile(boolean ableRate, TimeTypeEnum timeTypeEnum){
        switch (timeTypeEnum){
            case D:
                if (ableRate) {
                    return commonConfigurer.templateFilePath + getFileDe(rateDayExportFilePath, true);
                } else {
                    return commonConfigurer.templateFilePath + getFileDe(noRateDayExportFilePath, false);
                }
            case M:
                if (ableRate) {
                    return commonConfigurer.templateFilePath + getFileDe(rateMonthExportFilePath, true);
                } else {
                    return commonConfigurer.templateFilePath + getFileDe(noRateMonthExportFilePath, false);
                }
            case Q:
                if (ableRate) {
                    return commonConfigurer.templateFilePath + getFileDe(rateQuarterExportFilePath, true);
                } else {
                    return commonConfigurer.templateFilePath + getFileDe(noRateQuarterExportFilePath, false);
                }
            case Y:
                if (ableRate) {
                    return commonConfigurer.templateFilePath + getFileDe(rateYearExportFilePath, true);
                } else {
                    return commonConfigurer.templateFilePath + getFileDe(noRateYearExportFilePath, false);
                }
            default: return "";
        }
    }

    /**
     * 模版文件
     */
    public String templeFile(boolean ableRate, TimeTypeEnum timeTypeEnum){
        switch (timeTypeEnum){
            case D:
                if (ableRate) {
                    return commonConfigurer.templateFilePath + getTemplateFileDe(rateDayTempleFilePath, true) + File.separator + getTemplateFileNe(rateDayTempleFileName, true);
                } else {
                    return commonConfigurer.templateFilePath + getTemplateFileDe(noRateDayTempleFilePath, false) + File.separator + getTemplateFileNe(noRateDayTempleFileName, false);
                }
            case M:
                if (ableRate) {
                    return commonConfigurer.templateFilePath + getTemplateFileDe(rateMonthTempleFilePath, true) + File.separator + getTemplateFileNe(rateMonthTempleFileName, true);
                } else {
                    return commonConfigurer.templateFilePath + getTemplateFileDe(noRateMonthTempleFilePath, false) + File.separator + getTemplateFileNe(noRateMonthTempleFileName, false);
                }
            case Q:
                if (ableRate) {
                    return commonConfigurer.templateFilePath + getTemplateFileDe(rateQuarterTempleFilePath, true) + File.separator + getTemplateFileNe(rateQuarterTempleFileName, true);
                } else {
                    return commonConfigurer.templateFilePath + getTemplateFileDe(noRateQuarterTempleFilePath, false) + File.separator + getTemplateFileNe(noRateQuarterTempleFileName, false);
                }
            case Y:
                if (ableRate) {
                    return commonConfigurer.templateFilePath + getTemplateFileDe(rateYearTempleFilePath, true) + File.separator + getTemplateFileNe(rateYearTempleFileName, true);
                } else {
                    return commonConfigurer.templateFilePath + getTemplateFileDe(noRateYearTempleFilePath, false) + File.separator + getTemplateFileNe(noRateYearTempleFileName, false);
                }
            default: return "";
        }
    }

    private String getTemplateFileDe(String hPath, boolean rate){
        String dex = StringUtils.isEmpty(hPath) ? (rate ? crTempleFilePath : cnrTempleFilePath) : hPath;
        String dex2 = StringUtils.isEmpty(dex) ? templateFilePath : dex;
        return StringUtils.isEmpty(dex2) ? "" : dex2;
    }

    private String getTemplateFileNe(String hName, boolean rate){
        return StringUtils.isEmpty(hName) ? (rate ? crTempleFileName : cnrTempleFileName) : hName;
    }

    private String getFileDe(String hPath, boolean rate){
        String dex = StringUtils.isEmpty(hPath) ? (rate ? crFilePath : cnrFilePath) : hPath;
        String dex2 = StringUtils.isEmpty(dex) ? exportFilePath : dex;
        return StringUtils.isEmpty(dex2) ? null : dex2;
    }

    /**
     * 表头参数
     */
    public void putParam(Map<String, Object> params, boolean ableRate, TimeTypeEnum timeTypeEnum){
        String num = "";
        switch (timeTypeEnum) {
            case D: num = ableRate ? "R005" : "R004 ";
                break;
            case M: num = ableRate ? "R007" : "R006 ";
                break;
            case Q: num = ableRate ? "R009" : "R008 ";
                break;
            case Y: num = ableRate ? "R011" : "R010 ";
                break;
        }


        params.put("reportNumber", num);
        params.put("reportName", "每" + timeTypeEnum.getDes() + "发卡清算 " + (ableRate ? "（汇率转换）" : "") + "报表");
    }
}
