package com.anytech.anytxn.accounting.batch.job.report.step.installfree.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 入账分期手续费报表
 * <AUTHOR>
 * @Date 2020/9/11 11:46
 */
@Getter
@Setter
public class PostInstallFeeSub {
    private List<PostInstallFeeDetail> detailList;
    /**小计**/
    //分期产品id
    private String productId;
    //币种
    private String currencyCode;
    //条数
    private Integer subCount;
    //合计总额
    private BigDecimal amountTotal;

    private boolean end = false;
}
