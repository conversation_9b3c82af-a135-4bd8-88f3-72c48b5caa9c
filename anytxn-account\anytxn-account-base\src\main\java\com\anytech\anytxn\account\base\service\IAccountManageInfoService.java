package com.anytech.anytxn.account.base.service;

import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.account.base.domain.bo.GiroAutoPayBO;
import com.anytech.anytxn.account.base.domain.dto.AccountLimitInfoDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.business.base.account.domain.bo.ExchangePaymentBO;
import com.anytech.anytxn.business.base.account.domain.dto.AccMaDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.InCollectionDTO;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2018-09-29
 */
public interface IAccountManageInfoService {
    /**
     * 根据客户号查询管理账户信息
     *
     * @param customerId 客户号
     * @param pageNum    页码
     * @param pageSize   单页显示数
     * @return list
     */
    PageResultDTO<AccountManagementInfoDTO> queryByCustomerId(String customerId, int pageNum,
                                                              int pageSize);


    /**
     * 根据客户号查询管理账户
     *
     * @param customerId 客户号
     * @return 管理账户
     */
    List<AccountManagementInfoDTO> findListByCustomerId(String customerId);

    /**
     * 根据查询类型（卡号-C,证件号：I）、查询值(卡号/证件号)，查询管理账户
     * @param searchType  searchType
     * @param idType  证件类型
     * @param number number
     * @param partnerId 第三方机构号
     * @return List<AccountManagementInfoDTO>
     */
    List<AccountManagementInfoDTO> findListBySearchTypeAndNum(String searchType, String idType, String number, String partnerId);


    /**
     *根据管理账户id查询管理账户明细
     *
     * @param accountManagementId 管理账户id
     * @return 管理账户
     */
    AccountManagementInfoDTO findAccManInfo(String accountManagementId);

    /**
     *根据扩展参考号查询管理账户明细
     *
     * @param externalReferenceNumber 扩展参考号-持卡人首张卡
     * @return 管理账户
     */
    AccountManagementInfoDTO findAccManInfoByErn(String externalReferenceNumber, String orgNum);


    /**
     *根据机构号、管理账户id查询管理账户明细
     *
     * @param orgNum              机构号
     * @param accountManagementId 管理账户id
     * @return 管理账户
     */
    AccountManagementInfoDTO findByOrgNumAndMid(String orgNum, String accountManagementId);

    /**
     * 更新管理账户信息
     * @param accountManagementInfoDTO  管理账户信息
     * @return
     */
     void modifyAccountManagementInfo(AccountManagementInfoDTO accountManagementInfoDTO);

    /**
     * 根据获得的入账管理账户ID读取账户管理信息表
     *
     * @param accountId 入账管理账户ID
     * @return AccountManagementInfo
     */
    AccountManagementInfoDTO getAccountManagementInfo(String accountId);
    /**
     * 根据传入数据创建管理账户
     * @param accMaDto 传入数据
     */
    void addAccManInfo(AccMaDTO accMaDto);

    /**
     *新增管理账号
     * @param accountManagementInfoDTO
     */
    void  registAccountManagementInfo(AccountManagementInfoDTO accountManagementInfoDTO);


    /**
     * 根据机构号、管理账户id、币种读取账户管理信息表
     *
     * @param orgNumber 机构号
     * @param accountManagerId 管理账户id
     * @param currency 币种
     * @return AccountManagementInfoDTO
     */
    AccountManagementInfoDTO getAccountManagementInfo(String orgNumber, String accountManagerId, String currency);

    /**
     * 获取机构下需要跑批的管理账户id数
     *
     * @param partitionKey 分区值
     * @param organizations 当日要处理的机构
     * @return int
     */
    int getAccountManagementIdCount(String partitionKey, List<Map<String, Object>> organizations);

    /**
     * 分区管理账户
     * @param partitionKey 分区值
     * @param organizations 当日要处理的机构
     * @param rowNumbers accountManagementId
     * @return List<String>
     */
    List<String> getAccountManagementIds(String partitionKey, List<Map<String, Object>> organizations, List<Integer> rowNumbers);

    /**
     * 获取延滞管理账户ids
     *
     * @param partitionKey 分区键值
     * @param organizations 机构
     * @return int
     */
    int getAccountManagementIdCount4Delin(String partitionKey, List<Map<String, Object>> organizations);

    /**
     * 获取延滞管理账户id区间
     *
     * @param partitionKey 分区键值
     * @param organizations 机构
     * @param rowNumbers rownum
     * @return List<String>
     */
    List<String> queryAccountManagementIds4Delin(String partitionKey, List<Map<String, Object>> organizations, List<Integer> rowNumbers);


    /**
     * 约定还款process
     *
     * @param autoEbitSignUpInfor 签约表
     * @return AutoPaymentLogDTO
     */
    AutoPaymentBO batchProcess4AutoPayment(AutoEbitSignUpInforDTO autoEbitSignUpInfor);

    /**
     * 自扣process
     *
     * @param accountManagementInfo AccountManagementInfo
     * @return AutoPaymentLog
     */
    GiroAutoPayBO batchProcessAutoPay(AccountManagementInfo accountManagementInfo);

    /**
     * 购汇批量处理
     *
     * @param accountManagementInfo 管理账户dto
     * @return ExchangePaymentBO
     */
    ExchangePaymentBO batchProcess4ExchangePayment(AccountManagementInfo accountManagementInfo);

    /**
     * 获取批量购汇需要处理的管理账户数
     *
     * @param partitionKey 分区键值
     * @return int
     */
    int getAccountManagementIdCount4Exchange(String partitionKey);

    /**
     * 获取批量购汇需要处理的管理账户id
     * @param partitionKey 分区键值
     * @param rowNumbers rowNumber
     * @return List<String>
     */
    List<String> queryAccountManagementIds4Exchange(String partitionKey, List<Integer> rowNumbers);

    /**
     * 修改账户对应状态，若为0   - 新户  1 - 静止 则改为2 - 活跃
     * @param accountManagementId
     */
    void modifyAccountManagementAccountStatus(String accountManagementId);

    /**
     * 批量处理
     * @param accountManagementInfo 管理账户信息
     * @return InCollectionDTO
     */
    InCollectionDTO batchProcess4InCollection(AccountManagementInfo accountManagementInfo);

    /**
     * 根据客户号、账户产品编号、机构号查询管理账户信息
     * @param customerId 客户号
     * @param productNumber 账户产品编号
     * @param organizationNumber 机构号
     * @return AccountManagementInfoDTO
     */
    AccountManagementInfoDTO selectByCusIdProNumAndOrg(String customerId, String productNumber, String organizationNumber);

    /**
     * 根据客户号、账户产品编号、机构号查询管理账户信息
     * @param customerId 客户号
     * @param productNumber 账户产品编号
     * @param organizationNumber 机构号
     * @return List<AccountManagementInfoDTO>
     *
     * 和上面的selectByCusIdProNumAndOrg 查询的一样，因为开双币卡上面sql查询到两条数据只用了一个实体接收，导致程序报错
     *
     */
    List<AccountManagementInfoDTO> selectByCusIdProNumAndOrgList(String customerId, String productNumber, String organizationNumber);
    /**
     * 更新管理账户信息页面使用
     * @param accountManagementInfoDTO 管理账户信息
     * @return int
     */
    void modifyAccountManagementInfoCms(AccountManagementInfoDTO accountManagementInfoDTO);

    /**
     *根据管理账户id查询客户信息
     *
     * @param accountManagementId 管理账户id
     * @return 管理账户
     */
    CustomerAuthorizationInfoDTO findCustomerByAccountManagementId(String accountManagementId);

    /**
     * 根据客户号查询管理账户
     * @param customerId
     * @param relationshipIndicator
     * @param cardNumber
     * @return
     */
    List<AccountManagementInfoDTO> findsListByCustomerId(String customerId,String relationshipIndicator,String cardNumber);

    /**
     * 根据机构号+公司客户号查询管理账户
     * @param organizationNumber  String
     * @param corporateCustomerId String
     * @return List<AccountManagementInfoDTO>
     */
    List<AccountManagementInfoDTO> findByOrgNumAndCorpCusId(String organizationNumber, String corporateCustomerId);

    /**
     * 根据机构号和卡号获取账户额度信息
     * @param orgNumber
     * @param cardNumber
     * @return
     */
    AccountLimitInfoDTO getAccountLimitInfoByCardNumber(String orgNumber, String cardNumber);

    /**
     *
     * @param organizationNumber
     * @param corporateCustomerId
     * @param searchType
     * @param searchNumber
     * @return
     */
    List<AccountManagementInfoDTO> findByCorpManagement(String organizationNumber, String corporateCustomerId, String searchType, String searchNumber);
}
