<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>anytxn-account</artifactId>
        <groupId>com.anytech</groupId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>anytxn-account-batch</artifactId>

    <dependencies>
        <!--        内部依赖start-->
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-file-manager-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-account-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>jrx.anyscheduler</groupId>
            <artifactId>anyscheduler-batch-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-sequence</artifactId>
        </dependency>

        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-parameter-base</artifactId>
        </dependency>
        <!--        内部依赖end-->

        <!--<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-task-batch</artifactId>
        </dependency>-->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>-->


        <!--日志-->
        <dependency>
            <groupId>com.plumelog</groupId>
            <artifactId>plumelog-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <!--<exclude>*.xml</exclude>-->
                        <exclude>*.properties</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!-- 镜像仓库 -->
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <!--配置远程docker守护进程url-->
                    <!--                    <dockerHost></dockerHost>-->
                    <!--认证配置,用于私有registry认证-->
                    <authConfig>
                        <username>${harbor.auth.user}</username>
                        <password>${harbor.auth.passd}</password>
                    </authConfig>
                    <!--镜像相关配置,支持多镜像-->
                    <images>
                        <!-- 单个镜像配置 -->
                        <image>
                            <!--镜像名(含版本号)-->
                            <name>k8s.jrx.com/anytxn/${project.artifactId}:${project.version}</name>
                            <!--registry地址,用于推送,拉取镜像-->
                            <registry>${harbor.registry}</registry>
                            <!--镜像build相关配置-->
                            <build>
                                <!--使用dockerFile文件-->
                                <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                            </build>
                        </image>
                    </images>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
