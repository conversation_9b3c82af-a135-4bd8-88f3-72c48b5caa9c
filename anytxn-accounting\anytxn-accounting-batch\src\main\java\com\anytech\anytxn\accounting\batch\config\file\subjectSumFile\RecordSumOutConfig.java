package com.anytech.anytxn.accounting.batch.config.file.subjectSumFile;

import com.anytech.anytxn.accounting.base.domain.model.AccountantGlRecordSum;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlRecordSumHearder;
import com.anytech.anytxn.accounting.service.GlvcherFileServiceImpl;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSum;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.common.service.system.OrganizationInfoServiceImpl;
import com.anytech.batch.job.file.common.FileHeader;
import com.anytech.batch.job.file.handler.TextLineHandler;
import com.anytech.batch.job.file.write.FileBody;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJob;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJobConfig;
import jakarta.annotation.Resource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;

@Configuration
public class RecordSumOutConfig extends SimpleWriteFileBatchJobConfig<SubjectRecordSum, AccountantGlRecordSum, Long> {
    @Resource
    private GlvcherFileServiceImpl glSumService;
    @Resource
    private AnytxnFilePathConfig recordSumFileFormatPathConfig;
    @Resource
    private OrganizationInfoServiceImpl organizationInfoService;

    @Autowired
    private TPmsGlacgnSelfMapper tPmsGlacgnSelfMapper;

    public RecordSumOutConfig(JobBuilderFactory jobs,
                              StepBuilderFactory steps,
                              SqlSessionFactory sqlSessionFactory,
                              GlvcherFileServiceImpl glSumService) {
        super(jobs, steps, sqlSessionFactory);
        this.glSumService = glSumService;
    }
    @Override
    public SimpleWriteFileBatchJob<SubjectRecordSum, AccountantGlRecordSum, Long> getBatchJob() {
        String recordSumFilePath = recordSumFileFormatPathConfig.getCommonPath();
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        LocalDate today = organizationInfo.getToday();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyMMdd");
        DateTimeFormatter dateTimeFormatter2 = DateTimeFormatter.ofPattern("yyyyMMdd");
        String format = dateTimeFormatter.format(today);
        String format2 = dateTimeFormatter2.format(today);
        HashMap<String, Object> map = new HashMap<>();
        map.put("sumDate", format2);
        SimpleWriteFileBatchJob<SubjectRecordSum, AccountantGlRecordSum, Long> batchJob = SimpleWriteFileBatchJob.of();

        batchJob.name("recordSumFileProcess").path(recordSumFilePath).resolveClass(AccountantGlRecordSum.class)
                .fileName("RECORD.SUM.OUT." + format)
                .header(new FileHeader(AccountantGlRecordSumHearder.class, (TextLineHandler) () -> {
                    AccountantGlRecordSumHearder headerDto = new AccountantGlRecordSumHearder();
                    headerDto.setSumDate("post_date");
                    headerDto.setSubjectCodeId("account");
                    headerDto.setSubjectDesc("account_name");
                    headerDto.setDebitCreditSide("Dr_or_Cr");
                    headerDto.setTotalAmt("post_amt");
                    headerDto.setCurrency("post_curr");
                    headerDto.setModuleFlag("business");
                    headerDto.setCrdOrganization("card_scheme");
                    headerDto.setAccountProduct("Account_product");
                    return headerDto;
                }))
                .body(FileBody.<SubjectRecordSum, AccountantGlRecordSum, Long>of().queryId("com.anytech.anytxn.core.accountant.mapper.SubjectRecordSumMapper.selectDateAndProcessInd")
                        .pageSize(1000)
                        .parameterValues(map)
                        .processorHandler(codeId ->
                                glSumService.buildGlRecordSum(codeId)));

        return batchJob;
    }
}
