package com.anytech.anytxn.account;

import com.anytech.anytxn.account.config.AccountingConfiguration;
import com.anytech.anytxn.business.dao.EnableBisCoreDao;
import com.anytech.anytxn.rule.EnableRuleService;
import com.anytech.anytxn.transaction.EnableTransactionService;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;


/**
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import({EnableAccountingService.AccountingDaoConfigurer.class, AccountingConfiguration.class})
public @interface EnableAccountingService {
    @Configuration
    @EnableBisCoreDao
    @EnableTransactionService
    @EnableRuleService
//    @EnableMappingService
    @ComponentScan(basePackages = {"com.anytech.anytxn.account.service", "com.anytech.anytxn.account.feign"})
    class AccountingDaoConfigurer {
    }
}
