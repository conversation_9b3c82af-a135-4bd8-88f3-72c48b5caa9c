package com.anytech.anytxn.account.job.overpayallocation.step;

import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.base.monetary.enums.AnyTxnCustAccountRespCodeEnum;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountException;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-10-20 14:55
 **/
public class OverpayAllocationReader extends JdbcPagingItemReader<CustReconciliationControlDTO> {
    private static final Logger LOG = LoggerFactory.getLogger(OverpayAllocationReader.class);

    public OverpayAllocationReader(DataSource dataSource) {
        super();
        this.setRowMapper(new BeanPropertyRowMapper<>(CustReconciliationControlDTO.class));
        this.setQueryProvider(oraclePagingQueryProvider(dataSource));
    }
    private PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();

        String orgConditionStr = "ORGANIZATION_NUMBER = "+ OrgNumberUtils.getOrg();
        String sql = orgConditionStr ;
        //调试用
//        String sql = orgConditionStr +" and ID = '162261538512300000001067'";
        providerFactoryBean.setSelectClause("ID, ORGANIZATION_NUMBER, CUSTOMER_ID, RECONCILIATION_DATE, OPTMISTIC_LOCK_COUNT, CREATE_TIME, UPDATE_TIME, UPDATE_BY, VERSION_NUMBER, BATCH_STATUS, ACTIVE_STATUS");
        providerFactoryBean.setFromClause("CUST_RECONCILIATION_CONTROL");

        // 需要补充查询条件
        providerFactoryBean.setWhereClause(sql);
        Map<String, Order> sortKey = new HashMap<>(8);
        sortKey.put("ID", Order.DESCENDING);
        providerFactoryBean.setSortKeys(sortKey);
        providerFactoryBean.setDataSource(dataSource);
        try {
            return providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("PagingQueryProvider exception",e);
            throw new AnyTxnCustAccountException(AnyTxnCustAccountRespCodeEnum.D_DATABASE_SELECT_ERROR, "PagingQueryProvider");
        }
    }
//    private PagingQueryProvider oraclePagingQueryProvider(String partitionKey,DataSource dataSource) {
//        SqlPagingQueryProviderFactoryBean provider = new SqlPagingQueryProviderFactoryBean();
//        provider.setSelectClause(" ACCOUNT_MANAGEMENT_ID, ORGANIZATION_NUMBER,CURRENCY, PRODUCT_NUMBER,CUSTOMER_ID,LAST_STATEMENT_DATE, " +
//                " AUTO_EXCHANGE_PAYMENT_TYPE, STATEMENT_DUE_AMOUNT, TOTAL_DUE_AMOUNT , AUTO_EXCHANGE_INDICATOR");
//        provider.setFromClause(" ACCOUNT_MANAGEMENT_INFO ");
//
//        String orgConditionStr = " ORGANIZATION_NUMBER = "+ OrgNumberUtils.getOrg();
//        provider.setWhereClause(orgConditionStr);
//
//        Map<String, Order> sortKey = new HashMap<>(10);
//        sortKey.put("ACCOUNT_MANAGEMENT_ID", Order.ASCENDING);
//        provider.setSortKeys(sortKey);
//        provider.setDataSource(dataSource);
//        try {
//            return provider.getObject();
//        } catch (Exception e) {
//            logger.error("PagingQueryProvider exception",e);
//            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.UNKONWN_ERR);
//        }
//    }
}
