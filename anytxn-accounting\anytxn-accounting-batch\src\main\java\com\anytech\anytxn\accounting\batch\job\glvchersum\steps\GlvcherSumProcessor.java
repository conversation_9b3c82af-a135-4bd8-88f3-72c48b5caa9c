package com.anytech.anytxn.accounting.batch.job.glvchersum.steps;

import com.anytech.anytxn.accounting.base.service.IGlvcherSumService;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public class GlvcherSumProcessor implements ItemProcessor<AccountantGlvcher, AccountantGlvcher> {

    @Autowired
    private IGlvcherSumService glvcherSumService;

    @Override
    public AccountantGlvcher process(AccountantGlvcher item) throws Exception {
        return glvcherSumService.glvcherSum(item);
    }
}
