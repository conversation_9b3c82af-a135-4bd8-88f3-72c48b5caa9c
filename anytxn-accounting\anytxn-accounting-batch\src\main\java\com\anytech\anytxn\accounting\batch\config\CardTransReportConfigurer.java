package com.anytech.anytxn.accounting.batch.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 发卡产品
 * <AUTHOR>
 * @date 2020/10/15
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.report.card-trans")
public class CardTransReportConfigurer extends BaseConfigurer{
}
