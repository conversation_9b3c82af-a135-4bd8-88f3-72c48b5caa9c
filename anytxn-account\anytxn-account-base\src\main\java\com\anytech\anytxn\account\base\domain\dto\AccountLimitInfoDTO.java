package com.anytech.anytxn.account.base.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 管理账户额度信息
 *
 * <AUTHOR>
 * @time 7/12/2021
 */
@Data
public class AccountLimitInfoDTO {

    /**
     * 账单日
     * 表字段：CYCLE_DAY
     * 来源表：account_management_info
     */
    private Short cycleDay;

    /**
     * 总额度
     */
    private BigDecimal creditLimit;

    /**
     * 已用额度
     */
    private BigDecimal usedLimit;

    /**
     * 当前积分
     * 表字段：CURRENT_BONUS_POINTS
     * 来源表：account_management_info
     */
    private BigDecimal currentBonusPoints;

    /**
     * 币种
     * 表字段：CURRENCY
     * 来源表：account_management_info
     */
    private String currency;

    /**
     * 还款日
     * 表字段：PAYMENT_DUE_DATE
     * 来源表：account_management_info
     */
    @JsonFormat( pattern = "yyyy-MM-dd")
    private LocalDate paymentDueDate;

    /**
     * 已用额度
     */
    private BigDecimal availableLimit;

}
