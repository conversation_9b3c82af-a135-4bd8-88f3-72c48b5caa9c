package com.anytech.anytxn.accounting.base.domain.model;


import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TextLineObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TextLineObject(encoding = "UTF-8")
public class AccountantGlvcherOutHeader implements Serializable {

    /**
     * 分行号
     * 表字段:BRANCHID
     */
    @FileField(order = 1, length = 9, rightPad = true)
    private String branchid="BRANCH_ID";

    @FileField(order = 2, length = 1, rightPad = true)
    private String dot2=",";

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    @FileField(order = 3, length = 21, rightPad = true)
    private String accountManagementId="ACCOUNT_MANAGEMENT_ID";

    @FileField(order = 4, length = 1, rightPad = true)
    private String dot3=",";

    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    @FileField(order = 5, length = 9, rightPad = true)
    private String acctLogo="ACCT_LOGO";

    @FileField(order = 6, length = 1, rightPad = true)
    private String dot4=",";

    /**
     * 全局业务流水号
     * 表字段:GLOBAL_FLOW_NO
     */
    @FileField(order = 7, length = 14, rightPad = true)
    private String globalFlowNo="GLOBAL_FLOW_NO";

    @FileField(order = 8, length = 1, rightPad = true)
    private String dot5=",";

    /**
     * 模块标识
     * 表字段:MODULE_FLAG
     */
    @FileField(order = 9, length = 11, rightPad = true)
    private String moduleFlag="MODULE_FLAG";

    @FileField(order = 10, length = 1, rightPad = true)
    private String dot6=",";

    /**
     * 交易码
     * 表字段:VP_TXN_CODE
     */
    @FileField(order = 11, length = 11, rightPad = true)
    private String vpTxnCode="VP_TXN_CODE";

    @FileField(order = 12, length = 1, rightPad = true)
    private String dot7=",";

    /**
     * 币种
     * 表字段:CURR_CODE
     */
    @FileField(order = 13, length = 13, rightPad = true)
    private String currCode="CURRENCY_CODE";

    @FileField(order = 14, length = 1, rightPad = true)
    private String dot8=",";

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    @FileField(order = 15, length = 7, rightPad = true)
    private String glAcct="GL_ACCT";

    @FileField(order = 16, length = 1, rightPad = true)
    private String dot9=",";

    /**
     * 借贷方向
     * 表字段:DRCR
     */
    @FileField(order = 17, length = 22, rightPad = true)
    private String drcr="DEBIT_CREDIT_INDICATOR";

    @FileField(order = 18, length = 1, rightPad = true)
    private String dot10=",";

    /**
     * 交易金额
     * 表字段:GL_AMOUNT
     */
    @FileField(order = 19, length = 9, rightPad = true)
    private String glAmount="GL_AMOUNT";

    @FileField(order = 20, length = 1, rightPad = true)
    private String dot11=",";

    /**
     * 入账笔数
     * 表字段:POSTING_CNT
     */
    @FileField(order = 21, length = 8, rightPad = true)
    private String glCnt = "GL_COUNT";

    @FileField(order = 22, length = 1, rightPad = true)
    private String dot12=",";

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    @FileField(order = 23, length = 12, rightPad = true)
    private String postingDate ="POSTING_DATE";

    @FileField(order = 24, length = 1, rightPad = true)
    private String dot13=",";

    /**
     * 处理标识
     * 表字段:PROCESS_TYPE
     */
    @FileField(order = 25, length = 12, rightPad = true)
    private String processType="PROCESS_TYPE";

    @FileField(order = 26, length = 1, rightPad = true)
    private String dot14=",";

    /**
     * 分期订单号
     * 表字段:ORDER_ID
     */
    @FileField(order = 27, length = 8, rightPad = true)
    private String orderId="ORDER_ID";

    @FileField(order = 28, length = 1, rightPad = true)
    private String dot15=",";

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    @FileField(order = 29, length = 10, rightPad = true)
    private String absStatus="ABS_STATUS";

    @FileField(order = 30, length = 1, rightPad = true)
    private String dot16=",";

    /**
     * 原始交易码
     * 表字段:TXN_CODE_ORIG
     */
    @FileField(order = 31, length = 13, rightPad = true)
    private String txnCodeOrig="TXN_CODE_ORIG";

    @FileField(order = 32, length = 1, rightPad = true)
    private String dot17=",";

    /**
     * 渠道ID
     * 表字段:CHANNEL_ID
     */
    @FileField(order = 33, length = 10, rightPad = true)
    private String channelId="CHANNEL_ID";

    @FileField(order = 34, length = 1, rightPad = true)
    private String dot18=",";

    /**
     * 子渠道ID
     * 表字段:SUBCHANNEL_ID
     */
    @FileField(order = 35, length = 13, rightPad = true)
    private String subchannelId="SUBCHANNEL_ID";

    @FileField(order = 36, length = 1, rightPad = true)
    private String dot19=",";

    /**
     * 资金源ID
     * 表字段:FUND_ID
     */
    @FileField(order = 37, length = 7, rightPad = true)
    private String fundId="FUND_ID";

    @FileField(order = 38, length = 1, rightPad = true)
    private String dot20=",";

    /**
     * 平台ID
     * 表字段:PLATFORM_ID
     */
    @FileField(order = 39, length = 11, rightPad = true)
    private String platformId="PLATFORM_ID";

    @FileField(order = 40, length = 1, rightPad = true)
    private String dot21=",";

    /**
     * 商户ID
     * 表字段:MERCHANT_NUMBER
     */
    @FileField(order = 41, length = 15, rightPad = true)
    private String merchantNumber="MERCHANT_NUMBER";

    @FileField(order = 42, length = 1, rightPad = true)
    private String dot22=",";

    /**
     * 五级分类标识
     */
    @FileField(order = 43, length = 19, rightPad = true)
    private String fiveTypeIndicator="FIVE_TYPE_INDICATOR";

    @FileField(order = 44, length = 1, rightPad = true)
    private String dot23=",";

    /**
     * abs类型
     */
    @FileField(order = 45, length = 8, rightPad = true)
    private String absType="ABS_TYPE";

    @FileField(order = 46, length = 1, rightPad = true)
    private String dot24=",";

    /**
     * crdNumber
     */
    @FileField(order = 47, length = 10)
    private String crdNumber="CRD_NUMBER";

    @FileField(order = 48, length = 1)
    private String dot33=",";

    /**
     * crdProductNumber
     */
    @FileField(order = 49, length = 18)
    private String crdProductNumber="CRD_PRODUCT_NUMBER";

    @FileField(order = 50, length = 1)
    private String dot34=",";

    /**
     * crdType
     */
    @FileField(order = 51, length = 8)
    private String crdType="CRD_TYPE";

    @FileField(order = 52, length = 1)
    private String dot35=",";

    /**
     * txnAmt
     */
    @FileField(order = 53, length = 7)
    private String txnAmt="TXN_AMT";

    @FileField(order = 54, length = 1)
    private String dot36=",";

    /**
     * txnCurrency
     */
    @FileField(order = 55, length = 12)
    private String txnCurrency="TXN_CURRENCY";

    @FileField(order = 56, length = 1)
    private String dot37=",";

    /**
     * settleAmt
     */
    @FileField(order = 57, length = 10)
    private String settleAmt="SETTLE_AMT";

    @FileField(order = 58, length = 1)
    private String dot38=",";

    /**
     * settleCurrency
     */
    @FileField(order = 59, length = 15)
    private String settleCurrency="SETTLE_CURRENCY";

    @FileField(order = 60, length = 1)
    private String dot39=",";

    /**
     * txnDate
     */
    @FileField(order = 61, length = 8)
    private String txnDate="TXN_DATE";

    @FileField(order = 62, length = 1)
    private String dot40=",";

    /**
     * txnSource
     */
    @FileField(order = 63, length = 10)
    private String txnSource="TXN_SOURCE";

    @FileField(order = 64, length = 1)
    private String dot41=",";

    /**
     * txnDescription
     */
    @FileField(order = 65, length = 15)
    private String txnDescription="TXN_DESCRIPTION";

    @FileField(order = 66, length = 1)
    private String dot42=",";

    /**
     * currencyRate
     */
    @FileField(order = 67, length = 13)
    private String currencyRate="CURRENCY_RATE";

    @FileField(order = 68, length = 1)
    private String dot43=",";

    /**
     * rrn
     */
    @FileField(order = 69, length = 3)
    private String rrn="RRN";

    @FileField(order = 70, length = 1)
    private String dot44=",";

    /**
     * acqId
     */
    @FileField(order = 71, length = 6)
    private String acqId="ACQ_ID";

    @FileField(order = 72, length = 1)
    private String dot45=",";

    /**
     * mkUpFeeInd
     */
    @FileField(order = 73, length = 13)
    private String mkUpFeeInd="MK_UP_FEE_IND";

    @FileField(order = 74, length = 1)
    private String dot46=",";

    /**
     * 产品类型
     */
    @FileField(order = 75, length = 12)
    private String productType="PRODUCT_TYPE";

    @FileField(order = 76, length = 1, rightPad = true)
    private String dot47=",";

    /**
     * 卡组织
     */
    @FileField(order = 77, length = 11)
    private String cardOrgNumber="CARD_SCHEME";

    @FileField(order = 78, length = 1)
    private String dot48=",";

    /**
     *
     * va号:VA_NUMBER
     */
    @FileField(order = 79, length = 9)
    private String vaNum="VA_NUMBER";

    @FileField(order = 80, length = 1)
    private String dot49=",";

    /**
     *
     * VATA_COUPLE_INDICATOR
     */
    @FileField(order = 81, length = 21)
    private String vataCoupleIndicator="VATA_COUPLE_INDICATOR";
}
