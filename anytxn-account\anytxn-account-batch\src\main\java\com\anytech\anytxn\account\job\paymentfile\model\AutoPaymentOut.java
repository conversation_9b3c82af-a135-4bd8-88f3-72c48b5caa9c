package com.anytech.anytxn.account.job.paymentfile.model;

import com.anytech.anytxn.business.dao.account.model.AutoPaymentLog;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2021/3/23
 */
@Getter
@Setter
public class AutoPaymentOut {

    /**
     * 	银行编号
     */
    private String bank;
    /**
     * 分行代号
     */
    private String branch;
    /**
     * 记录产生日期
     */
    private String inpDate;
    /**
     * 扣账账户号码
     */
    private String acct;
    /**
     * 扣账金额
     */
    private String amount;
    /**
     * 扣账币种
     */
    private String curr;
    /**
     * 扣账日期
     */
    private String trnDate;
    /**
     * 信用卡卡号
     */
    private String cardNbr;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号码
     */
    private String id;
    /**
     * 扣款账户类型
     */
    private String acctType;
    /**
     * 排序优先级
     */
    private String order;
    /**
     * 跨行自扣账号
     */
    private String obank;
    /**
     * 保留字段
     */
    private String reservd;
    /**
     * 姓名
     */
    private String name;
    /**
     * 账号附加信息
     */
    private String addAcct;
    /**
     * 保留域2
     */
    private String revs2;
    /**
     * 公司编号
     */
    private String business;

    public static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    public AutoPaymentOut create(AutoPaymentLog log){
        AutoPaymentOut out = new AutoPaymentOut();

        out.setBank(log.getAutoPaymentDebitBankNumber());
        out.setBranch(log.getAutoPaymentBranchNumber() == null ? "": log.getAutoPaymentBranchNumber());
        out.setInpDate(log.getCreateTime().format(formatter));
        out.setAcct(log.getAutoPaymentDebitAcctNumber());
        // 输出带两位小数的字符串
        String amountStr = log.getFinalPaymentAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString();
        // 去除字符串中的.
        amountStr = amountStr.replaceAll("\\.","");
        amountStr = String.format("%012d", Long.valueOf(amountStr));
        out.setAmount(amountStr);
        out.setCurr(log.getOriginalCurrency());
        out.setTrnDate(log.getTrnDate().format(formatter));
        out.setCardNbr(log.getCardNbr());
        out.setIdType(log.getIdType());
        out.setId(log.getIdNumber());
        out.setAcctType(log.getAcctType());
        out.setName(log.getCustomerName());
        out.setOrder("0001");
        out.setObank(" ");
        out.setReservd(" ");
        out.setAddAcct(" ");
        out.setBusiness(" ");
        out.setRevs2(" ");

        return out;
    }
}
