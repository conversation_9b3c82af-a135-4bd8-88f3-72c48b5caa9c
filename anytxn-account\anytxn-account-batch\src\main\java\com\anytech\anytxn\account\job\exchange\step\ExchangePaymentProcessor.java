package com.anytech.anytxn.account.job.exchange.step;

import com.anytech.anytxn.account.base.service.IAccountManageInfoService;
import com.anytech.anytxn.business.base.account.domain.bo.ExchangePaymentBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.ExPurchaseInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import jakarta.annotation.Resource;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-09 14:54
 **/
public class ExchangePaymentProcessor implements ItemProcessor<ExPurchaseInfoDTO, ExchangePaymentBO> {
    @Autowired
    private IAccountManageInfoService accountManageInfoService;

    @Resource
    private AccountManagementInfoMapper accountManagementInfoMapper;


    /**
     * 购汇批量
     */
    @Override
    public ExchangePaymentBO process(ExPurchaseInfoDTO exPurchaseInfo) throws Exception {
        ExchangePaymentBO exchangePaymentBO = null;
        //自动购汇类型设置为0-溢缴款购汇  0-签约
        if ("0".equals(exPurchaseInfo.getAutoForeignExchangePurchaseType()) && "0".equals(exPurchaseInfo.getContractType())) {
            List<AccountManagementInfo> accountManagementInfos = accountManagementInfoMapper.selectByCustomerIdAndOrganizationNumber(exPurchaseInfo.getCustomerId(), OrgNumberUtils.getOrg());
            for (AccountManagementInfo accountManagementInfo : accountManagementInfos) {
                if (accountManagementInfo.getTotalDueAmount().compareTo(BigDecimal.ZERO) > 0) {
                    exchangePaymentBO = accountManageInfoService.batchProcess4ExchangePayment(accountManagementInfo);
                }
            }
        }
        return exchangePaymentBO;
    }
}
