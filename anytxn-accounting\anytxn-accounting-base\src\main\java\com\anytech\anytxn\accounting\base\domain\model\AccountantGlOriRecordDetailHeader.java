package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TextLineObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TextLineObject(encoding = "UTF-8")
public class AccountantGlOriRecordDetailHeader implements Serializable {
    /**
     * CP_FINEXT.CB_EXT_REF_NO 8 汇总日期
     */
    @FileField(order = 1, length = 9)
    private String sumDate="POST_DATE";

    @FileField(order = 2, length = 1)
    private String dot=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 科目号
     */
    @FileField(order = 3, length = 7)
    private String subjectCodeId="ACCOUNT";

    @FileField(order = 4, length = 1)
    private String dot2=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 120 科目描述
     */
    @FileField(order = 5, length = 12)
    private String subjectDesc="ACCOUNT_NAME";

    @FileField(order = 6, length = 1)
    private String dot3=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 2 借贷记标志
     */
    @FileField(order = 7, length = 8)
    private String debitCreditSide="DE_OR_CR";

    @FileField(order = 8, length = 1)
    private String dot4=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 汇总金额
     */
    @FileField(order = 9, length = 8)
    private String totalAmt="POST_AMT";

    @FileField(order = 10, length = 1)
    private String dot5=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 3 币种
     */
    @FileField(order = 11, length = 9)
    private String sumCurrency="POST_CURR";

    @FileField(order = 12, length = 1)
    private String dot6=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 交易码
     */
    @FileField(order = 13, length = 9)
    private String txnCode="TRAN_CODE";

    @FileField(order = 14, length = 1)
    private String dot7=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 120 交易描述
     */
    @FileField(order = 15, length = 14)
    private String txnDesc="TRAN_CODE_DESC";

    @FileField(order = 16, length = 1)
    private String dot8=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 6 帐产品
     */
    @FileField(order = 17, length = 15)
    private String accountProduct="ACCOUNT_PRODUCT";

    @FileField(order = 18, length = 1)
    private String dot9=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 卡号
     */
    @FileField(order = 19, length = 11)
    private String crdNumber="CARD_NUMBER";

    @FileField(order = 20, length = 1)
    private String dot10=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 50 商户号
     */
    @FileField(order = 21, length = 3)
    private String merchantId="MID";

    @FileField(order = 22, length = 1)
    private String dot11=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 17 完整交易日期
     */
    @FileField(order = 23, length = 9)
    private String txnDate="TRAN_DATE";

    @FileField(order = 24, length = 1)
    private String dot12=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 交易来源
     */
    @FileField(order = 25, length = 11)
    private String source="TRAN_SOURCE";

    @FileField(order = 26, length = 1)
    private String dot13=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 3 交易币种
     */
    @FileField(order = 27, length = 9)
    private String txnCurrency="TRAN_CURR";

    @FileField(order = 28, length = 1)
    private String dot14=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 交易金额
     */
    @FileField(order = 29, length = 8)
    private String txnAmt="TRAN_AMT";

    @FileField(order = 30, length = 1)
    private String dot15=",";
    /**
     * CP_FINEXT.CB_EXT_REF_NO 30 货币汇率
     */
    @FileField(order = 31, length = 13)
    private String currencyRate="EXCHANGE_RATE";

    @FileField(order = 32, length = 1)
    private String dot16=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 32 参考号
     */
    @FileField(order = 33, length = 3)
    private String rrn="RRN";

    @FileField(order = 34, length = 1)
    private String dot17=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 6 卡产品
     */
    @FileField(order = 35, length = 12)
    private String cardProduct="CARD_PRODUCT";

    @FileField(order = 36, length = 1)
    private String dot18=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 卡组织
     */
    @FileField(order = 37, length = 11)
    private String cardOrganization="CARD_SCHEME";

    @FileField(order = 38, length = 1)
    private String dot19=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 模块标志
     */
    @FileField(order = 39, length = 8)
    private String moduleFlag="BUSINESS";

    @FileField(order = 40, length = 1)
    private String dot20=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 模块标志
     */
    @FileField(order = 41, length = 6)
    private String acqId="ACQ_ID";

    @FileField(order = 42, length = 1)
    private String dot21=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 模块标志
     */
    @FileField(order = 43, length = 12)
    private String productType="PRODUCT_TYPE";

    @FileField(order = 44, length = 1)
    private String dot22=",";

    /**
     * ACCOUNT_MANAGEMENT_ID 23 管理账户
     */
    @FileField(order = 45, length = 23)
    private String accountManagementInfo="ACCOUNT_MANAGEMENT_ID";

    @FileField(order = 46, length = 1)
    private String dot23=",";

    /**
     *
     * va号:VA_NUMBER
     */
    @FileField(order = 47, length = 9)
    private String vaNum="VA_NUMBER";

    @FileField(order = 48, length = 1)
    private String dot24=",";

    /**
     *
     * VATA_COUPLE_INDICATOR
     */
    @FileField(order = 49, length = 21)
    private String vataCoupleIndicator="VATA_COUPLE_INDICATOR";

    @Override
    public String toString() {
        return sumDate + dot + subjectCodeId + dot2 + subjectDesc + dot3 + debitCreditSide + dot4 +
                totalAmt + dot5 + sumCurrency + dot6 + txnCode + dot7 + txnDesc + dot8 + accountProduct + dot9 +
                crdNumber + dot10 + merchantId + dot11 + txnDate + dot12 + source + dot13 + txnCurrency + dot14 +
                txnAmt + dot15 + currencyRate + dot16 + rrn + dot17 + cardProduct + dot18 + cardOrganization + dot19 +
                moduleFlag + dot20 + acqId + dot21 + productType + dot22 + accountManagementInfo + dot23 + vaNum + dot24 +
                vataCoupleIndicator;
    }
}
