package com.anytech.anytxn.account.base.constants;

/**
 * 授权匹配标志
 *
 * <AUTHOR>
 * @date 2019-08-28
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 *
 */
public enum AuthMatchIndicator {
    /**
     * 授权匹配标志
     * 0=未匹配授权；1=匹配授权
     */
    NOT_MATCH_AUTH("0", "未匹配授权"),
    MATCH_AUTH("1", "匹配授权")
    ;
    private String code;
    private String desc;


    AuthMatchIndicator(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
