package com.anytech.anytxn.accounting.batch.job.report.step.amountdetail.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @Date 2020/9/11 11:48
 */
@Getter
@Setter
@ToString
public class ReceivablesDetail {

    //分行号
    private String bankNumber;
    //客户号
    private String customerId;
    //客户名称
    private String customerName;
    //产品ID
    private String productId;
    //描述
    private String desc;
    //币种
    private String currencyCode;
    //入账日期
    private String postDate;
    //科目号
    private String subjectNumber;
    //科目名称
    private String subjectName;
    //借贷方向
    private String debitCreditIndcator;
    //发生额
    private BigDecimal createAmount;
}
