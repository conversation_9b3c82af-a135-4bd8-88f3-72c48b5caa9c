package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlactbalDTO;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlinsbalDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;

/**
 * <AUTHOR>
public interface IBalService {

    /**
     * 分期余额汇总
     * @param installOrderDTO
     * @return
     */
    AccountantGlinsbalDTO installOrderSum(InstallOrderDTO installOrderDTO);

    /**
     * 分户余额汇总
     * @param accountBalanceInfoDTO
     * @return
     */
    AccountantGlactbalDTO accountBalanceSum(AccountBalanceInfoDTO accountBalanceInfoDTO);
}
