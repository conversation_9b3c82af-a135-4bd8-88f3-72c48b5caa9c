package com.anytech.anytxn.account.utils;

import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2021/1/6
 */
@Slf4j
public class FileUtil {

    /**
     * 创建文件夹
     * @param filePath
     * @param ableDelete
     * @return
     */
    public static File mkdirFile(String filePath, boolean ableDelete){
        File file = new File(filePath);
        // 创建父级目录
        if (!file.getParentFile().exists()) {
            boolean mkdirs = file.getParentFile().mkdirs();
            if (!mkdirs) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.CF_DE);
            }
        }

        // 删除过期文件
        if (ableDelete && file.exists()) {
            log.info("文件已存在，开始删除原文件"+file.getAbsolutePath());
            boolean delete = file.delete();
            if (!delete) {
                log.error("文件已存在，但没有删除成功");
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.DFF);
            }
        }

        try {
            boolean newFile = file.createNewFile();
            if (!newFile) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.CFF);
            }
        } catch (IOException e) {
            log.error("创建文件失败！文件：{}", file.getPath(), e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR,  AccountingRepDetailEnum.CFF);
        }

        return file;
    }

    public static File[] getChildFiles(String filePath){
        File file = new File(filePath);
        if (file.exists()) {
            return file.listFiles();
        }
        return null;
    }

    public static boolean deleteChildDir(String filePath){
        File[] childFiles = getChildFiles(filePath);
        if (childFiles != null) {
            for (File file : childFiles) {
                if (file.exists() && file.isDirectory()) {
                    if (file.getName().equals(OrgNumberUtils.getOrg())){
                        try {
                            FileUtils.deleteDirectory(file);
                            log.info("删除文件，文件路径：{}", file.getPath());
                        } catch (IOException e) {
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.DFF);
                        }
                    }
                }
            }
        }

        return true;
    }

    public static File getFileFile(String filePath){
        File file = new File(filePath);
        if (file.exists() && file.isFile()) {
            return file;
        }
        return null;
    }

    public static String getString(Object d){
        if (d == null) {
            return "";
        } else if (d instanceof Integer){
           return String.valueOf(d);
        } else if (d instanceof Long){
            return String.valueOf(d);
        } else if (d instanceof BigDecimal){
            return d.toString();
        } else if (d instanceof LocalDate){
            return ((LocalDate)d).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }

        return "";
    }

    /**
     * 输出文件
     *
     * @param exportFilePath  输出文件的路径
     * @param fileData 写入文件的数据
     */
    public static void outFile(String exportFilePath, String fileData) {
        File file = new File(exportFilePath);
        try {
            if (file.exists()) {
                FileUtils.deleteQuietly(file);
            }
            FileUtils.write(file, fileData, "GBK", Boolean.TRUE);
        } catch (IOException e) {
            log.error("写入文件失败");
        }
    }
}
