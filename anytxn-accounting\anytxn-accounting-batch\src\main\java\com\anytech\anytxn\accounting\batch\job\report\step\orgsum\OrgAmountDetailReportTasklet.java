package com.anytech.anytxn.accounting.batch.job.report.step.orgsum;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.orgsum.dto.DistIOrganizationPost;
import com.anytech.anytxn.accounting.batch.job.report.step.orgsum.model.OrgAmountDetail;
import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportListTasklet;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.config.OrgAmountDetailReportConfigurer;
import com.anytech.anytxn.accounting.mapper.AccountantGlvcherSumSelfMapper;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlvcherSum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlacgn;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分机构落款（日/月/季/年）
 * <AUTHOR>
 * @date 2020/9/14
 */
@Slf4j
public class OrgAmountDetailReportTasklet extends AbstractReportListTasklet {

    private TimeTypeEnum timeTypeEnum;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private TPmsGlacgnSelfMapper tPmsGlacgnSelfMapper;
    @Autowired
    private AccountantGlvcherSumSelfMapper accountantGlvcherSumSelfMapper;

    public OrgAmountDetailReportTasklet(BaseConfigurer baseConfigurer, TimeTypeEnum timeTypeEnum) {
        super(baseConfigurer);
        this.timeTypeEnum = timeTypeEnum;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        if (reportConfigurer.isAbleReport()) {
            List<TAmsGlvcherSum> glvcherSumList = loadGlvcherSumModels();

            List<OrgAmountDetail> data = modelToReport(glvcherSumList);
            out(data);
        }

        return RepeatStatus.FINISHED;
    }

    /**
     * 当前日期资产/负债传票（已处理）
     * @return
     */
    private List<TAmsGlvcherSum> loadGlvcherSumModels(){
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        List<TAmsGlvcherSum> result = null;
        switch (timeTypeEnum) {
            case D:
//                organizationInfo.setToday(LocalDate.of(2020,8,19));
                result = accountantGlvcherSumSelfMapper.selectByPostDateAndR0(organizationInfo.getToday(),OrgNumberUtils.getOrg());
                break;
            case M:
//                organizationInfo.setToday(LocalDate.of(2020,5,31));
                if (TimeUtils.isEndMonth(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.mothRand(organizationInfo.getToday());
                    result = accountantGlvcherSumSelfMapper.selectByPostDateRandAndR0(mothRand.getKey(), mothRand.getValue(),OrgNumberUtils.getOrg());
                }
                break;
            case Q:
//                organizationInfo.setToday(LocalDate.of(2020,6,30));
                if (TimeUtils.isEndQuarter(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.quarterRand(organizationInfo.getToday());
                    result = accountantGlvcherSumSelfMapper.selectByPostDateRandAndR0(mothRand.getKey(), mothRand.getValue(),OrgNumberUtils.getOrg());
                }
                break;
            case Y:
//                organizationInfo.setToday(LocalDate.of(2020,12,31));
                if (TimeUtils.isEndYear(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> yearRand = TimeUtils.yearRand(organizationInfo.getToday());
                    result = accountantGlvcherSumSelfMapper.selectByPostDateRandAndR0(yearRand.getKey(), yearRand.getValue(),OrgNumberUtils.getOrg());
                }
                break;
            default:
                break;
        }

        log.info("timeType: {}, 加载业务数据量: {}", timeTypeEnum, result == null ? 0 : result.size());
        return result;
    }

    /**
     * 业务数据转报表数据
     * @return 入账汇总,入账会计,入账差异汇总,入账差异会计
     */
    private List<OrgAmountDetail> modelToReport(List<TAmsGlvcherSum> glvcherSumList){
        List<OrgAmountDetail> result = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(glvcherSumList)) {
            Map<String, List<TAmsGlvcherSum>> group = glvcherSumList.stream().collect(Collectors.groupingBy(x -> x.getBranchid() + x.getCurrCode() + x.getGlAcct()));

            for (Map.Entry<String, List<TAmsGlvcherSum>> entry : group.entrySet()) {
                List<TAmsGlvcherSum> value = entry.getValue();
                TAmsGlvcherSum temple = value.get(0);

                OrgAmountDetail detail = new OrgAmountDetail();
                detail.setBranchId(temple.getBranchid());
                detail.setFundId(temple.getFundId());
                detail.setCurrCode(temple.getCurrCode());
                detail.setGlAcct(temple.getGlAcct());
                TPmsGlacgn pmsGlacgn = tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(temple.getOrganizationNumber(), temple.getBranchid(), temple.getGlAcct(), temple.getCurrCode());
                detail.setGlAcctName(pmsGlacgn.getGlAcctName());
                detail.setGlAcctType(pmsGlacgn.getGlClass());
                detail.setPostDate(temple.getPostingDate());
                detail.setCMount(value.stream().map(TAmsGlvcherSum::getOccurCr).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                detail.setDMount(value.stream().map(TAmsGlvcherSum::getOccurDb).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

                result.add(detail);
            }
        }

        return result;
    }

    /**
     * 输出报表
     * @param data
     */
    private void out(List<OrgAmountDetail> data) throws FileNotFoundException {
        if (CollectionUtils.isNotEmpty(data)) {
            OrgAmountDetailReportConfigurer reportConfigurer0 = (OrgAmountDetailReportConfigurer)reportConfigurer;
            Map<String, Object> param = new HashMap<>();
            reportConfigurer0.putParam(param, timeTypeEnum);

            outReport(Lists.newArrayList(DistIOrganizationPost.resetReportData(data, timeTypeEnum)), reportConfigurer0.outFile(timeTypeEnum), reportConfigurer0.outFileName(timeTypeEnum), reportConfigurer0.templeFile(timeTypeEnum), param);
        }
    }
}
