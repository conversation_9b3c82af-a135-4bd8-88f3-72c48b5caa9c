package com.anytech.anytxn.accounting.batch.job.report.step.installfree;

import com.anytech.anytxn.accounting.batch.job.report.step.installfree.dto.PostInstallFee;
import com.anytech.anytxn.accounting.batch.job.report.step.installfree.model.InstallFreeReport;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallProductInfo;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoSelfMapper;
import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportTasklet;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分期手续费 (del)
 * <AUTHOR>
 * @date 2020/9/12
 */
@Slf4j
public class InstallFreeReportTasklet extends AbstractReportTasklet<AccountantGlvcher, PostInstallFee> {

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private AccountantGlvcherSelfMapper accountantGlvcherSelfMapper;
    @Autowired
    private InstallProductInfoSelfMapper installProductInfoSelfMapper;
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private ParmTransactionCodeSelfMapper parmTransactionCodeSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    public InstallFreeReportTasklet(BaseConfigurer baseConfigurer) {
        super(baseConfigurer);
    }

    @Override
    protected List<AccountantGlvcher> loadModels() {
        OrganizationInfoResDTO organizationInfo =
                organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

//        organizationInfo.setToday(LocalDate.of(2020,5,12));
        return accountantGlvcherSelfMapper.selectByPostDateAndOrderAndCode8(organizationInfo.getOrganizationNumber(), organizationInfo.getToday());
    }

    @Override
    protected List<PostInstallFee> modelToReport(List<AccountantGlvcher> models) {
        List<InstallFreeReport> result = Lists.newArrayList();
        Map<String, List<AccountantGlvcher>> glvcherGroup = models.stream().collect(Collectors.groupingBy(x -> x.getGlobalFlowNo() + x.getOrderId() + x.getTxnCodeOrig() + x.getCurrCode() + x.getDrcr() + x.getGlAcct()));
        for (Map.Entry<String, List<AccountantGlvcher>> entry : glvcherGroup.entrySet()) {
            List<AccountantGlvcher> accountantGlvchers = entry.getValue();
            AccountantGlvcher temple = accountantGlvchers.get(0);

//            // 贷科目
//            Optional<AccountantGlvcher> c = accountantGlvchers.stream().filter(x -> x.getDrcr().equals("C")).findFirst();
//            // 借科目
//            Optional<AccountantGlvcher> d = accountantGlvchers.stream().filter(x -> x.getDrcr().equals("D")).findFirst();
//            // 汇总金额
//            Optional<BigDecimal> glAmount = accountantGlvchers.stream().map(AccountantGlvcher::getGlAmount).reduce(BigDecimal::add);

            InstallFreeReport reportDTO = new InstallFreeReport();
            reportDTO.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            reportDTO.setOrganizationNumber(temple.getOrganizationNumber());
            // 入账日期
            reportDTO.setPostingDate(temple.getPostingDate());
            // 借科目
            if (temple.getDrcr().equals("C")) {
                reportDTO.setCAcct(temple.getGlAcct());
            } else {
                reportDTO.setDAcct(temple.getGlAcct());
            }
//            c.ifPresent(accountantGlvcher -> reportDTO.setCAcct(accountantGlvcher.getGlAcct()));
//            // 贷科目
//            d.ifPresent(accountantGlvcher -> reportDTO.setDAcct(accountantGlvcher.getGlAcct()));
//            // 交易金额
//            reportDTO.setGlAmount(glAmount.orElse(BigDecimal.ZERO));
            reportDTO.setGlAmount(temple.getGlAmount());
            // 交易码
            reportDTO.setTransactionCode(temple.getTxnCodeOrig());
            // 交易码描述
            ParmTransactionCode transactionCode = parmTransactionCodeSelfMapper.selectByOrgNumberAndCode(temple.getOrganizationNumber(), temple.getTxnCodeOrig());
            reportDTO.setDiscription(transactionCode.getDescription());
            // 币种
            reportDTO.setCurrCode(temple.getCurrCode());
            // 分期订单号
            reportDTO.setOrderId(temple.getOrderId());
            // 卡号
            InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(temple.getOrderId());
            reportDTO.setCardNumber(installOrder.getCardNumber());
            // 分期产品
            InstallProductInfo productInfo = installProductInfoSelfMapper.selectByIndex(installOrder.getOrganizationNumber(), installOrder.getProductCode());
            reportDTO.setProductCode(installOrder.getProductCode());
            reportDTO.setProductDescription(productInfo.getProductDesc());

            result.add(reportDTO);
        }

        // 进一步转换
        return Lists.newArrayList(PostInstallFee.resetReportData(result));
    }
}
