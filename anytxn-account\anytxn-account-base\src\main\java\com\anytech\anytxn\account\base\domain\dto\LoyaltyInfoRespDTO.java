package com.anytech.anytxn.account.base.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/5  15:48
 * @Version 1.0
 */
@Data
public class LoyaltyInfoRespDTO<T> {
    private String cardNumber;
    private String accountNumber;
    private String productNumber;
    private List<T> projectList;

    public LoyaltyInfoRespDTO(String cardNumber, String accountNumber, String productNumber) {
        this.cardNumber = cardNumber;
        this.accountNumber = accountNumber;
        this.productNumber = productNumber;
    }

    public LoyaltyInfoRespDTO() {
    }
}