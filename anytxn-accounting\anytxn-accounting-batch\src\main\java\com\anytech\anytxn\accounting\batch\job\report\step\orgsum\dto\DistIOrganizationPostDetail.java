package com.anytech.anytxn.accounting.batch.job.report.step.orgsum.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 具体数据
 * <AUTHOR>
 * @Date 2020/9/11 11:48
 */
@Getter
@Setter
@ToString
public class DistIOrganizationPostDetail {

    //分行号
    private String bankNumber;
    private String netNumber;
    //币种
    private String currencyCode;
    //科目数
    private String subjectNumber;
    //科目名称
    private String subjectName;
    //科目分类
    private String subjectCategory;
    //入账日期
    private String postDate;


    //借方发生额
    private BigDecimal debitAmount;
    //贷方发生额
    private BigDecimal crebitAmount;

}
