package com.anytech.anytxn.account.base.enums;

/**
 * 交易类型
 *
 * <AUTHOR>
 * @date 2018-9-24
 *
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 */
public enum TransactionTypeCodeEnum {


    /**
     * 00000:汇总
     * DCS0O:溢缴款
     */
    AGGREGATION("00000", "汇总"),

    OVER_REPAYMENT("DCS0O", "溢缴款");


    private String code;
    private String reason;


    TransactionTypeCodeEnum(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }
    }
