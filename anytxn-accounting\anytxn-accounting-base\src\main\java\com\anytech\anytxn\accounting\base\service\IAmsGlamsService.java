package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsPageDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;

import java.util.List;

/**
 * 交易会计流水表
 *
 * <AUTHOR>
public interface IAmsGlamsService {

    /**
     * 获取交易会计流水表中管理账户id数
     *
     * @param partitionKey 分区键值
     * @return int
     */
    int getAccountManageIdCount(String partitionKey);

    /**
     * 获取交易会计流水表中管理账户区间
     *
     * @param partitionKey 分区键值
     * @param rowNumbers   rownum
     * @return List<String>
     */
    List<String> queryAccountManageIds(String partitionKey, List<Integer> rowNumbers);

    /**
     * 根据id查询当前交易会计流水表数据
     *
     * @param tAmsGlamsId
     * @return
     */
    TAmsGlamsPageDTO findTamsGlamsById(String tAmsGlamsId);

    /**
     * 交易会计流水表
     *
     * @param tAmsGlvcherId
     * @param tableSource
     * @date 2020-7-28
     */
    TAmsGlvcherDTO findtAmsGlvcherById(String tAmsGlvcherId, String tableSource);

    /**
     * 分页查询会计流水表数据
     *
     * @param page
     * @param rows
     * @param postingDate
     * @param accountManagementId
     * @param globalFlowNo
     * @return
     */
    PageResultDTO<TAmsGlamsPageDTO> getTamsGlamsByPage(Integer page, Integer rows, String postingDate, String accountManagementId, String globalFlowNo);

    /**
     * TODO
     *
     * @param page
     * @param rows
     * @param accountManagementId
     * @param globalFlowNo
     * @param postingDate
     * @param tableSource
     * @param processType
     * @date 2020-7-28
     */
    PageResultDTO<TAmsGlvcherDTO> getTamsGlvcherPage(Integer page, Integer rows, String accountManagementId, String globalFlowNo, String postingDate, String tableSource, String processType);

}
