package com.anytech.anytxn.account.base.enums;

/**
 * 约定扣款类型
 *
 * <AUTHOR>
 * @date 2018-9-22
 */
public enum AutoPaymentTypeEnum {

    /**
     * 1：直接借记账户自动扣款
     * 9：购汇扣款
     */

    NOT_SIGN("0", "未签约约定扣款"),

    DIRECT("1", "直接借记账户自动扣款"),

    OTHER("2", "他行借记账户自动扣款"),

    INDIRECT("9", "购汇扣款");

    private String code;
    private String reason;


    AutoPaymentTypeEnum(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }
}
