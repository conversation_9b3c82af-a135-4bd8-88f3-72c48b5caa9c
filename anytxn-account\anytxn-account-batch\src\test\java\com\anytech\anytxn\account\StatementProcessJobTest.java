package com.anytech.anytxn.account;

import jrx.anyscheduler.batch.job.single.EnableSingleBatchJobApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;

import java.util.ArrayList;
import java.util.List;

@EnableSingleBatchJobApplication

@EnableAccountingService
public class StatementProcessJobTest {

	public static void main(String[] args) {
		SpringApplication batch = new SpringApplication(StatementProcessJobTest.class);
		//关闭web模式，防止引入web包导致批处理无法自动关闭
		batch.setWebApplicationType(WebApplicationType.NONE);

		//jar包在服务器启动
		if(args!= null && args.length > 0){
			batch.run(args);
		}else{
			List<String> list = new ArrayList<>();
            list.add("--task.jobName=statementProcessJob");//账单处理
//            list.add("--task.jobName=lateFeeChargeJob");//滞纳金
//            list.add("--task.jobName=autoPaymentJob");//约定扣款
//			list.add("--task.jobName=delinquencyAgeJob");//延滞滚动
//            list.add("--task.jobName=exchangePaymentJob");//购汇
//            list.add("--task.jobName=incollectionJob");//催收
			list.add("--job.partitionKey=9999-9999");
			list.add("--job.runDate=" + System.currentTimeMillis());
			batch.run(list.toArray(new String[]{}));
		}
	}
}
