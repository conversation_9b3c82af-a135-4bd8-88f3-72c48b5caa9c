package com.anytech.anytxn.account;

import com.anytech.anytxn.business.EnableBisCoreService;
import com.anytech.anytxn.parameter.EnableParameterServiceAnnotation;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;


/**
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import(EnableAccountingApi.AccountingApiContextConfigurer.class)
public @interface EnableAccountingApi {

    @Configuration
    @EnableParameterServiceAnnotation
    @EnableAccountingService
    @EnableBisCoreService
    @ComponentScan(basePackages = {"com.anytech.anytxn.account.controller"})
    class AccountingApiContextConfigurer{
    }
}
