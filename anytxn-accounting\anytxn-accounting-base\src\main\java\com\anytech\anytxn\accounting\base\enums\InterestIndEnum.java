package com.anytech.anytxn.accounting.base.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 模块标识
 * @date 2020-02-16 14:06
 **/
public enum InterestIndEnum {
    /**
     * 0:非利息，非费用
     * 1:已缴营业税
     * 2:已缴增值税
     * 3:未缴增值税
     */
    CORE("0", "非利息，非费用"),
    CHARGE_OFF("1", "已缴营业税"),
    TAX("2", "已缴增值税"),
    YEAR_END("3", "未缴增值税"),
    ;
    private String code;
    private String desc;


    InterestIndEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String getValue(String code) {
        if (code == null) {
            return null;
        }
        InterestIndEnum[] var2 = values();
        int var3 = var2.length;
        for (int var4 = 0; var4 < var3; ++var4) {
            InterestIndEnum moduleFlagEnum = var2[var4];
            if (Objects.equals(code, moduleFlagEnum.getCode())) {
                return moduleFlagEnum.getDesc();
            }
        }
        return null;
    }
}
