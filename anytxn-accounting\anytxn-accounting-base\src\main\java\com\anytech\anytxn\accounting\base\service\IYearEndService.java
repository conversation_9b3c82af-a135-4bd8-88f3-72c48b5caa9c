package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.ActualBudgetDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgpDTO;
/**
 * <AUTHOR>
public interface IYearEndService {
/**
 * TODO
  * @param amsGlacgp
 * @return com.anytech.anytxn.accountant.dto.ActualBudgetDTO
 * <AUTHOR> @date 2020-7-28
 */
  ActualBudgetDTO actualBudget(TAmsGlacgpDTO amsGlacgp);
}
