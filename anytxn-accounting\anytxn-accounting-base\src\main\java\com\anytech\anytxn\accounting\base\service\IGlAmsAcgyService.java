package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgyDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
/**
 * <AUTHOR>
public interface IGlAmsAcgyService {
/**
 * TODO
  * @param page
 * @param rows
 * @param postingDate
 * @return com.anytech.anytxn.biz.common.dto.PageResultDTO<com.anytech.anytxn.accountant.dto.TAmsGlacgyDTO>
 * <AUTHOR> @date 2020-7-28
 */
    PageResultDTO<TAmsGlacgyDTO> getGlAmsAcgyPage(Integer page, Integer rows, String postingDate);

}
