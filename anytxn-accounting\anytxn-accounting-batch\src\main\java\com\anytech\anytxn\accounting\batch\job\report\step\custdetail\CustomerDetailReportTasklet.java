package com.anytech.anytxn.accounting.batch.job.report.step.custdetail;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.anytech.anytxn.accounting.batch.job.report.step.custdetail.dto.CustomerPostDetail;
import com.anytech.anytxn.accounting.batch.job.report.step.custdetail.model.CustomerDetail;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.config.CustomerDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportListTasklet;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlacgn;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 客户落账（日/月/季/年）
 * <AUTHOR>
 * @date 2020/9/14
 */
//TODO 有点慢
public class CustomerDetailReportTasklet extends AbstractReportListTasklet {

    private TimeTypeEnum timeTypeEnum;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private TPmsGlacgnSelfMapper tPmsGlacgnSelfMapper;
    @Autowired
    private AccountantGlvcherSelfMapper accountantGlvcherSelfMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;

    public CustomerDetailReportTasklet(BaseConfigurer baseConfigurer, TimeTypeEnum timeTypeEnum) {
        super(baseConfigurer);
        this.timeTypeEnum = timeTypeEnum;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        if (reportConfigurer.isAbleReport()) {
            List<AccountantGlvcher> glvcherList = loadGlvcherModels();

            List<CustomerDetail> data = modelToReport(glvcherList);
            out(data);
        }

        return RepeatStatus.FINISHED;
    }

    /**
     * 当前日期传票（已处理）
     * @return
     */
    private List<AccountantGlvcher> loadGlvcherModels(){
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        List<AccountantGlvcher> result = null;
        switch (timeTypeEnum) {
            case D:
//                organizationInfo.setToday(LocalDate.of(2020,2,02));
                result = accountantGlvcherSelfMapper.selectByPostDateAndR2(organizationInfo.getOrganizationNumber(), organizationInfo.getToday());
                break;
            case M:
//                organizationInfo.setToday(LocalDate.of(2020,5,31));
                if (TimeUtils.isEndMonth(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.mothRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndR2(organizationInfo.getOrganizationNumber(),mothRand.getKey(), mothRand.getValue());
                }
                break;
            case Q:
//                organizationInfo.setToday(LocalDate.of(2020,6,30));
                if (TimeUtils.isEndQuarter(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.quarterRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndR2(organizationInfo.getOrganizationNumber(),mothRand.getKey(), mothRand.getValue());
                }
                break;
            case Y:
//                organizationInfo.setToday(LocalDate.of(2020,12,31));
                if (TimeUtils.isEndYear(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> yearRand = TimeUtils.yearRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndR2(organizationInfo.getOrganizationNumber(),yearRand.getKey(), yearRand.getValue());
                }
                break;
            default:
                break;
        }

        return result;
    }

    /**
     * 业务数据转报表数据
     */
    private List<CustomerDetail> modelToReport(List<AccountantGlvcher> accountantGlvchers){
        List<CustomerDetail> result = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(accountantGlvchers)) {
            Map<String, AccountManagementInfo> aCache = Maps.newHashMap();
            Map<String, CustomerAuthorizationInfo> cCache = Maps.newHashMap();

            // 先根据管理账户分组
            Map<String, List<AccountantGlvcher>> group = accountantGlvchers.stream().collect(Collectors.groupingBy(x -> x.getAccountManagementId() + x.getCurrCode() + x.getGlAcct() + x.getDrcr()));
            List<CustomerDetail> cache = Lists.newArrayList();

            for (Map.Entry<String, List<AccountantGlvcher>> entry : group.entrySet()) {
                List<AccountantGlvcher> value = entry.getValue();
                AccountantGlvcher temple = value.get(0);

                CustomerDetail detail = new CustomerDetail();

                try {
                    // 客户号
                    AccountManagementInfo managementInfo = aCache.containsKey(temple.getAccountManagementId()) ? aCache.get(temple.getAccountManagementId()) : accountManagementInfoMapper.selectByPrimaryKey(temple.getAccountManagementId());
                    aCache.put(managementInfo.getAccountManagementId(), managementInfo);
                    detail.setCustomerId(managementInfo.getCustomerId());
                    // 账产品
                    detail.setProductNumber(managementInfo.getProductNumber());

                    // 客户名称
                    CustomerAuthorizationInfo customer = cCache.containsKey(managementInfo.getCustomerId()) ? cCache.get(managementInfo.getCustomerId()) : customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),managementInfo.getCustomerId());
                    cCache.put(customer.getCustomerId(), customer);
                    detail.setCustomerName(customer.getChineseName());
                }catch (Exception e){

                }
                // 管理账号
                detail.setManagementId(temple.getAccountManagementId());
                // 币种
                detail.setCurrCode(temple.getCurrCode());
                // 入账日期
                detail.setPostDate(temple.getPostingDate());
                // 科目号
                detail.setGlAcct(temple.getGlAcct());
                // 科目名称
                TPmsGlacgn pmsGlacgn = tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(temple.getOrganizationNumber(), temple.getBranchid(), temple.getGlAcct(), temple.getCurrCode());
                detail.setGlAcctName(pmsGlacgn.getGlAcctName());
                // 借贷方向
                detail.setDrcr(temple.getDrcr());
                // 发生额
                Optional<BigDecimal> decimal = value.stream().map(AccountantGlvcher::getGlAmount).reduce(BigDecimal::add);
                detail.setGlAmount(decimal.orElse(BigDecimal.ZERO));

                cache.add(detail);
            }

            // 再根据客户号分组
            Map<String, List<CustomerDetail>> group2 = cache.stream().collect(Collectors.groupingBy(x -> x.getCustomerId() + x.getCurrCode() + x.getGlAcct() + x.getDrcr()));
            for (Map.Entry<String, List<CustomerDetail>> entry : group2.entrySet()) {
                List<CustomerDetail> value = entry.getValue();

                Optional<BigDecimal> decimal = value.stream().map(CustomerDetail::getGlAmount).reduce(BigDecimal::add);
                CustomerDetail temple = value.get(0);
                temple.setGlAmount(decimal.orElse(BigDecimal.ZERO));

                result.add(temple);
            }
        }

        return result;
    }

    /**
     * 输出报表
     * @param data
     */
    private void out(List<CustomerDetail> data) throws FileNotFoundException {
        if (CollectionUtils.isNotEmpty(data)) {
            CustomerDetailReportConfigurer reportConfigurer0 = (CustomerDetailReportConfigurer)reportConfigurer;
            Map<String, Object> param = new HashMap<>();
            reportConfigurer0.putParam(param, timeTypeEnum);

            outReport(CustomerPostDetail.resetReportData(data, timeTypeEnum), reportConfigurer0.outFile(timeTypeEnum), reportConfigurer0.outFileName(timeTypeEnum), reportConfigurer0.templeFile(timeTypeEnum), param);
        }
    }
}
