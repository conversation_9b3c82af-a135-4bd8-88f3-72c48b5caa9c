package com.anytech.anytxn.account.base.utils;

import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;

/**
 * 之所以定义这个helper类,目前仅剩下能够更加规范响应码的定义,因为你要使用的话必须要先定义响应码
 * @author: sumingyue
 * @create: 2020/03/28 18:56
 */
public class AnyTxnHttpResponseHelper {

    /**
     * 不关注提示信息的场景
     * @return
     */
    public static AnyTxnHttpResponse<?> success() {
        return AnyTxnHttpResponse.success();
    }
    /**
     * 关注提示信息的场景,此提示信息需要做到简单明了,且人性化,语义更贴合使用者,而不是开发者角度.
     * 还有关于提示信息的说明,需要从贴近业务角度去描述,比如插入数据库发生主键重复,提示信息不要直接给类似ON DUPLICATE KEYZ这种让使用者一脸XX的提示信息,
     * 而应该给一个业务上更让人容易理解的数据重复的提示
     * @param detail
     * @return
     */
    public static AnyTxnHttpResponse<?> success(String detail) {
        return AnyTxnHttpResponse.successDetail(detail);
    }
    /**
     * 需要返回数据
     * @param page
     * @return
     */
    public static AnyTxnHttpResponse<PageResultDTO<?>> success(PageResultDTO page) {
        return AnyTxnHttpResponse.success(page);

    }
    /**
     * 响应码对应msg能够对错误做出精确描述,前端不需要detail补充即可看懂,并且提示信息能够做到人性化,简单明了
     * @param anyTxnAccountingRespCode
     * @return
     */
    public static AnyTxnHttpResponse<?> fail(AnyTxnAccountingRespCodeEnum anyTxnAccountingRespCode) {
        return AnyTxnHttpResponse.fail(anyTxnAccountingRespCode.getCode(), anyTxnAccountingRespCode.getMsg());
    }

    /**
     * 响应码对应msg不能做到对错误做出精确描述,需要通过detail来补充说明的
     * @param anyTxnAccountingRespCode
     * @param detail
     * @return
     */
    public static AnyTxnHttpResponse<?> failWithDetail(AnyTxnAccountingRespCodeEnum anyTxnAccountingRespCode, String detail) {
        return AnyTxnHttpResponse.fail(anyTxnAccountingRespCode.getCode(), anyTxnAccountingRespCode.getMsg(), detail);
    }


}
