package com.anytech.anytxn.accounting.batch.job.glvchersum.steps;

import com.anytech.anytxn.accounting.base.constants.AccountantConstants;
import com.anytech.anytxn.accounting.mapper.AccountantGlvcherSumSelfMapper;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlvcherSumC;
import com.anytech.anytxn.accounting.service.jdbc.AccountantJdbcService;
import com.anytech.anytxn.accounting.base.utils.AmountUtil;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class GlvcherSumWriter implements ItemWriter<AccountantGlvcher> {

    @Autowired
    private AccountantJdbcService accountantJdbcService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    private AccountantGlvcherSumSelfMapper accountantGlvcherSumSelfMapper;
    @Autowired
    private AccountantGlvcherSelfMapper accountantGlvcherSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Override
    public void write(Chunk<? extends AccountantGlvcher> chunk) throws Exception {
        List<? extends AccountantGlvcher> items = chunk.getItems();
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        List<AccountantGlvcher> updates = Lists.newArrayList();
        Map<String, TAmsGlvcherSumC> cache = new HashMap<>(items.size());
        AtomicInteger atomicInteger = new AtomicInteger(0);
        items.forEach(accountantGlvcher -> {
            // 更新
            updates.add(accountantGlvcher);

            String k = accountantGlvcher.getBranchid() + accountantGlvcher.getCurrCode() + accountantGlvcher.getGlAcct() + accountantGlvcher.getDrcr();

            TAmsGlvcherSumC sumDTO;
            if (cache.containsKey(k)) {
                sumDTO = cache.get(k);
            } else {
                //会计汇总传票复制
                sumDTO = copyTAmsGlvcherSum(accountantGlvcher);
                sumDTO.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                sumDTO.setDrcr(accountantGlvcher.getDrcr());
                sumDTO.setOccurDb(BigDecimal.ZERO);
                sumDTO.setOccurCr(BigDecimal.ZERO);
                sumDTO.setCntCr(BigDecimal.ZERO);
                sumDTO.setCntDr(BigDecimal.ZERO);
                sumDTO.setVersionNumber(1L);
                sumDTO.setProcessInd("0");
            }

            if ("D".equals(accountantGlvcher.getDrcr())) {
                sumDTO.setOccurDb(
                        AmountUtil.addAmount(sumDTO.getOccurDb(), accountantGlvcher.getGlAmount()));
                sumDTO.setCntDr(
                        AmountUtil.addAmount(sumDTO.getCntDr(), accountantGlvcher.getGlCnt())
                );
            }
            if ("C".equals(accountantGlvcher.getDrcr())) {
                sumDTO.setOccurCr(
                        AmountUtil.addAmount(sumDTO.getOccurCr(), accountantGlvcher.getGlAmount()));
                sumDTO.setCntCr(
                        AmountUtil.addAmount(sumDTO.getCntCr(), accountantGlvcher.getGlCnt())
                );
            }

            cache.put(k, sumDTO);

            int incrementAndGet = atomicInteger.incrementAndGet();
            log.info("{},tiao",incrementAndGet);
            if (incrementAndGet % 500 == 0) {
                //accountantGlvcherSelfMapper.updateBatch(updates);
                accountantJdbcService.batchUpdateById(updates);
                log.info("{}条传票处理标志更新完毕",incrementAndGet);
                updates.clear();
            }
            //accountantGlvcherSelfMapper.updateBatch(updates);
            accountantJdbcService.batchUpdateById(updates);
            log.info("{}条传票处理标志更新完毕",updates.size());
            updates.clear();

        });
        // 更新汇总
        Collection<TAmsGlvcherSumC> glvcherSums = cache.values();
        List<TAmsGlvcherSumC> tAmsGlvcherSumCS = Lists.newArrayList(glvcherSums);
        // help gc
        cache.clear();
        glvcherSums.clear();

        if (tAmsGlvcherSumCS.size() > 500) {
            List<List<TAmsGlvcherSumC>> partition = Lists.partition(tAmsGlvcherSumCS, 500);
            for (List<TAmsGlvcherSumC> glvcherSum : partition) {
                //accountantGlvcherSumSelfMapper.insertBatch(glvcherSum);
                accountantJdbcService.batchInset(glvcherSum);

                glvcherSum.clear();
            }
        } else {
            //accountantGlvcherSumSelfMapper.insertBatch(tAmsGlvcherSumCS);
            accountantJdbcService.batchInset(tAmsGlvcherSumCS);
        }
    }

    public TAmsGlvcherSumC copyTAmsGlvcherSum(AccountantGlvcher source) {
        TAmsGlvcherSumC tAmsGlvcherSum = new TAmsGlvcherSumC();
        tAmsGlvcherSum.setOrganizationNumber(source.getOrganizationNumber());
        tAmsGlvcherSum.setBranchid(source.getBranchid());
        tAmsGlvcherSum.setFundId(source.getFundId());
        tAmsGlvcherSum.setCurrCode(source.getCurrCode());
        tAmsGlvcherSum.setGlAcct(source.getGlAcct());
        tAmsGlvcherSum.setOccurDb(source.getGlAmount());
        tAmsGlvcherSum.setOccurCr(source.getGlAmount());
        tAmsGlvcherSum.setPostingDate(source.getPostingDate());
        tAmsGlvcherSum.setCreateTime(LocalDateTime.now());
        tAmsGlvcherSum.setUpdateTime(LocalDateTime.now());
        tAmsGlvcherSum.setUpdateBy(AccountantConstants.DEFAULT_USER);
        return tAmsGlvcherSum;
    }

}
