
# 工程名称
# 信用卡系统-账户服务工程

## 工程用途及介绍
## 账户服务工程
> 该工程主要由服务接口层anytxn-accounting-sdk、账户批量作业模块anytxn-accounting-batch、账户服务调用客户端模块anytxn-accounting-base、账户核心业务模块anytxn-accounting-sdk组成。
- 提供管理账户查询接口、统计账户查询接口、账单账户查询接口
- 批量工程实现了账单批量、滞纳金批量、约定扣款批量、延滞滚动批量处理
## 使用用户群
- 开发
- 运维

## 用户术语

- 【名称】：accounting--账户
- 【名称】：batch--批量


## 工程结构说明
> 工程目录结构，包结构说明
- anytxn-accounting-base(anytxn账户服务接口模块)
    + 包结构说明
    ```text
      |-java
         |-com.anytech.anytxn.account      
              |-dto        接口请求，响应实体
              |-service           服务接口
    ```
               
- anytxn-accounting-batch(anytxn账户核心批量作业模块)
    + 包结构说明
    ```text
      |-java
         |-com.anytech.anytxn.account  
              |-config        系统配置类包，系统相关的java配置类统一
              |-job           作业任务包
              |-service          参数请求包
              |-AnytxnAccountingBatchApplication.java 服务启动类
      |-resources   静态资源包
    ```
                
- anytxn-accounting-sdk(anytxn账户服务实现模块)
    + 包结构说明
    ```text
      |-java
         |-com.anytech.anytxn.account
              |-config       系统配置类包，系统相关的java配置类统一
              |-controller          controller层
              |-service.impl          服务实现层
              |-EnableAccountingApi.java          服务启动容器加载bean的配置类，包括controller层
              |-EnableAccountingService.java          服务启动容器加载bean的配置类
              
    ```
    
- anytxn-accounting-server(anytxn账户web模块)
    + 包接口说明
    ```
      |-java                         
          |-com.anytech.anytxn.account.server    
              |-config    bean配置类
              |-AnyTxnAccountingServerApplication     springboot启动类     
    ``` 
                         
## 服务发布
- 编译  
  ```text
  1. 在工程根目录下执行如下命令打包
   mvn clean install 
  2. 在anytxn-accounting-api模块的target目录下生产anytxn-accounting-api-2.0.0-SNAPSHOT.jar
   ```
  
- 部署  
  ```text
  1. 将anytxn-accounting-api-2.0.0-SNAPSHOT.jar放到服务器指定目录
  2. 执行nohup java -jar -Dspring.profiles.active=uat anytxn-accounting-api-2.0.0-SNAPSHOT.jar &
  ```

## 库表结构
> 数据库结构表

## 参考资料