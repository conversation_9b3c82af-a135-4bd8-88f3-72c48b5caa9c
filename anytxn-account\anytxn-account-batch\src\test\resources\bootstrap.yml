spring:
  # 可以放在远程配置文件中
  cloud:
    nacos:
      # 配置中心
      config:
        prefix: accountingBatch
        file-extension: yaml
        server-addr: 10.0.24.130
        namespace: d88d3a7f-28a8-4786-8648-da4fe8411a5e
        # 扩展配置(公用db)
        extension-configs[0]:
          data-id: commonDb.yaml
          refresh: true
        extension-configs[1]:
          data-id: bizDb-1.yaml
          refresh: true
        extension-configs[2]:
          data-id: cache.yaml
          refresh: true
        extension-configs[3]:
          data-id: commonDb-batch.yaml
        extension-configs[4]:
          data-id: custMapping.yaml
        extension-configs[5]:
          data-id: file.yaml
      discovery:
        server-addr: 10.0.24.130
        namespace: d88d3a7f-28a8-4786-8648-da4fe8411a5e
