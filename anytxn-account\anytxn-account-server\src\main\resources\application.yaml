server:
  port: 18084

logging:
  level:
    org:
      apache:
        shardingsphere: DEBUG
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: caffeine
      keyConvertor: fastjson
      limit: 100

anytxn:
  number:
    segmentEnable: true
    tenantIds: 6001,6002
    segmentMap:
      param6001: 6001
      param6002: 6002

  shardingsphere:
    enabled: true
    mode: nacos
    #    shardingFile: classpath:sharding-config.yaml
    properties:
      namespace: 4a3c117e-3efb-4acc-b05b-0c0bf8e6acd6
      group: DEFAULT_GROUP
      serverAddr: 172.16.70.20:8848
#      dataId: sharding-config-wjc-accouting.yaml
      dataId: sharding-param-biz.yaml
      username: nacos
      password: nacos

spring:
  application:
    name: anytxn-account-server
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许Bean定义覆盖，因为项目是三层架构，无法避免这个情况。
  config:
    import:
      - optional:nacos:${spring.application.name}.yaml # 加载【Nacos】的配置


---
spring:
  config:
    activate:
      on-profile: dev
  cloud:
    nacos:
      server-addr: 172.16.70.20:8848 # Nacos 服务器地址
      username: nacos # Nacos 账号
      password: nacos # Nacos 密码
      discovery: # 【配置中心】配置项
        namespace: 4a3c117e-3efb-4acc-b05b-0c0bf8e6acd6 # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
      config: # 【注册中心】配置项
        namespace: 4a3c117e-3efb-4acc-b05b-0c0bf8e6acd6 # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        file-extension: yaml # 配置文件格式

---
spring:
  config:
    activate:
      on-profile: sit
  cloud:
    nacos:
      server-addr: 172.16.70.20:8848 # Nacos 服务器地址
      username: nacos # Nacos 账号
      password: nacos # Nacos 密码
      discovery: # 【配置中心】配置项
        namespace: 4a3c117e-3efb-4acc-b05b-0c0bf8e6acd6 # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
      config: # 【注册中心】配置项
        namespace: 4a3c117e-3efb-4acc-b05b-0c0bf8e6acd6 # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        file-extension: yaml # 配置文件格式