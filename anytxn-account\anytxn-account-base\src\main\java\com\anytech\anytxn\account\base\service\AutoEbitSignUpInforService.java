package com.anytech.anytxn.account.base.service;


import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;

/**
 * <AUTHOR>
 * @date 2021-05-13 11:16
 **/
public interface AutoEbitSignUpInforService {

    /**
     * 新增约定扣款签约信息
     */
    int addAutoInfo(AutoEbitSignUpInforDTO autoEbitSignUpInfor);

    /**
     * 更新约定扣款签约信息
     */
    int updateAutoInfo(AutoEbitSignUpInforDTO autoEbitSignUpInfor);

    /**
    * 查询约定扣款签约信息
    **/
    void selectByCustid(AutoEbitSignUpInforDTO autoEbitSignUpInfor);


    AutoEbitSignUpInforDTO findStaById(String contractType, String customerId, String autoDebitType);


    /**
     * 新增临时扣款签约信息
     */
    int addAutoTempInfo(AutoEbitSignUpInforDTO autoEbitSignUpInfor);

    /**
     * 更新临时扣款签约信息
     */
    int updateAutoTempInfo(AutoEbitSignUpInforDTO autoEbitSignUpInfor);

}
