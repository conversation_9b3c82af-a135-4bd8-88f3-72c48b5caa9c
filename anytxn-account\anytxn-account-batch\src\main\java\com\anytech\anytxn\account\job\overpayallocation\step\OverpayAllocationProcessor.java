package com.anytech.anytxn.account.job.overpayallocation.step;

import com.anytech.anytxn.account.base.service.IOverpayAllocationService;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 账户间溢缴款分配
 * <AUTHOR>
 * @date 2020-10-20 14:54
 **/
public class OverpayAllocationProcessor implements ItemProcessor<CustReconciliationControlDTO, CustReconciliationControlDTO> {
    @Autowired
    private IOverpayAllocationService overpayAllocationService;

    /**
     * 账户间溢缴款分配
     */
    @Override
    public CustReconciliationControlDTO process(CustReconciliationControlDTO custReconciliationControlDTO) throws Exception {
        overpayAllocationService.overpayAllocationProcessNew(custReconciliationControlDTO);
        return custReconciliationControlDTO;
    }
}
