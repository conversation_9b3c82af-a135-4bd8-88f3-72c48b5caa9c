package com.anytech.anytxn.accounting.batch.job.report.step.amountdetail.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2020/9/14
 */
@Getter
@Setter
public class AmountDetail {

    /**
     * 分行号
     */
    private String branchId;

    /**
     * 客户号
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 账产品id
     */
    private String acctLogo;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 币种
     */
    private String currCode;

    /**
     * 入账日期
     */
    private LocalDate postDate;

    /**
     * 科目号
     */
    private String glAcct;

    /**
     * 科目名称
     */
    private String glAcctName;

    /**
     * 借贷方向
     */
    private String drcr;

    /**
     * 金额
     */
    private BigDecimal glAmount;
}
