package com.anytech.anytxn.accounting.batch.job.report.step.installdetail.dto;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.anytech.anytxn.accounting.batch.job.report.step.installdetail.model.InstallDetail;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/16
 */
@Getter
@Setter
public class InstallDetailReport {

    private String type;

    private String productId;

    private String description;

    private String currencyCode;

    private String transactionCode;

    private String postDate;

    private String subjectNumber;

    private String subjectName;

    private String debitCreditIndcator;

    private BigDecimal createAmount;


    public static List<InstallDetailReport> resetReportData(List<InstallDetail> data, TimeTypeEnum timeTypeEnum){

        return data.parallelStream().map(x-> {
            InstallDetailReport detail = new InstallDetailReport();
            detail.setType(x.getInstallType());
            detail.setProductId(x.getInstallProductId());
            detail.setDescription(x.getDescription());
            detail.setCurrencyCode(x.getCurrCode());
            detail.setTransactionCode(x.getTxnCodeOrig());
            detail.setPostDate(TimeUtils.dateToStr(x.getPostDate(), timeTypeEnum));
            detail.setSubjectNumber(x.getGlAcct());
            detail.setSubjectName(x.getGlAcctName());
            detail.setDebitCreditIndcator(x.getDrcr());
            detail.setCreateAmount(x.getGlAmount());

            return detail;
        }).sorted(Comparator.comparing(InstallDetailReport::getProductId)).collect(Collectors.toList());
    }
}
