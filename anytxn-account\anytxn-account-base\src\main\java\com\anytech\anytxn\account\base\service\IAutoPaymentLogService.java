package com.anytech.anytxn.account.base.service;

import com.anytech.anytxn.account.base.domain.bo.GiroAutoPayBO;
import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogSearchKeyDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 约定扣款service
 *
 * @Author: Wenwu Huang
 * @Date: 2019/1/21 16:03
 */
public interface IAutoPaymentLogService {
    /**
     * 微信公众号还款记录
     * @param accountManagementId 管理账户号
     * @param cardNumber  卡号
     * @param originalPaymentAmount  原始扣款金额
     */
    void insertAutoPaymentLog(String accountManagementId, String cardNumber, BigDecimal originalPaymentAmount,String trnDate);

    /**
     * 约定扣款处理
     * @param list 约定扣款list
     */
    void batchSave(List<AutoPaymentBO> list);

    /**
     * 自扣writer
     * @param list 约定扣款list
     */
    void newAutoPayBatchSave(List<GiroAutoPayBO> list);

    /**
     * 根据搜索条件分页查询约定扣款流水记录
     * @param searchKey 搜索条件
     * @return PageResultDTO<AutoPaymentLogDTO>
     */
    PageResultDTO<AutoPaymentLogDTO> findByCardNum(AutoPaymentLogSearchKeyDTO searchKey);

    /**
     * 根据主键查询约定扣款流水表明细
     * @param autoPaymentId 约定扣款id
     * @return AutoPaymentLogDTO
     */
    AutoPaymentLogDTO selectById(String autoPaymentId);
}
