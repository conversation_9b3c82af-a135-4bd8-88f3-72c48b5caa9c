package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TextLineObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TextLineObject(encoding = "UTF-8")
public class AccountantGlamsSumOutHeader implements Serializable {
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    @FileField(order = 1, length = 8)
    private String branchid="BRANCHID";

    @FileField(order = 2, length = 1)
    private String dot=",";

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    @FileField(order = 3, length = 21)
    private String accountManagementId="ACCOUNT_MANAGEMENT_ID";

    @FileField(order = 4, length = 1)
    private String dot2=",";

    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    @FileField(order = 5, length = 9)
    private String acctLogo="ACCT_LOGO";

    @FileField(order = 6, length = 14)
    private String dot3=",";

    /**
     * 全局业务流水号
     * 表字段:GLOBAL_FLOW_NO
     */
    @FileField(order = 7, length = 14)
    private String globalFlowNo="GLOBAL_FLOW_NO";

    @FileField(order = 8, length = 1)
    private String dot4=",";

    /**
     * 模块标识
     * 表字段:MODULE_FLAG
     */
    @FileField(order = 9, length = 11)
    private String moduleFlag="MODULE_FLAG";

    @FileField(order = 10, length = 1)
    private String dot5=",";

    /**
     * null
     * 表字段:TXN_CODE
     */
    @FileField(order = 11, length = 8)
    private String txnCode="TXN_CODE";

    @FileField(order = 12, length = 1)
    private String dot6=",";

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    @FileField(order = 13, length = 12)
    private String postingDate="POSTING_DATE";

    @FileField(order = 14, length = 1)
    private String dot7=",";

    /**
     * 入账币种
     * 表字段:POSTING_CURRENCY_CODE
     */
    @FileField(order = 15, length = 21)
    private String postingCurrencyCode="POSTING_CURRENCY_CODE";

    @FileField(order = 16, length = 1)
    private String dot8=",";

    /**
     * 入账金额
     * 表字段:POSTING_AMT
     */
    @FileField(order = 17, length = 11)
    private String postingAmt="POSTING_AMT";

    @FileField(order = 18, length = 1)
    private String dot9=",";

    /**
     * 入账笔数
     * 表字段:POSTING_CNT
     */
    @FileField(order = 19, length = 11)
    private String postingCnt="POSTING_CNT";

    @FileField(order = 20, length = 1)
    private String dot10=",";

    /**
     * 核算状态
     * 表字段:FINANCE_STATUS
     */
    @FileField(order = 21, length = 15)
    private String financeStatus="FINANCE_STATUS";

    @FileField(order = 22, length = 1)
    private String dot11=",";

    /**
     * 表内表外标识
     * 表字段:INTEREST_IND
     */
    @FileField(order = 23, length = 12)
    private String interestInd="INTEREST_IND";

    @FileField(order = 24, length = 1)
    private String dot12=",";

    /**
     * 价税分离标识
     * 表字段:PRICE_TAX_FLG
     */
    @FileField(order = 25, length = 13)
    private String priceTaxFlg="PRICE_TAX_FLG";

    @FileField(order = 26, length = 1)
    private String dot13=",";

    /**
     * 原始交易码
     * 表字段:TXN_CODE_ORIG
     */
    @FileField(order = 27, length = 13)
    private String txnCodeOrig="TXN_CODE_ORIG";

    @FileField(order = 28, length = 1)
    private String dot14=",";

    /**
     * 余额类型
     * 表字段:BAL_TYPE
     */
    @FileField(order = 29, length = 8)
    private String balType="BAL_TYPE";

    @FileField(order = 30, length = 1)
    private String dot15=",";

    /**
     * 余额结转标志
     * 表字段:BAL_PROCESS_IND
     */
    @FileField(order = 31, length = 15)
    private String balProcessInd="BAL_PROCESS_IND";

    @FileField(order = 32, length = 1)
    private String dot16=",";

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    @FileField(order = 33, length = 10)
    private String absStatus="ABS_STATUS";

    @FileField(order = 34, length = 1)
    private String dot17=",";

    /**
     * 原始交易ABS借贷记标志
     * 表字段:ORIG_TXN_ABS_IND
     */
    @FileField(order = 35, length = 16)
    private String origTxnAbsInd="ORIG_TXN_ABS_IND";

    @FileField(order = 36, length = 1)
    private String dot18=",";

    /**
     * 分期订单号
     * 表字段:ORDER_ID
     */
    @FileField(order = 37, length = 8)
    private String orderId="ORDER_ID";

    @FileField(order = 38, length = 1)
    private String dot19=",";

    /**
     * 处理标识
     * 表字段:PROCESS_IND
     */
    @FileField(order = 39, length = 11)
    private String processInd="PROCESS_IND";

    @FileField(order = 40, length = 1)
    private String dot20=",";

    /**
     * 渠道ID
     * 表字段:CHANNEL_ID
     */
    @FileField(order = 41, length = 10)
    private String channelId="CHANNEL_ID";

    @FileField(order = 42, length = 1)
    private String dot21=",";

    /**
     * 子渠道ID
     * 表字段:SUBCHANNEL_ID
     */
    @FileField(order = 43, length = 13)
    private String subchannelId="SUBCHANNEL_ID";

    @FileField(order = 44, length = 1)
    private String dot22=",";

    /**
     * 资金源ID
     * 表字段:FUND_ID
     */
    @FileField(order = 45, length = 7)
    private String fundId="FUND_ID";

    @FileField(order = 46, length = 1)
    private String dot23=",";

    /**
     * 平台ID
     * 表字段:PLATFORM_ID
     */
    @FileField(order = 47, length = 12)
    private String platformId="PLATFORM_ID";

    @FileField(order = 48, length = 1)
    private String dot24=",";

    /**
     * 商户ID
     * 表字段:MERCHANT_NUMBER
     */
    @FileField(order = 49, length = 15)
    private String merchantNumber="MERCHANT_NUMBER";

    @FileField(order = 50, length = 1)
    private String dot25=",";
    /**
     * 交易属性
     */
    @FileField(order = 51, length = 21)
    private String transactionAttribute="TRANSACTION_ATTRIBUTE";

    @FileField(order = 52, length = 1)
    private String dot26=",";
    /**
     * 借贷记属性
     */
    @FileField(order = 53, length = 22)
    private String debitCreditIndicator="DEBIT_CREDIT_INDICATOR";

    @FileField(order = 54, length = 1)
    private String dot27=",";

    /**
     * 递延标识
     */
    @FileField(order = 55, length = 13)
    private String amortizeInd="AMORTIZE_IND";

    @FileField(order = 56, length = 1)
    private String dot28=",";
    /**
     * 交易类型
     */
    @FileField(order = 57, length = 21)
    private String transactionTypeCode="TRANSACTION_TYPE_CODE";

    @FileField(order = 58, length = 1)
    private String dot29=",";

    /**
     * 规则ID
     */
    @FileField(order = 59, length = 7)
    private String ruleId="RULE_ID";

    @FileField(order = 60, length = 1)
    private String dot30=",";

    /**
     * 五级分类标识
     */
    @FileField(order = 61, length = 19)
    private String fiveTypeIndicator="FIVE_TYPE_INDICATOR";

    @FileField(order = 62, length = 1)
    private String dot31=",";

    /**
     * abs类型
     */
    @FileField(order = 63, length = 8)
    private String absType="ABS_TYPE";

    @FileField(order = 64, length = 1)
    private String dot32=",";

    /**
     * crdNumber
     */
    @FileField(order = 65, length = 10)
    private String crdNumber="CRD_NUMBER";

    @FileField(order = 66, length = 1)
    private String dot33=",";

    /**
     * crdProductNumber
     */
    @FileField(order = 67, length = 18)
    private String crdProductNumber="CRD_PRODUCT_NUMBER";

    @FileField(order = 68, length = 1)
    private String dot34=",";

    /**
     * crdType
     */
    @FileField(order = 69, length = 8)
    private String crdType="CRD_TYPE";

    @FileField(order = 70, length = 1)
    private String dot35=",";

    /**
     * txnAmt
     */
    @FileField(order = 71, length = 7)
    private String txnAmt="TXN_AMT";

    @FileField(order = 72, length = 1)
    private String dot36=",";

    /**
     * txnCurrency
     */
    @FileField(order = 73, length = 12)
    private String txnCurrency="TXN_CURRENCY";

    @FileField(order = 74, length = 1)
    private String dot37=",";

    /**
     * settleAmt
     */
    @FileField(order = 75, length = 10)
    private String settleAmt="SETTLE_AMT";

    @FileField(order = 76, length = 1)
    private String dot38=",";

    /**
     * settleCurrency
     */
    @FileField(order = 77, length = 15)
    private String settleCurrency="SETTLE_CURRENCY";

    @FileField(order = 78, length = 1)
    private String dot39=",";

    /**
     * txnDate
     */
    @FileField(order = 79, length = 8)
    private String txnDate="TXN_DATE";

    @FileField(order = 80, length = 1)
    private String dot40=",";

    /**
     * txnSource
     */
    @FileField(order = 81, length = 10)
    private String txnSource="TXN_SOURCE";

    @FileField(order = 82, length = 1)
    private String dot41=",";

    /**
     * txnDescription
     */
    @FileField(order = 83, length = 15)
    private String txnDescription="TXN_DESCRIPTION";

    @FileField(order = 84, length = 1)
    private String dot42=",";

    /**
     * currencyRate
     */
    @FileField(order = 85, length = 13)
    private String currencyRate="CURRENCY_RATE";

    @FileField(order = 86, length = 1)
    private String dot43=",";

    /**
     * rrn
     */
    @FileField(order = 87, length = 3)
    private String rrn="RRN";

    @FileField(order = 88, length = 1)
    private String dot44=",";

    /**
     * acqId
     */
    @FileField(order = 89, length = 6)
    private String acqId="ACQ_ID";

    @FileField(order = 90, length = 1)
    private String dot45=",";

    /**
     * mkUpFeeInd
     */
    @FileField(order = 91, length = 13)
    private String mkUpFeeInd="MK_UP_FEE_IND";

    @FileField(order = 92, length = 1, rightPad = true)
    private String dot46=",";

    /**
     * 产品类型
     */
    @FileField(order = 93, length = 12)
    private String productType="PRODUCT_TYPE";

    @FileField(order = 94, length = 1, rightPad = true)
    private String dot47=",";

    /**
     * 卡组织
     */
    @FileField(order = 95, length = 11)
    private String cardOrgNumber="CARD_SCHEME";

    @FileField(order = 96, length = 1)
    private String dot48=",";

    /**
     *
     * va号:VA_NUMBER
     */
    @FileField(order = 97, length = 9)
    private String vaNum="VA_NUMBER";

    @FileField(order = 98, length = 1)
    private String dot49=",";

    /**
     *
     * VATA_COUPLE_INDICATOR
     */
    @FileField(order = 99, length = 21)
    private String vataCoupleIndicator="VATA_COUPLE_INDICATOR";
}
