package com.anytech.anytxn.accounting.batch.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/15
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.report.install-free-day")
public class InstallFreeDayReportConfigurer extends BaseConfigurer{
    /**
     * 设置参数
     * @param param
     */
    @Override
    public void putParam(Map<String, Object> param){
        param.put("reportNumber", "R038");
        param.put("reportName", "每日分期手续费报表");
    }
}
