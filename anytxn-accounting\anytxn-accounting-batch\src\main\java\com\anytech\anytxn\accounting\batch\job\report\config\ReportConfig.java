package com.anytech.anytxn.accounting.batch.job.report.config;

import com.anytech.anytxn.accounting.batch.config.AmountDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.CardDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.CardSettleReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.CustomerDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.InstallBalanceReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.InstallDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.InstallFreeDayReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.InstallFreeReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.InstallMonthReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.OrgAmountDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.OutCustomerDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.config.TransactionDaySumReportConfigurer;
import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.amountdetail.AmountDetailReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.carddetail.CardDetailReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement.CardSettlementReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.custdetail.CustomerDetailReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.dayinstallfree.DayInstallFreeReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.installbalance.InstallBalanceReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.installdetail.InstallDetailReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.installmonth.InstallMonthReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.orgsum.OrgAmountDetailReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.outcustdetail.OutCustomerDetailReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.transactionsum.TransactionSumReportTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2020/9/10
 *
 * 交易报表
 */
@Configuration
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class ReportConfig {

    @Autowired
    private JobBuilderFactory jobBuilderFactory;
    @Autowired
    private StepBuilderFactory stepBuilderFactory;
    @Autowired
    private InstallMonthReportConfigurer installMonthReportConfigurer;
    @Autowired
    private InstallBalanceReportConfigurer installBalanceReportConfigurer;
    @Autowired
    private InstallFreeReportConfigurer installFreeReportConfigurer;
    @Autowired
    private InstallFreeDayReportConfigurer installFreeDayReportConfigurer;
    @Autowired
    private TransactionDaySumReportConfigurer transactionDaySumReportConfigurer;
    @Autowired
    private CardSettleReportConfigurer cardSettleReportConfigurer;
    @Autowired
    private AmountDetailReportConfigurer amountDetailReportConfigurer;
    @Autowired
    private InstallDetailReportConfigurer installDetailReportConfigurer;
    @Autowired
    private CardDetailReportConfigurer cardDetailReportConfigurer;
    @Autowired
    private CustomerDetailReportConfigurer customerDetailReportConfigurer;
    @Autowired
    private OutCustomerDetailReportConfigurer outCustomerDetailReportConfigurer;
    @Autowired
    private OrgAmountDetailReportConfigurer orgAmountDetailReportConfigurer;

    @Bean("reportJob")
    public Job reportJob(@Qualifier("installMonthStep") Step installMonthStep,
//                         @Qualifier("installFreeStep") Step installFreeStep,
                         @Qualifier("transactionSumStep") Step transactionSumStep,
                         @Qualifier("cardSettlementDayStep") Step cardSettlementDayStep,
                         @Qualifier("cardSettlementMonthStep") Step cardSettlementMonthStep,
                         @Qualifier("cardSettlementQuarterStep") Step cardSettlementQuarterStep,
                         @Qualifier("cardSettlementYearStep") Step cardSettlementYearStep,
                         @Qualifier("amountDetailDayStep") Step amountDetailDayStep,
                         @Qualifier("amountDetailMonthStep") Step amountDetailMonthStep,
                         @Qualifier("amountDetailQuarterStep") Step amountDetailQuarterStep,
                         @Qualifier("amountDetailYearStep") Step amountDetailYearStep,
                         @Qualifier("installDetailDayStep") Step installDetailDayStep,
                         @Qualifier("installDetailMonthStep") Step installDetailMonthStep,
                         @Qualifier("installDetailQuarterStep") Step installDetailQuarterStep,
                         @Qualifier("installDetailYearStep") Step installDetailYearStep,
                         @Qualifier("cardDetailDayStep") Step cardDetailDayStep,
                         @Qualifier("cardDetailMonthStep") Step cardDetailMonthStep,
                         @Qualifier("cardDetailQuarterStep") Step cardDetailQuarterStep,
                         @Qualifier("cardDetailYearStep") Step cardDetailYearStep,
                         @Qualifier("customerDetailDayStep") Step customerDetailDayStep,
                         @Qualifier("customerDetailMonthStep") Step customerDetailMonthStep,
                         @Qualifier("customerDetailQuarterStep") Step customerDetailQuarterStep,
                         @Qualifier("customerDetailYearStep") Step customerDetailYearStep,
                         @Qualifier("outCustomerDetailDayStep") Step outCustomerDetailDayStep,
                         @Qualifier("outCustomerDetailMonthStep") Step outCustomerDetailMonthStep,
                         @Qualifier("outCustomerDetailQuarterStep") Step outCustomerDetailQuarterStep,
                         @Qualifier("outCustomerDetailYearStep") Step outCustomerDetailYearStep,
                         @Qualifier("installFreeDayStep") Step installFreeDayStep,
                         @Qualifier("installBalanceStep") Step installBalanceStep,
                         @Qualifier("orgAmountDetailDayStep") Step orgAmountDetailDayStep,
                         @Qualifier("orgAmountDetailMonthStep") Step orgAmountDetailMonthStep,
                         @Qualifier("orgAmountDetailQuarterStep") Step orgAmountDetailQuarterStep,
                         @Qualifier("orgAmountDetailYearStep") Step orgAmountDetailYearStep
                         ) {

        return jobBuilderFactory.get("reportJob")
                .start(installMonthStep)
                .next(transactionSumStep)
                .next(cardSettlementDayStep)
                .next(cardSettlementMonthStep)
                .next(cardSettlementQuarterStep)
                .next(cardSettlementYearStep)
                .next(amountDetailDayStep)
                .next(amountDetailMonthStep)
                .next(amountDetailQuarterStep)
                .next(amountDetailYearStep)
                .next(installDetailDayStep)
                .next(installDetailMonthStep)
                .next(installDetailQuarterStep)
                .next(installDetailYearStep)
                .next(cardDetailDayStep)
                .next(cardDetailMonthStep)
                .next(cardDetailQuarterStep)
                .next(cardDetailYearStep)
                .next(customerDetailDayStep)
                .next(customerDetailMonthStep)
                .next(customerDetailQuarterStep)
                .next(customerDetailYearStep)
                .next(outCustomerDetailDayStep)
                .next(outCustomerDetailMonthStep)
                .next(outCustomerDetailQuarterStep)
                .next(outCustomerDetailYearStep)
//                .next(installFreeDayStep)
                .next(installBalanceStep)
                .next(orgAmountDetailDayStep)
                .next(orgAmountDetailMonthStep)
                .next(orgAmountDetailQuarterStep)
                .next(orgAmountDetailYearStep)
                .build();
    }

    /**
     * 每月分期付款出账
     */
    @Bean("installMonthReportTasklet")
    @StepScope
    public Tasklet installMonthReportTasklet(){
        return new InstallMonthReportTasklet(installMonthReportConfigurer);
    }

    @Bean("installMonthStep")
    public Step installMonthStep(){
        return stepBuilderFactory.get("installMonthStep")
                .tasklet(installMonthReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 每日分期余额
     */
    @Bean("installBalanceReportTasklet")
    @StepScope
    public Tasklet installBalanceReportTasklet(){
        return new InstallBalanceReportTasklet(installBalanceReportConfigurer);
    }

    @Bean("installBalanceStep")
    public Step installBalanceStep(){
        return stepBuilderFactory.get("installBalanceStep")
                .tasklet(installBalanceReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

//    /**
//     * 入账分期手续费
//     */
//    @Bean("installFreeReportTasklet")
//    @StepScope
//    public Tasklet installFreeReportTasklet(){
//        return new InstallFreeReportTasklet(installFreeReportConfigurer);
//    }
//
//    @Bean("installFreeStep")
//    public Step installFreeStep(){
//        return stepBuilderFactory.get("installFreeStep")
//                .tasklet(installFreeReportTasklet())
//                .allowStartIfComplete(true)
//                .build();
//    }

    /**
     * 每日分期手续费（已抛账/未抛账）
     */
    @Bean("installFreeDayReportTasklet")
    @StepScope
    public Tasklet installFreeDayReportTasklet(){
        return new DayInstallFreeReportTasklet(installFreeDayReportConfigurer);
    }

    @Bean("installFreeDayStep")
    public Step installFreeDayStep(){
        return stepBuilderFactory.get("installFreeDayStep")
                .tasklet(installFreeDayReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 每日入账汇总/会计（含时间差）
     */
    @Bean("transactionSumReportTasklet")
    @StepScope
    public Tasklet transactionSumReportTasklet(){
        return new TransactionSumReportTasklet(transactionDaySumReportConfigurer);
    }

    @Bean("transactionSumStep")
    public Step transactionSumStep(){
        return stepBuilderFactory.get("transactionSumStep")
                .tasklet(transactionSumReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 发卡清算 - 日
     */
    @Bean("cardSettlementDayReportTasklet")
    @StepScope
    public Tasklet cardSettlementDayReportTasklet(){
        return new CardSettlementReportTasklet(cardSettleReportConfigurer, TimeTypeEnum.D);
    }

    @Bean("cardSettlementDayStep")
    public Step cardSettlementDayStep(){
        return stepBuilderFactory.get("cardSettlementDayStep")
                .tasklet(cardSettlementDayReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 发卡清算 - 月
     */
    @Bean("cardSettlementMonthReportTasklet")
    @StepScope
    public Tasklet cardSettlementMonthReportTasklet(){
        return new CardSettlementReportTasklet(cardSettleReportConfigurer, TimeTypeEnum.M);
    }

    @Bean("cardSettlementMonthStep")
    public Step cardSettlementMonthStep(){
        return stepBuilderFactory.get("cardSettlementMonthStep")
                .tasklet(cardSettlementMonthReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 发卡清算 - 季度
     */
    @Bean("cardSettlementQuarterReportTasklet")
    @StepScope
    public Tasklet cardSettlementQuarterReportTasklet(){
        return new CardSettlementReportTasklet(cardSettleReportConfigurer, TimeTypeEnum.Q);
    }

    @Bean("cardSettlementQuarterStep")
    public Step cardSettlementQuarterStep(){
        return stepBuilderFactory.get("cardSettlementQuarterStep")
                .tasklet(cardSettlementQuarterReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 发卡清算 - 年
     */
    @Bean("cardSettlementYearReportTasklet")
    @StepScope
    public Tasklet cardSettlementYearReportTasklet(){
        return new CardSettlementReportTasklet(cardSettleReportConfigurer, TimeTypeEnum.Y);
    }

    @Bean("cardSettlementYearStep")
    public Step cardSettlementYearStep(){
        return stepBuilderFactory.get("cardSettlementYearStep")
                .tasklet(cardSettlementYearReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 应收款明细 - 日
     */
    @Bean("amountDetailDayReportTasklet")
    @StepScope
    public Tasklet amountDetailDayReportTasklet(){
        return new AmountDetailReportTasklet(amountDetailReportConfigurer, TimeTypeEnum.D);
    }

    @Bean("amountDetailDayStep")
    public Step amountDetailDayStep(){
        return stepBuilderFactory.get("amountDetailDayStep")
                .tasklet(amountDetailDayReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 应收款明细 - 月
     */
    @Bean("amountDetailMonthReportTasklet")
    @StepScope
    public Tasklet amountDetailMonthReportTasklet(){
        return new AmountDetailReportTasklet(amountDetailReportConfigurer, TimeTypeEnum.M);
    }

    @Bean("amountDetailMonthStep")
    public Step amountDetailMonthStep(){
        return stepBuilderFactory.get("amountDetailMonthStep")
                .tasklet(amountDetailMonthReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 应收款明细 - 季度
     */
    @Bean("amountDetailQuarterReportTasklet")
    @StepScope
    public Tasklet amountDetailQuarterReportTasklet(){
        return new AmountDetailReportTasklet(amountDetailReportConfigurer, TimeTypeEnum.Q);
    }

    @Bean("amountDetailQuarterStep")
    public Step amountDetailQuarterStep(){
        return stepBuilderFactory.get("amountDetailQuarterStep")
                .tasklet(amountDetailQuarterReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 应收款明细 - 年
     */
    @Bean("amountDetailYearReportTasklet")
    @StepScope
    public Tasklet amountDetailYearReportTasklet(){
        return new AmountDetailReportTasklet(amountDetailReportConfigurer, TimeTypeEnum.Y);
    }

    @Bean("amountDetailYearStep")
    public Step amountDetailYearStep(){
        return stepBuilderFactory.get("amountDetailYearStep")
                .tasklet(amountDetailYearReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 分期收入明细 - 日
     */
    @Bean("installDetailDayReportTasklet")
    @StepScope
    public Tasklet installDetailDayReportTasklet(){
        return new InstallDetailReportTasklet(installDetailReportConfigurer, TimeTypeEnum.D);
    }

    @Bean("installDetailDayStep")
    public Step installDetailDayStep(){
        return stepBuilderFactory.get("installDetailDayStep")
                .tasklet(installDetailDayReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 分期收入明细 - 月
     */
    @Bean("installDetailMonthReportTasklet")
    @StepScope
    public Tasklet installDetailMonthReportTasklet(){
        return new InstallDetailReportTasklet(installDetailReportConfigurer, TimeTypeEnum.M);
    }

    @Bean("installDetailMonthStep")
    public Step installDetailMonthStep(){
        return stepBuilderFactory.get("installDetailMonthStep")
                .tasklet(installDetailMonthReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 分期收入明细 - 季度
     */
    @Bean("installDetailQuarterReportTasklet")
    @StepScope
    public Tasklet installDetailQuarterReportTasklet(){
        return new InstallDetailReportTasklet(installDetailReportConfigurer, TimeTypeEnum.Q);
    }

    @Bean("installDetailQuarterStep")
    public Step installDetailQuarterStep(){
        return stepBuilderFactory.get("installDetailQuarterStep")
                .tasklet(installDetailQuarterReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 分期收入明细 - 年
     */
    @Bean("installDetailYearReportTasklet")
    @StepScope
    public Tasklet installDetailYearReportTasklet(){
        return new InstallDetailReportTasklet(installDetailReportConfigurer, TimeTypeEnum.Y);
    }

    @Bean("installDetailYearStep")
    public Step installDetailYearStep(){
        return stepBuilderFactory.get("installDetailYearStep")
                .tasklet(installDetailYearReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 分期收入明细 - 日
     */
    @Bean("cardDetailDayReportTasklet")
    @StepScope
    public Tasklet cardDetailDayReportTasklet(){
        return new CardDetailReportTasklet(cardDetailReportConfigurer, TimeTypeEnum.D);
    }

    @Bean("cardDetailDayStep")
    public Step cardDetailDayStep(){
        return stepBuilderFactory.get("cardDetailDayStep")
                .tasklet(cardDetailDayReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 发卡收入明细 - 月
     */
    @Bean("cardDetailMonthReportTasklet")
    @StepScope
    public Tasklet cardDetailMonthReportTasklet(){
        return new CardDetailReportTasklet(cardDetailReportConfigurer, TimeTypeEnum.M);
    }

    @Bean("cardDetailMonthStep")
    public Step cardDetailMonthStep(){
        return stepBuilderFactory.get("cardDetailMonthStep")
                .tasklet(cardDetailMonthReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 发卡收入明细 - 季度
     */
    @Bean("cardDetailQuarterReportTasklet")
    @StepScope
    public Tasklet cardDetailQuarterReportTasklet(){
        return new CardDetailReportTasklet(cardDetailReportConfigurer, TimeTypeEnum.Q);
    }

    @Bean("cardDetailQuarterStep")
    public Step cardDetailQuarterStep(){
        return stepBuilderFactory.get("cardDetailQuarterStep")
                .tasklet(cardDetailQuarterReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 发卡收入明细 - 年
     */
    @Bean("cardDetailYearReportTasklet")
    @StepScope
    public Tasklet cardDetailYearReportTasklet(){
        return new CardDetailReportTasklet(cardDetailReportConfigurer, TimeTypeEnum.Y);
    }

    @Bean("cardDetailYearStep")
    public Step cardDetailYearStep(){
        return stepBuilderFactory.get("cardDetailYearStep")
                .tasklet(cardDetailYearReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 客户落账 - 日
     */
    @Bean("customerDetailDayReportTasklet")
    @StepScope
    public Tasklet customerDetailDayReportTasklet(){
        return new CustomerDetailReportTasklet(customerDetailReportConfigurer, TimeTypeEnum.D);
    }

    @Bean("customerDetailDayStep")
    public Step customerDetailDayStep(){
        return stepBuilderFactory.get("customerDetailDayStep")
                .tasklet(customerDetailDayReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 客户落账 - 月
     */
    @Bean("customerDetailMonthReportTasklet")
    @StepScope
    public Tasklet customerDetailMonthReportTasklet(){
        return new CustomerDetailReportTasklet(customerDetailReportConfigurer, TimeTypeEnum.M);
    }

    @Bean("customerDetailMonthStep")
    public Step customerDetailMonthStep(){
        return stepBuilderFactory.get("customerDetailMonthStep")
                .tasklet(customerDetailMonthReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 客户落账 - 季度
     */
    @Bean("customerDetailQuarterReportTasklet")
    @StepScope
    public Tasklet customerDetailQuarterReportTasklet(){
        return new CustomerDetailReportTasklet(customerDetailReportConfigurer, TimeTypeEnum.Q);
    }

    @Bean("customerDetailQuarterStep")
    public Step customerDetailQuarterStep(){
        return stepBuilderFactory.get("customerDetailQuarterStep")
                .tasklet(customerDetailQuarterReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 客户落账 - 年
     */
    @Bean("customerDetailYearReportTasklet")
    @StepScope
    public Tasklet customerDetailYearReportTasklet(){
        return new CustomerDetailReportTasklet(customerDetailReportConfigurer, TimeTypeEnum.Y);
    }

    @Bean("customerDetailYearStep")
    public Step customerDetailYearStep(){
        return stepBuilderFactory.get("customerDetailYearStep")
                .tasklet(customerDetailYearReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 境外交易明细 - 日
     */
    @Bean("outCustomerDetailDayReportTasklet")
    @StepScope
    public Tasklet outCustomerDetailDayReportTasklet(){
        return new OutCustomerDetailReportTasklet(outCustomerDetailReportConfigurer, TimeTypeEnum.D);
    }

    @Bean("outCustomerDetailDayStep")
    public Step outCustomerDetailDayStep(){
        return stepBuilderFactory.get("outCustomerDetailDayStep")
                .tasklet(outCustomerDetailDayReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 境外交易明细 - 月
     */
    @Bean("outCustomerDetailMonthReportTasklet")
    @StepScope
    public Tasklet outCustomerDetailMonthReportTasklet(){
        return new OutCustomerDetailReportTasklet(outCustomerDetailReportConfigurer, TimeTypeEnum.M);
    }

    @Bean("outCustomerDetailMonthStep")
    public Step outCustomerDetailMonthStep(){
        return stepBuilderFactory.get("outCustomerDetailMonthStep")
                .tasklet(outCustomerDetailMonthReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 境外交易明细 - 季度
     */
    @Bean("outCustomerDetailQuarterReportTasklet")
    @StepScope
    public Tasklet outCustomerDetailQuarterReportTasklet(){
        return new OutCustomerDetailReportTasklet(outCustomerDetailReportConfigurer, TimeTypeEnum.Q);
    }

    @Bean("outCustomerDetailQuarterStep")
    public Step outCustomerDetailQuarterStep(){
        return stepBuilderFactory.get("outCustomerDetailQuarterStep")
                .tasklet(outCustomerDetailQuarterReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 境外交易明细 - 年
     */
    @Bean("outCustomerDetailYearReportTasklet")
    @StepScope
    public Tasklet outCustomerDetailYearReportTasklet(){
        return new OutCustomerDetailReportTasklet(outCustomerDetailReportConfigurer, TimeTypeEnum.Y);
    }

    @Bean("outCustomerDetailYearStep")
    public Step outCustomerDetailYearStep(){
        return stepBuilderFactory.get("outCustomerDetailYearStep")
                .tasklet(outCustomerDetailYearReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 分机构落账 - 日
     */
    @Bean("orgAmountDetailDayReportTasklet")
    @StepScope
    public Tasklet orgAmountDetailDayReportTasklet(){
        return new OrgAmountDetailReportTasklet(orgAmountDetailReportConfigurer, TimeTypeEnum.D);
    }

    @Bean("orgAmountDetailDayStep")
    public Step orgAmountDetailDayStep(){
        return stepBuilderFactory.get("orgAmountDetailDayStep")
                .tasklet(orgAmountDetailDayReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 分机构落账 - 月
     */
    @Bean("orgAmountDetailMonthReportTasklet")
    @StepScope
    public Tasklet orgAmountDetailMonthReportTasklet(){
        return new OrgAmountDetailReportTasklet(orgAmountDetailReportConfigurer, TimeTypeEnum.M);
    }

    @Bean("orgAmountDetailMonthStep")
    public Step orgAmountDetailMonthStep(){
        return stepBuilderFactory.get("orgAmountDetailMonthStep")
                .tasklet(orgAmountDetailMonthReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 分机构落账 - 季度
     */
    @Bean("orgAmountDetailQuarterReportTasklet")
    @StepScope
    public Tasklet orgAmountDetailQuarterReportTasklet(){
        return new OrgAmountDetailReportTasklet(orgAmountDetailReportConfigurer, TimeTypeEnum.Q);
    }

    @Bean("orgAmountDetailQuarterStep")
    public Step orgAmountDetailQuarterStep(){
        return stepBuilderFactory.get("orgAmountDetailQuarterStep")
                .tasklet(orgAmountDetailQuarterReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }

    /**
     * 分机构落账 - 年
     */
    @Bean("orgAmountDetailYearReportTasklet")
    @StepScope
    public Tasklet orgAmountDetailYearReportTasklet(){
        return new OrgAmountDetailReportTasklet(orgAmountDetailReportConfigurer, TimeTypeEnum.Y);
    }

    @Bean("orgAmountDetailYearStep")
    public Step orgAmountDetailYearStep(){
        return stepBuilderFactory.get("orgAmountDetailYearStep")
                .tasklet(orgAmountDetailYearReportTasklet())
                .allowStartIfComplete(true)
                .build();
    }
}
