package com.anytech.anytxn.account.base.utils;

import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.function.Consumer;

/**
 * @description: 批量插入和更新用
 * @author: zhangnan
 * @create: 2020-05-06
 **/
public class AccountBatchInsertUpdateHelper {

    public static <T> void batchUpdate(List<T> oriUpdateList, int maxUpdateSize, Consumer<List> mapper) {
        if (CollectionUtils.isNotEmpty(oriUpdateList)) {
            List<List<T>> updateSplitLists = ListUtils.fixedGrouping(oriUpdateList, maxUpdateSize);
            if (updateSplitLists != null) {
                for (List<T> oneList : updateSplitLists) {
                    mapper.accept(oneList);
                }
            }
        }
    }

    public static <T> void batchInsert(List<T> oriUpdateList, int maxUpdateSize, Consumer<List> mapper) {
        batchUpdate(oriUpdateList, maxUpdateSize, mapper);
    }
}
