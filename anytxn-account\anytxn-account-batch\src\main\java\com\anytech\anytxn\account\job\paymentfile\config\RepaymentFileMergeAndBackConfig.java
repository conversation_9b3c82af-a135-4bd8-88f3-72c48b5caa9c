package com.anytech.anytxn.account.job.paymentfile.config;

import com.anytech.anytxn.account.config.RePaymentFileConfigurer;
import com.anytech.anytxn.account.job.paymentfile.step.FileBackTasklet;
import com.anytech.anytxn.account.job.paymentfile.step.repay.RepaymentFileSplitTasklet;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.file.utils.okfile.UploadOkFileTasklet;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.transaction.base.constants.FileConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;
import java.io.File;
import java.time.format.DateTimeFormatter;

/**
 * 还款文件拆分与备份处理
 * <AUTHOR>
 * @date 2021/3/24
 */
@Configuration
@Slf4j
public class RepaymentFileMergeAndBackConfig {
    public static final String OK_SUFFIX = FileConstants.OK_SUFFIX;
    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;

    @Autowired
    private RePaymentFileConfigurer rePaymentFileConfigurer;

    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Resource(name = "paymentFileInputPathConfig")
    private AnytxnFilePathConfig pathConfigInput;

    @Resource(name = "paymentFileOutPutPathConfig")
    private AnytxnFilePathConfig pathConfigOutPut;

    @Value("${data-id:-1}")
    private String dataId;

    /**
     * 还款文件备份拆分
     */
    @Bean
    public Job rePaymentFileCommonJob(@Qualifier("uploadOkFileStep") Step uploadOkFileStep,
                                      @Qualifier("reFileBackStep") Step step,
                                     @Qualifier("repaymentFileSplitStep") Step step2) {
        return jobs.get("rePaymentFileCommonJob")
                .start(uploadOkFileStep)
                .next(step)
                .next(step2)
                .build();
    }

    @Bean
    public Step repaymentFileSplitStep(
            @Qualifier("repaymentFileSplitTasklet") Tasklet repaymentFileSplitTasklet) {
        return steps.get("repaymentFileSplitStep").tasklet(repaymentFileSplitTasklet).build();
    }

    @Bean
    public RepaymentFileSplitTasklet repaymentFileSplitTasklet(){
        String shardFilePathPre = pathConfigOutPut.getShardingPath();
        log.info("outFilePath分片处理的时候:{}",shardFilePathPre);
        log.info("目标文件:{}",joinRePaymentFilePath()+ File.separator);
        return new RepaymentFileSplitTasklet(shardFilePathPre, joinRePaymentFilePath(), joinRePaymentFileName(),rePaymentFileConfigurer.getErrorSli());
    }

    @Bean
    public Step reFileBackStep(
            @Qualifier("reFileBackTasklet") Tasklet reFileBackTasklet) {
        return steps.get("reFileBackStep").tasklet(reFileBackTasklet).build();
    }

    @Bean
    public FileBackTasklet reFileBackTasklet(){
        return new FileBackTasklet(joinRePaymentFilePath(), joinRePaymentFileBackPath());
    }

    @Bean
    @StepScope
    public UploadOkFileTasklet uploadOkFileTasklet() {
        return new UploadOkFileTasklet(joinOkFilePath(), joinOkFileBackPath());
    }

    @Bean
    public Step uploadOkFileStep(
            @Qualifier("uploadOkFileTasklet") Tasklet uploadOkFileTasklet) {
        return steps.get("uploadOkFileStep").tasklet(uploadOkFileTasklet).build();
    }

    private String joinRePaymentFileName(){
        String orgNum = OrgNumberUtils.getOrg();
        ParmOrganizationInfo org = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(orgNum);
        // 文件名称由【机构号-文件名-日期】组成
        String today = org.getToday().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return org.getOrganizationNumber() + "-" + rePaymentFileConfigurer.getFileName() + "-" + today;
    }

    private String joinRePaymentFilePath(){
        // 拼接读取文件目录：前缀/分片号/机构号/文件名
        return pathConfigInput.getCommonPath() + FileConstants.getPathForSystem() + joinRePaymentFileName();
    }

    private String joinOkFilePath(){
        //.ok上传文件路径读取
        return pathConfigInput.getCommonPath() + FileConstants.getPathForSystem() + joinRePaymentFileName() + OK_SUFFIX;
    }

    private String joinOkFileBackPath(){
        //.ok上传文件路径读取
        return pathConfigInput.getCommonBackPath() + FileConstants.getPathForSystem() + joinRePaymentFileName() + FileConstants.OK_SUFFIX;
    }

    private String joinRePaymentFileBackPath(){
        // 拼接读取文件目录：前缀/分片号/机构号/文件名
        return pathConfigInput.getCommonBackPath() + FileConstants.getPathForSystem() + joinRePaymentFileName();
    }

    private String getDusId(){
        if (dataId.contains(".")) {
            dataId = dataId.split("\\.")[0];
        }
        if (dataId.contains("-")){
            dataId = dataId.split("-")[1];
        }
        return dataId;
    }
}
