package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.anytxn.common.core.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会计月终余额备份表 
 * 表:ACCOUNTANT_GLACGM_B
 * <AUTHOR> 
 * @date 2019-10-12 
 */
public class TAmsGlacgmB extends BaseEntity {
    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 日期
     * 表字段:DATE_M
     */
    private String dateM;

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    private String glAcct;

    /**
     * 币种
     * 表字段:CURR
     */
    private String curr;

    /**
     * 科目分类
     * 表字段:GL_CLASS
     */
    private String glClass;

    /**
     * 科目名称
     * 表字段:GL_NAME
     */
    private String glName;

    /**
     * 上日借方余额
     * 表字段:PREV_BAL_DR
     */
    private BigDecimal prevBalDr;

    /**
     * 上日贷方余额
     * 表字段:PREV_BAL_CR
     */
    private BigDecimal prevBalCr;

    /**
     * 当日借方发生额
     * 表字段:OCCUR_DR
     */
    private BigDecimal occurDr;

    /**
     * 当日贷方发生额
     * 表字段:OCCUR_CR
     */
    private BigDecimal occurCr;

    /**
     * 当日借方余额
     * 表字段:CURR_BAL_DR
     */
    private BigDecimal currBalDr;

    /**
     * 当日贷方余额
     * 表字段:CURR_BAL_CR
     */
    private BigDecimal currBalCr;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

    /**
     * 获取技术主键
     * @return id Long
     */
    public String getId() {
        return id;
    }

    /**
     * 设置技术主键
     * @param id 技术主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取分行号
     * @return branchid String
     */
    public String getBranchid() {
        return branchid;
    }

    /**
     * 设置分行号
     * @param branchid 分行号
     */
    public void setBranchid(String branchid) {
        this.branchid = branchid == null ? null : branchid.trim();
    }

    /**
     * 获取日期
     * @return dateM String
     */
    public String getDateM() {
        return dateM;
    }

    /**
     * 设置日期
     * @param dateM 日期
     */
    public void setDateM(String dateM) {
        this.dateM = dateM == null ? null : dateM.trim();
    }

    /**
     * 获取科目号
     * @return glAcct String
     */
    public String getGlAcct() {
        return glAcct;
    }

    /**
     * 设置科目号
     * @param glAcct 科目号
     */
    public void setGlAcct(String glAcct) {
        this.glAcct = glAcct == null ? null : glAcct.trim();
    }

    /**
     * 获取币种
     * @return curr String
     */
    public String getCurr() {
        return curr;
    }

    /**
     * 设置币种
     * @param curr 币种
     */
    public void setCurr(String curr) {
        this.curr = curr == null ? null : curr.trim();
    }

    /**
     * 获取科目分类
     * @return glClass String
     */
    public String getGlClass() {
        return glClass;
    }

    /**
     * 设置科目分类
     * @param glClass 科目分类
     */
    public void setGlClass(String glClass) {
        this.glClass = glClass == null ? null : glClass.trim();
    }

    /**
     * 获取科目名称
     * @return glName String
     */
    public String getGlName() {
        return glName;
    }

    /**
     * 设置科目名称
     * @param glName 科目名称
     */
    public void setGlName(String glName) {
        this.glName = glName == null ? null : glName.trim();
    }

    /**
     * 获取上日借方余额
     * @return prevBalDr BigDecimal
     */
    public BigDecimal getPrevBalDr() {
        return prevBalDr;
    }

    /**
     * 设置上日借方余额
     * @param prevBalDr 上日借方余额
     */
    public void setPrevBalDr(BigDecimal prevBalDr) {
        this.prevBalDr = prevBalDr;
    }

    /**
     * 获取上日贷方余额
     * @return prevBalCr BigDecimal
     */
    public BigDecimal getPrevBalCr() {
        return prevBalCr;
    }

    /**
     * 设置上日贷方余额
     * @param prevBalCr 上日贷方余额
     */
    public void setPrevBalCr(BigDecimal prevBalCr) {
        this.prevBalCr = prevBalCr;
    }

    /**
     * 获取当日借方发生额
     * @return occurDr BigDecimal
     */
    public BigDecimal getOccurDr() {
        return occurDr;
    }

    /**
     * 设置当日借方发生额
     * @param occurDr 当日借方发生额
     */
    public void setOccurDr(BigDecimal occurDr) {
        this.occurDr = occurDr;
    }

    /**
     * 获取当日贷方发生额
     * @return occurCr BigDecimal
     */
    public BigDecimal getOccurCr() {
        return occurCr;
    }

    /**
     * 设置当日贷方发生额
     * @param occurCr 当日贷方发生额
     */
    public void setOccurCr(BigDecimal occurCr) {
        this.occurCr = occurCr;
    }

    /**
     * 获取当日借方余额
     * @return currBalDr BigDecimal
     */
    public BigDecimal getCurrBalDr() {
        return currBalDr;
    }

    /**
     * 设置当日借方余额
     * @param currBalDr 当日借方余额
     */
    public void setCurrBalDr(BigDecimal currBalDr) {
        this.currBalDr = currBalDr;
    }

    /**
     * 获取当日贷方余额
     * @return currBalCr BigDecimal
     */
    public BigDecimal getCurrBalCr() {
        return currBalCr;
    }

    /**
     * 设置当日贷方余额
     * @param currBalCr 当日贷方余额
     */
    public void setCurrBalCr(BigDecimal currBalCr) {
        this.currBalCr = currBalCr;
    }

    /**
     * 获取创建日期
     * @return createTime LocalDateTime
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建日期
     * @param createTime 创建日期
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新日期
     * @return updateTime LocalDateTime
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新日期
     * @param updateTime 更新日期
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新用户
     * @return updateBy String
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新用户
     * @param updateBy 更新用户
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    /**
     * 获取版本号
     * @return versionNumber Long
     */
    public Long getVersionNumber() {
        return versionNumber;
    }

    /**
     * 设置版本号
     * @param versionNumber 版本号
     */
    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }
}