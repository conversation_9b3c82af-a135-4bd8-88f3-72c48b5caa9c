package com.anytech.anytxn.account.job.overpayallocation.step;

import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;

/**
 * 账户间溢缴款分配
 * <AUTHOR>
 * @date 2020-10-20 14:55
 **/
public class OverpayAllocationWriter implements ItemWriter<CustReconciliationControlDTO> {

    @Override
    public void write(Chunk<? extends CustReconciliationControlDTO> chunk) throws Exception {
        //无实际用处,在process中做的入账
    }
}
