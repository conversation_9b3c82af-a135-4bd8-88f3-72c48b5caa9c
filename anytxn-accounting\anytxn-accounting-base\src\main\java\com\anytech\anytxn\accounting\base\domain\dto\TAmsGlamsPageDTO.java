package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.accounting.base.enums.AbsStatusEnum;
import com.anytech.anytxn.accounting.base.enums.BalTypeEnum;
import com.anytech.anytxn.accounting.base.enums.FinanceStatusEnum;
import com.anytech.anytxn.accounting.base.enums.InterestIndEnum;
import com.anytech.anytxn.accounting.base.enums.ModuleFlagEnum;
import com.anytech.anytxn.accounting.base.enums.OrigTxnAbsIndEnum;
import com.anytech.anytxn.accounting.base.enums.PriceTaxFlgEnum;
import com.anytech.anytxn.accounting.base.enums.TransactionAttributeEnum;
import com.anytech.anytxn.accounting.base.enums.TxnIndEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @author: sumingyue
 * @create: 2019/12/04 16:30
 */
@Getter
@Setter
public class TAmsGlamsPageDTO extends BaseEntity {

    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    private String accountManagementId;

    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    private String acctLogo;

    /**
     * 产品代码描述
     * 表字段:ACCT_LOGO
     */
    private String acctLogoDesc;

    /**
     * 全局业务流水号
     * 表字段:GLOBAL_FLOW_NO
     */
    private String globalFlowNo;
    /**
     * 表内表外标识
     * 表字段:INTEREST_IND
     */
    @Schema(description =  "表内表外标识")
    private String interestIndDesc;

    public String getInterestIndDesc() {
        return InterestIndEnum.getValue(interestInd);
    }

    /**
     * 价税分离标识
     * 表字段:PRICE_TAX_FLG
     */
    @Schema(description =  "价税分离标识")
    private String priceTaxFlgDesc;

    public String getPriceTaxFlgDesc() {
        return PriceTaxFlgEnum.getValue(priceTaxFlg);
    }

    /**
     * 余额类型
     * 表字段:BAL_TYPE
     */
    @Schema(description =  "余额类型")
    private String balTypeDesc;

    public String getBalTypeDesc() {
        return BalTypeEnum.getValue(balType);
    }

    /**
     * 模块标识描述
     * 表字段:MODULE_FLAG
     */
    @Schema(description =  "模块标识描述")
    private String moduleFlagDesc;

    public String getModuleFlagDesc() {
        return ModuleFlagEnum.getValue(moduleFlag);
    }

    /**
     * 入账金额
     * 表字段:FINANCE_STATUS
     */
    @Schema(description =  "入账金额")
    private String financeStatusDesc;

    public String getFinanceStatusDesc() {
        return FinanceStatusEnum.getValue(financeStatus);
    }

    /**
     * 交易标志
     * 表字段:TXN_IND
     */
    @Schema(description =  "交易标志")
    private String txnIndDesc;

    public String getTxnIndDesc() {
        return TxnIndEnum.getValue(txnInd);
    }

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    @Schema(description =  "资产出表状态")
    private String absStatusDesc;

    public String getAbsStatusDesc() {
        return AbsStatusEnum.getValue(absStatus);
    }

    /**
     * 原始交易ABS借贷记标志
     * 表字段:ORIG_TXN_ABS_IND
     */
    @Schema(description =  "原始交易ABS借贷记标志")
    private String origTxnAbsIndDesc;

    public String getOrigTxnAbsIndDesc() {
        return OrigTxnAbsIndEnum.getValue(origTxnAbsInd);
    }

    /**
     * 模块标识
     * 表字段:MODULE_FLAG
     */
    private String moduleFlag;

    /**
     * 交易码
     * 表字段:TXN_CODE
     */
    private String txnCode;

    private String txnCodeDesc;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate postingDate;

    /**
     * 入账币种
     * 表字段:POSTING_CURRENCY_CODE
     */
    private String postingCurrencyCode;

    /**
     * 入账金额
     * 表字段:POSTING_AMT
     */
    private BigDecimal postingAmt;

    /**
     * 入账金额
     * 表字段:FINANCE_STATUS
     */
    private String financeStatus;

    /**
     * 表内表外标识
     * 表字段:INTEREST_IND
     */
    private String interestInd;

    /**
     * 价税分离标识
     * 表字段:PRICE_TAX_FLG
     */
    private String priceTaxFlg;

    /**
     * 原始交易码
     * 表字段:TXN_CODE_ORIG
     */
    private String txnCodeOrig;

    private String txnCodeOrigDesc;

    /**
     * 余额类型
     * 表字段:BAL_TYPE
     */
    private String balType;

    /**
     * 交易标志
     * 表字段:TXN_IND
     */
    private String txnInd;

    /**
     * 余额结转标志
     * 表字段:BAL_PROCESS_IND
     */
    private String balProcessInd;

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    private String absStatus;

    /**
     * 资产包编号
     * 表字段:ASSET_NO
     */
    private String assetNo;

    /**
     * 原始交易ABS借贷记标志
     * 表字段:ORIG_TXN_ABS_IND
     */
    private String origTxnAbsInd;

    /**
     * 分期订单号
     * 表字段:ORDER_ID
     */
    private String orderId;

    /**
     * 处理标识
     * 表字段:PROCESS_IND
     */
    private String processInd;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String ruleIdDesc;

    public String getRuleIdDesc() {
        return ruleIdDesc;
    }

    public void setRuleIdDesc(String ruleIdDesc) {
        this.ruleIdDesc = ruleIdDesc;
    }

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

    /**
     * 分区键值
     * 表字段:PARTITION_KEY
     */
    private String partitionKey;
    /**
     * 渠道ID
     * 表字段:CHANNEL_ID
     */
    private String channelId;

    /**
     * 子渠道ID
     * 表字段:SUBCHANNEL_ID
     */
    private String subchannelId;

    /**
     * 资金源ID
     * 表字段:FUND_ID
     */
    private String fundId;

    /**
     * 平台ID
     * 表字段:PLATFORM_ID
     */
    private String platformId;

    /**
     * 商户ID
     * 表字段:MERCHANT_NUMBER
     */
    private String merchantNumber;

    /**
     * 交易属性
     */
    private String transactionAttribute;
    private String transactionAttributeDesc;
    /**
     * 借贷记属性
     */
    private String debitCreditIndicator;
    private String debitCreditIndicatorDesc;
    /**
     * 递延标识
     */
    private String amortizeInd;
    private String amortizeIndDesc;
    /**
     * 交易类型
     */
    private String transactionTypeCode;
    private String transactionTypeCodeDesc;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 五级分类标识
     */
    private String fiveTypeIndicator;

    /**
     * abs类型
     */
    private String absType;

    public String getTransactionAttributeDesc() {
        return  TransactionAttributeEnum.getValue(transactionAttribute);
    }
}
