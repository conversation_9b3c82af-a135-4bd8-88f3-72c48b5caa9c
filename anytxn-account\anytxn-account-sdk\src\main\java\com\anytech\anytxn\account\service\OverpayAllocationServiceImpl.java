package com.anytech.anytxn.account.service;

import com.anytech.anytxn.account.base.enums.RecordedEnum;
import com.anytech.anytxn.account.base.service.IOverpayAllocationService;
import com.anytech.anytxn.business.base.account.enums.TransactionTypeCodeEnum;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.LimitControlUnitDTO;
import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;

import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmTransactionCodeConfigSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.BalanceInwardTransferMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmStatementProcessSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;

import com.anytech.anytxn.parameter.base.account.domain.model.BalanceInwardTransfer;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.ICardProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.AccountBetweenCreditBalanceDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.AccountBetweenCreditBalanceDetailDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmBetweenAccountTypeOrderDetailDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.IAccountBetweenCreditBalanceService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;

import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmProductInfoSelfMapper;
import com.anytech.anytxn.transaction.base.domain.bo.AccountsPaymentAllocateBO;
import com.anytech.anytxn.transaction.base.domain.dto.account.AccountsPaymentAllocateDTO;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import com.anytech.anytxn.transaction.service.AccountsPaymentAllocationServiceImpl;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 原账户间溢缴款分配
 * 借贷分离改为账户间/内贷方余额分配
 *
 * 代码总体思路：
 * 使用内部类组装出有溢缴款的可用贷方交易账户并排序，
 * 顺序调用其内部内转方法进行金额的分配
 *
 * <AUTHOR>
 * @date 2021-6-4
 **/
@Service
@Slf4j
public class OverpayAllocationServiceImpl implements IOverpayAllocationService{
    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private ParmTransactionCodeConfigSelfMapper parmTransactionCodeConfigSelfMapper;
    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;
    @Autowired
    private ParmStatementProcessSelfMapper parmStatementProcessSelfMapper;
    @Autowired
    private ParmProductInfoSelfMapper parmProductInfoSelfMapper;
    @Autowired
    private ITxnRecordedService txnRecordedService;
    @Autowired
    private IAccountBetweenCreditBalanceService iAccountBetweenCreditBalanceService;
    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;
    @Autowired
    private AccountsPaymentAllocationServiceImpl accountsPaymentAllocationService;
    @Autowired
    private BalanceInwardTransferMapper balanceInwardTransferMapper;
    @Autowired
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;
    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;
    @Autowired
    private ICardProductInfoService cardProductInfoService;
    @Autowired
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    /**
     * 账户间/内贷方余额分配主逻辑
     * 循环一个客户
     * 暂时还无法控制整体事务
     * @param custReconciliationControlDTO
     */
    @Override
    @Transactional
    public void overpayAllocationProcessNew(CustReconciliationControlDTO custReconciliationControlDTO){

        //组装必要的客户、参数信息
        String customerId = custReconciliationControlDTO.getCustomerId();
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(custReconciliationControlDTO.getOrganizationNumber());
        String organizationNumber = organizationInfo.getOrganizationNumber();
        List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByCustomerId(organizationNumber,customerId);
        CustomerBasicInfo customerBasicInfo = customerBasicInfoSelfMapper.selectByOrgAndCustId(OrgNumberUtils.getOrg(), customerId);
        CustomerAuthorizationInfo customerAuthInfo = customerAuthorizationInfoSelfMapper.selectByOrgNumberAndCustomerId(OrgNumberUtils.getOrg(), customerId);

        AccountBetweenCreditBalanceDTO accountBetweenCreditBalanceDTO = iAccountBetweenCreditBalanceService.findOnlyOneByOrg(organizationNumber);


        //从账户间贷方余额使用顺序参数表中获取到管理账户和贷记账户的优先顺序，放到map中
        List<ParmBetweenAccountTypeOrderDetailDTO> parmBetweenAccountTypeOrderDetailDTOList = accountBetweenCreditBalanceDTO.getAccountTypeOrderDetailDTOS();

        Map<String, Integer> accountManagementPriorityMap = parmBetweenAccountTypeOrderDetailDTOList.stream()
                .collect(Collectors.toMap(ParmBetweenAccountTypeOrderDetailDTO::getCreditTransactionTypeCode, ParmBetweenAccountTypeOrderDetailDTO::getPriority));

        Map<String, Integer> overpayAccountbalancePriorityMap =accountBetweenCreditBalanceDTO.getAccountBetweenCreditBalanceDetailDTOS().stream()
                .collect(Collectors.toMap(AccountBetweenCreditBalanceDetailDTO::getCreditTransactionTypeCode,AccountBetweenCreditBalanceDetailDTO::getPriority));

        //组装各管理账户的入账信息
        Map<String,ManagementRecoredInfo> managementRecoredInfoMap = accountManagementInfos
                .stream().map(accountManagementInfo -> { return new ManagementRecoredInfo(accountManagementInfo,customerAuthInfo); })
                .collect(Collectors.toMap(e->e.getAccountManagementInfo().getAccountManagementId() ,e->e));


        //找到主账户
        AccountManagementInfo mainManagement =  findMainManagement(organizationInfo.getOrganizationNumber(),customerId);

        //循环所有管理账户，初始化管理账户信息
        List<AccountbalanceRecoredInfo> overpayAccountbalanceRecoredToReduce = new ArrayList<>();
        for(Map.Entry<String, ManagementRecoredInfo> entry:managementRecoredInfoMap.entrySet()) {

            ManagementRecoredInfo managementRecoredInfo = managementRecoredInfoMap.get(entry.getKey());
            managementRecoredInfo.init(organizationNumber, accountManagementPriorityMap, managementRecoredInfoMap, mainManagement);

            //查找该管理账户下的可以分配的溢缴款交易账户
            List<AccountBalanceInfo> allBalanceInfos = accountBalanceInfoSelfMapper.selectAccountBalanceInfoByManagementIdAndOrg(managementRecoredInfo.getAccountManagementInfo().getAccountManagementId(), organizationNumber);
            List<AccountbalanceRecoredInfo> overpayAccountbalanceRecoredInfos = allBalanceInfos
                    .stream()
                    .filter(accountBalanceInfo -> overpayAccountbalancePriorityMap.containsKey(accountBalanceInfo.getTransactionTypeCode()))
                    .map(accountBalanceInfo -> {
                        return new AccountbalanceRecoredInfo(organizationInfo, accountBalanceInfo, managementRecoredInfo,
                                overpayAccountbalancePriorityMap.get(accountBalanceInfo.getTransactionTypeCode())
                        );
                    }).collect(Collectors.toList());

            overpayAccountbalanceRecoredToReduce.addAll(overpayAccountbalanceRecoredInfos);
        }

        //初始化交易账户
        overpayAccountbalanceRecoredToReduce.forEach(AccountbalanceRecoredInfo::init);

        //排序（根据账户间贷方余额使用顺序参数)
        Collections.sort(overpayAccountbalanceRecoredToReduce);

        //主逻辑。循环交易账户准备冲减,组装冲减record列表
        for(AccountbalanceRecoredInfo accountbalanceRecoredInfo:overpayAccountbalanceRecoredToReduce){
            doNetoutOneAccount(accountbalanceRecoredInfo);
        }

    }

    /**
     * 由于账户间内转还不支持内存操作，暂时一个交易一个事务
     * @param accountbalanceRecoredInfo
     */

    private void doNetoutOneAccount(AccountbalanceRecoredInfo accountbalanceRecoredInfo){
        List<RecordedBO> recordedAllList = new ArrayList<>();
        if(accountbalanceRecoredInfo.leftAmount.compareTo(BigDecimal.ZERO) ==0){
            return;
        }
        if(accountbalanceRecoredInfo.isInnerFirst){
            accountbalanceRecoredInfo.doInnerNetout(recordedAllList);
            doBatchRecorded(recordedAllList);
            accountbalanceRecoredInfo.doBweenNetout(recordedAllList);
            doBatchRecorded(recordedAllList);
        }else {
            accountbalanceRecoredInfo.doBweenNetout(recordedAllList);
            accountbalanceRecoredInfo.doInnerNetout(recordedAllList);
            doBatchRecorded(recordedAllList);
        }
        //统一最后进行账户间归集
        accountbalanceRecoredInfo.doToMain(recordedAllList);
        doBatchRecorded(recordedAllList);

    }

    //@Transactional
    private void doBatchRecorded(List<RecordedBO> recordedAllList){
        for(RecordedBO recorded:recordedAllList){
            txnRecordedService.txnRecorded(recorded);
        }
        recordedAllList.clear();
    }


    //找到人民币账户的主管理账户
    private AccountManagementInfo findMainManagement(String orgNumber,String customerId){
        List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByOrgNumberAndCustomerId(orgNumber, customerId);
        List<CardAuthorizationInfo> authorizationInfos = cardAuthorizationInfos.stream().filter(x -> Objects.equals("0", x.getStatus())
                || Objects.equals("1", x.getStatus())
                || Objects.equals("2", x.getStatus())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(authorizationInfos)){
            CardAuthorizationInfo cardAuthorizationInfo = authorizationInfos.get(0);
            CardProductInfoResDTO cardProductInfo = cardProductInfoService.findByOrgAndProductNum(orgNumber, cardAuthorizationInfo.getProductNumber());
            AccountManagementInfo mainAccountManagement = accountManagementInfoSelfMapper.selectVaildByOrgProNumCurrAndCusId(orgNumber,cardProductInfo.getAccountProductNumber(),"156",customerId);
            return mainAccountManagement;

        }
        return null;
    }

    /**
     * 内部类。管理账户相关信息
     */
    private class ManagementRecoredInfo implements Comparable<ManagementRecoredInfo> {

        //自己所属的客户授权信息
        private CustomerAuthorizationInfo customerAuthInfo;

        //该客户名下所有管理账户，账户间余额分配时计算金额用
        private Map<String, ManagementRecoredInfo> managementRecoredInfoMap;
        //自己所属的管理账户
        private AccountManagementInfo accountManagementInfo;
        //人民币主账户
        private AccountManagementInfo mainManagement;

        //总欠款
        private BigDecimal totalDebit;

        //是否是人民币账户
        private boolean isCNY = false;

        //是否是外币账户
        private boolean isForeign = false;

        //是否是专项分期账户
        private boolean isInstallment = false;

        //排序用
        private Integer accountManagementPriority;
        //排序用的最低优先级
        private final Integer LOWEST_PRIORITY = 10000;

        public AccountManagementInfo getMainManagement() {
            return mainManagement;
        }
        public BigDecimal getTotalDebit() {
            return totalDebit;
        }

        public void setTotalDebit(BigDecimal totalDebit) {
            this.totalDebit = totalDebit;
        }

        public boolean isForeign() {
            return isForeign;
        }

        public Integer getAccountManagementPriority() {
            return accountManagementPriority;
        }

        public CustomerAuthorizationInfo getCustomerAuthInfo() {
            return customerAuthInfo;
        }

        public AccountManagementInfo getAccountManagementInfo() {
            return accountManagementInfo;
        }

        /**
         * 构造方法
         * @param accountManagementInfo
         * @param customerAuthInfo
         */
        ManagementRecoredInfo(AccountManagementInfo accountManagementInfo,CustomerAuthorizationInfo customerAuthInfo){
            this.accountManagementInfo = accountManagementInfo;
            this.customerAuthInfo = customerAuthInfo;
        }

        /**
         * 初始化管理账户，操作有：
         *  获取欠款余额
         *  加载账产品
         *  判断自己是外币账户还是人民币，还是分期
         *  赋值优先值
         *  赋值人民币主账户id
         * @param organizationNumber
         */
        protected void init(String organizationNumber,Map<String, Integer> accountManagementPriorityMap,
                            Map<String,ManagementRecoredInfo> managementRecoredInfoMap,AccountManagementInfo mainManagement){
            this.managementRecoredInfoMap = managementRecoredInfoMap;
            AccountStatisticsInfo accountStatisticsInfo88888 = accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(
                    accountManagementInfo.getAccountManagementId(), TransactionTypeCodeEnum.DEBIT_SUM.getCode(),organizationNumber);
            if(accountStatisticsInfo88888 != null){
                totalDebit = accountStatisticsInfo88888.getBalance();
            }else {
                totalDebit = BigDecimal.ZERO;
            }

            ParmAcctProductMainInfo parmAcctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(organizationNumber,accountManagementInfo.getProductNumber());

            //L-专项分期产品
            if(parmAcctProductMainInfo != null && "L".equals(parmAcctProductMainInfo.getAttribute())){
                isInstallment = true;
            }


            if("156".equals(accountManagementInfo.getCurrency())){
                isCNY = true;
            }else {
                isForeign = true;
            }
            computPriority(accountManagementPriorityMap);
            this.mainManagement = mainManagement;
        }
        //账户类型：1-人民币，2-外币，3-专项分期
        protected void computPriority(Map<String, Integer> accountManagementPriorityMap){
            if(isCNY){
                accountManagementPriority = accountManagementPriorityMap.get("1");
            }else if(isForeign){
                accountManagementPriority = accountManagementPriorityMap.get("2");
            }
            if(isInstallment){
                if(accountManagementPriority == null || accountManagementPriority > accountManagementPriorityMap.get("3")){
                    accountManagementPriority = accountManagementPriorityMap.get("3");
                }
            }

            if(accountManagementPriority == null){
                accountManagementPriority = LOWEST_PRIORITY;
            }
        }


        @Override
        public int compareTo(ManagementRecoredInfo o) {
            return this.getAccountManagementPriority().compareTo(o.getAccountManagementPriority());
        }

        /**
         * 扣减其他管理账户的欠款。
         * 账户间溢缴款分配时用于更新其他管理账户的欠款金额
         * @param id 管理账户id
         * @param subtractDebit 冲减掉的金欠款金额
         */
        public void subtractTotalDebitOtherManagementRecoredInfo(String id,BigDecimal subtractDebit){
            ManagementRecoredInfo otherManagementRecoredInfo = managementRecoredInfoMap.get(id);
            otherManagementRecoredInfo.setTotalDebit(otherManagementRecoredInfo.getTotalDebit().subtract(subtractDebit));
        }
    }

    /**
     * 内部类。交易账户相关信息
     */
    private class AccountbalanceRecoredInfo implements Comparable<AccountbalanceRecoredInfo>{
        /**
         * 交易账户所属的管理账户
         */
        private ManagementRecoredInfo managementRecoredInfo;

        /**
         * 本条交易账户本体
         */
        private AccountBalanceInfo accountBalanceInfo;

        /**
         * 交易账户上的余额内转表
         */
        private BalanceInwardTransfer balanceInwardTransfer;
        /**
         * 当前冲减剩余金额
         */
        private BigDecimal leftAmount;

        //是否会进行账户内
        private boolean isInnerNetout = false;
        //是否会进行账户间
        private boolean isBweenNetout= false;
        //是否允许向主账户归集溢缴款
        private boolean isToMain= false;

        //true = 先进行账户内，后进行账户间
        private boolean isInnerFirst = false;

        //排序用优先级
        private Integer accountbalancePriority;

        public Integer getAccountbalancePriority() {
            return accountbalancePriority;
        }

        public ManagementRecoredInfo getManagementRecoredInfo() {
            return managementRecoredInfo;
        }
        private OrganizationInfoResDTO organizationInfo;

        /**
         * 构造方法。
         * @param organizationInfo
         * @param accountBalanceInfo
         * @param managementRecoredInfo
         * @param accountbalancePriority 优先级。在账户间贷方余额使用顺序参数中配置。未配置的交易类型不会进行账户的贷方余额分配
         */
        AccountbalanceRecoredInfo(OrganizationInfoResDTO organizationInfo,AccountBalanceInfo accountBalanceInfo,ManagementRecoredInfo managementRecoredInfo,
                                  Integer accountbalancePriority){
            this.accountBalanceInfo = accountBalanceInfo;
            this.managementRecoredInfo = managementRecoredInfo;
            this.accountbalancePriority = accountbalancePriority;
            this.organizationInfo = organizationInfo;
        }

        @Override
        public int compareTo(AccountbalanceRecoredInfo o) {
            if(managementRecoredInfo.compareTo(o.getManagementRecoredInfo()) != 0){
                return managementRecoredInfo.compareTo(o.getManagementRecoredInfo());
            }else {
                return accountbalancePriority.compareTo(o.getAccountbalancePriority());
            }
        }

        /**
         * 初始化：
         * 1 初始化余额内转参数
         * 2 初始化是否可以内转等中间变量
         * 3 初始化余额，即可以用于冲减的溢缴款金额
         */
        public void init() {
            this.balanceInwardTransfer = balanceInwardTransferMapper.selectByTableIdAndOrg(this.accountBalanceInfo.getBalanceNetoutTableId(), organizationInfo.getOrganizationNumber());
            if (this.balanceInwardTransfer != null) {
                this.isInnerNetout = "Y".equals(this.balanceInwardTransfer.getInnerAccountNetout()) ? true:false;
                this.isBweenNetout = "Y".equals(this.balanceInwardTransfer.getBetweenAccountNetout()) ? true:false;
                this.isToMain = "Y".equals(this.balanceInwardTransfer.getToMainAccountCollectionOverpay()) ? true:false;
                // 1 优先账户内 2 优先账户间
                this.isInnerFirst = "1".equals(this.balanceInwardTransfer.getCreditBalanceFirstNetoutDimension())? true:false;
            }
            //外币账户溢缴款只允许账户内分配，不允许账户间分配。且不涉及归集到主账户
            if(this.managementRecoredInfo.isForeign()){
                this.isBweenNetout =false;
                this.isToMain = false;
            }
            this.leftAmount =this.accountBalanceInfo.getBalance();
        }

        /**
         * 账户内冲减主逻辑
         * @param recordedAllList 组装的待执行的record 列表
         */
        public void doInnerNetout(List<RecordedBO> recordedAllList ){
            if(!isInnerNetout|| balanceInwardTransfer == null){
                return;
            }
            //日期判断
            if (!matcheDateCondition(balanceInwardTransfer.getInnerAccountNetoutPoint())) {
                return;
            }

            BigDecimal totalDebit = managementRecoredInfo.getTotalDebit();
            if(totalDebit.compareTo(BigDecimal.ZERO)==0 || leftAmount.compareTo(BigDecimal.ZERO) ==0){
                return;
            }
            BigDecimal recordAmount;
            if(leftAmount.compareTo(totalDebit) >=0){
                recordAmount = totalDebit;
                managementRecoredInfo.setTotalDebit(BigDecimal.ZERO);
            }else {
                recordAmount = leftAmount;
                managementRecoredInfo.setTotalDebit(totalDebit.subtract(recordAmount));
            }
            //转入和转出两笔
            RecordedBO recorded3 = buildRecord(managementRecoredInfo.getAccountManagementInfo(),"3",recordAmount,organizationInfo.getNextProcessingDay());
            RecordedBO recorded4 = buildRecord(managementRecoredInfo.getAccountManagementInfo(),"4",recordAmount,organizationInfo.getNextProcessingDay());
            recordedAllList.add(recorded3);
            recordedAllList.add(recorded4);
            leftAmount = leftAmount.subtract(recordAmount);

        }

        /**
         * 账户间冲减主逻辑
         * @param recordedAllList 组装的待执行的record 列表
         */
        public void doBweenNetout(List<RecordedBO> recordedAllList){
            if(!isBweenNetout || balanceInwardTransfer == null){
                return;
            }
            if (!matcheDateCondition(balanceInwardTransfer.getBetweenAccountNetoutPoint())) {
                return;
            }
            BigDecimal recordAmount = leftAmount;
            AccountsPaymentAllocateDTO accountsPaymentAllocateDTO = new AccountsPaymentAllocateDTO();



            RecordedBO bweenNetoutrecord = buildRecord(managementRecoredInfo.getAccountManagementInfo(),"4",leftAmount,organizationInfo.getNextProcessingDay());
            accountsPaymentAllocateDTO.setTransactionCode(bweenNetoutrecord.getTxnTransactionCode());
            accountsPaymentAllocateDTO.setCurrency(bweenNetoutrecord.getTxnBillingCurrency());

            accountsPaymentAllocateDTO.setAmount(bweenNetoutrecord.getTxnBillingAmount());
            accountsPaymentAllocateDTO.setCustType(managementRecoredInfo.getCustomerAuthInfo().getType());
            accountsPaymentAllocateDTO.setCustGroup(managementRecoredInfo.getCustomerAuthInfo().getGroupType());

            accountsPaymentAllocateDTO.setOrganizationNumber(organizationInfo.getOrganizationNumber());
            accountsPaymentAllocateDTO.setCustomerId(managementRecoredInfo.getAccountManagementInfo().getCustomerId());

            AccountsPaymentAllocateBO accountsPaymentAllocateBO = null;
//            AccountsPaymentAllocateBO accountsPaymentAllocateBO = accountsPaymentAllocationService.accountsPaymentAllocation(accountsPaymentAllocateDTO, false);


            leftAmount = accountsPaymentAllocateBO.getRemainingAmount() == null ? BigDecimal.ZERO : accountsPaymentAllocateBO.getRemainingAmount();
            //没分配退出
            if(leftAmount.compareTo(recordAmount) ==0){
                return;
            }
            BigDecimal leftAmount2 = recordAmount.subtract(leftAmount);
            RecordedBO bweenNetoutrecordOut = buildRecord(managementRecoredInfo.getAccountManagementInfo(),"3",leftAmount2,organizationInfo.getNextProcessingDay());
            recordedAllList.add(bweenNetoutrecordOut);
            List<AccountsPaymentAllocateDTO> accountsPaymentAllocates = accountsPaymentAllocateBO.getAccountsPaymentAllocates();
            for (AccountsPaymentAllocateDTO accountsPaymentAllocate : accountsPaymentAllocates) {
                RecordedBO recordedAllocate = BeanMapping.copy(bweenNetoutrecord, RecordedBO.class);
                recordedAllocate.setTxnAccountManageId(accountsPaymentAllocate.getAccountManageId());
                recordedAllocate.setTxnBillingAmount(accountsPaymentAllocate.getAmount());
                managementRecoredInfo.subtractTotalDebitOtherManagementRecoredInfo(accountsPaymentAllocate.getAccountManageId(),accountsPaymentAllocate.getAmount());
                recordedAllocate.setTxnBillingCurrency(accountsPaymentAllocate.getCurrency());
                recordedAllList.add(recordedAllocate);
            }

        }

        /**
         * 主账户归集主逻辑
         * @param recordedAllList
         */
        public void doToMain(List<RecordedBO> recordedAllList){
            //如果配置为关、参数表为空、找不到主账户id、剩余金额为0.均退出
            if(!isToMain|| balanceInwardTransfer == null
                    || managementRecoredInfo.getMainManagement() == null
                    || leftAmount.compareTo(BigDecimal.ZERO) == 0){
                return;
            }
            //如果自己是主账户也不处理
            if(managementRecoredInfo.getMainManagement().getAccountManagementId().equals(managementRecoredInfo.getMainManagement().getAccountManagementId())){
                return;
            }
            RecordedBO recordedOut = buildRecord(managementRecoredInfo.getAccountManagementInfo(),"3",leftAmount,organizationInfo.getNextProcessingDay());
            RecordedBO recordedIn = buildRecord(managementRecoredInfo.getMainManagement(),"4",leftAmount,organizationInfo.getNextProcessingDay());
            recordedAllList.add(recordedOut);
            recordedAllList.add(recordedIn);
        }

        /**
         * 内部公共方法
         * 判断是否满足内转时点
         * @param netoutPoint  账户 NETOUT时点(1:每日，2:还款日，3:宽限日)
         * @return
         */
        private boolean matcheDateCondition(String netoutPoint){
            if(Objects.equals("1",netoutPoint)) {
                return true;
            }
            AccountStatementInfo accountStatementInfo = accountStatementInfoSelfMapper.selectAccountStatementInfo(managementRecoredInfo.getAccountManagementInfo().getOrganizationNumber(),
                    managementRecoredInfo.getAccountManagementInfo().getAccountManagementId(),
                    managementRecoredInfo.getAccountManagementInfo().getLastStatementDate());
            if(ObjectUtils.isEmpty(accountStatementInfo)){
                return false;
            }
            LocalDate paymentDueDate = null;
            LocalDate lastProcessingDay = organizationInfo.getLastProcessingDay();
            LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();

            if(Objects.equals("2",netoutPoint)){
                paymentDueDate= accountStatementInfo.getPaymentDueDate();
            }else if(Objects.equals("3",netoutPoint)){
                paymentDueDate= accountStatementInfo.getInterestWaiveGraceDate();
            }else {
                return false;
            }

            boolean flag = lastProcessingDay.isBefore(paymentDueDate)
                    && (paymentDueDate.isBefore(nextProcessingDay) || paymentDueDate.isEqual(nextProcessingDay));
            return flag;
        }

        /**
         * 内部公共方法
         * 组装入账Record
         * @param accountManagementInfo 需要入账的交易账户
         * @param type 是转出还是转入。3为转出，4为转入。是配置在内部交易码参数中
         * @param amount 入账金额
         * @param nextProcessingDay
         * @return
         */
        private RecordedBO buildRecord(AccountManagementInfo accountManagementInfo,String type,BigDecimal amount,LocalDate nextProcessingDay) {
            String txnTransactionCode = null;
            RecordedBO recorded = new RecordedBO();
            if(Objects.equals("3",type)){
                // 3.缴款转出交易码TRF-OUT-CDE
                txnTransactionCode = parmTransactionCodeConfigSelfMapper.selectByOrgAndType(accountManagementInfo.getOrganizationNumber(), "3").getTransactionCode();
                recorded.setTxnAccountManageId(accountManagementInfo.getAccountManagementId());
//                recorded.setSkipCreditPaymentAllocation("1");
                List<LimitControlUnitDTO> limitControlUnits = new ArrayList<>();
                LimitControlUnitDTO limitControlUnit = new LimitControlUnitDTO();
                //只传交易类型，指定交易类型
//                limitControlUnit.setLimitUnitCode("MO999");
//                limitControlUnit.setLimitUnitVersion("1");
                limitControlUnit.setTransactionType(accountBalanceInfo.getTransactionTypeCode());
                limitControlUnit.setAmount(amount);
                limitControlUnits.add(limitControlUnit);
                recorded.setLimitControlUnits(limitControlUnits);
            }else if(Objects.equals("4",type)){
                // 4.缴款转出转入交易码TRF-IN-CDE
                txnTransactionCode = parmTransactionCodeConfigSelfMapper.selectByOrgAndType(accountManagementInfo.getOrganizationNumber(), "4").getTransactionCode();
//                List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByOrgNumberAndCustomerId(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
//                List<CardAuthorizationInfo> authorizationInfos = cardAuthorizationInfos.stream().filter(x -> Objects.equals("0", x.getStatus())
//                        || Objects.equals("1", x.getStatus())
//                        || Objects.equals("2", x.getStatus())).collect(Collectors.toList());
//                if(CollectionUtils.isNotEmpty(authorizationInfos)){
//                    recorded.setTxnCardNumber(authorizationInfos.get(0).getCardNumber());
//                }
                recorded.setTxnAccountManageId(accountManagementInfo.getAccountManagementId());
            }
            // 入账金额
            recorded.setTxnBillingAmount(amount);
            // 入账币种
            recorded.setTxnBillingCurrency(accountManagementInfo.getCurrency());
            // 入账日期
            recorded.setTxnBillingDate(nextProcessingDay);
            recorded.setTxnPostMethod(RecordedEnum.REAL_TIME.getCode());
            recorded.setTxnReverseFeeIndicator(RecordedEnum.YES.getCode());
            recorded.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
            // 交易码参数表
            TransactionCodeResDTO codeConfig = transactionCodeService.findTransactionCode(accountManagementInfo.getOrganizationNumber(), txnTransactionCode);
            // 交易码
            recorded.setTxnTransactionCode(txnTransactionCode);
            log.info("txnTransactionCode:{},Description：{}",txnTransactionCode,codeConfig.getDescription());
            recorded.setTxnTransactionDescription(codeConfig.getDescription());
            // 交易日期
            recorded.setTxnTransactionDate(nextProcessingDay.atTime(0,0));
            // 交易金额
            recorded.setTxnTransactionAmount(amount);
            // 交易币种
            recorded.setTxnTransactionCurrency(accountManagementInfo.getCurrency());
            // 清算金额
            recorded.setTxnTransactionAmount(amount);
            // 清算币种
            recorded.setTxnSettlementCurrency(accountManagementInfo.getCurrency());
            recorded.setTxnExchangeRate(BigDecimal.ZERO);
            recorded.setTxnAuthorizationMatchIndicator(RecordedEnum.NOT_MATCH_AUTH.getCode());
            recorded.setTxnReleaseAuthorizationAmount(RecordedEnum.NOT_RECOVER.getCode());
            recorded.setTxnOutstandingAmount(BigDecimal.ZERO);
            recorded.setCustomerId(accountManagementInfo.getCustomerId());
            recorded.setTxnGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));


            return recorded;
        }
    }


    /**
     * 以下为旧方法。删除
     */
//
//    @Override
//    public void overpayAllocationProcess(AccountManagementInfo accountManagementInfo) {
//        log.info("AccountManagementId:{}",accountManagementInfo.getAccountManagementId());
//        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(accountManagementInfo.getOrganizationNumber());
//        if(ObjectUtils.isEmpty(organizationInfo)){
//            log.error("机构号不存在");
//            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.OR_E);
//        }
//        AccountStatementInfo accountStatementInfo = accountStatementInfoSelfMapper.selectAccountStatementInfo(accountManagementInfo.getOrganizationNumber(),
//                accountManagementInfo.getAccountManagementId(),
//                accountManagementInfo.getLastStatementDate());
//        if(ObjectUtils.isEmpty(accountStatementInfo)){
//            return;
//        }
//        List<ParmProductInfo> productInfos = parmProductInfoSelfMapper.isExists(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getProductNumber(), accountManagementInfo.getCurrency());
//        if(CollectionUtils.isEmpty(productInfos)){
//            log.error("账户产品不存在,org:{},productNumber:{},Currency:{}",accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getProductNumber(), accountManagementInfo.getCurrency());
//            return;
//        }
//        LocalDate paymentDueDate = null;
//        LocalDate lastProcessingDay = organizationInfo.getLastProcessingDay();
//        LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();
//        ParmStatementProcess parmStatementProcess = parmStatementProcessSelfMapper.isExists(accountManagementInfo.getOrganizationNumber(), productInfos.get(0).getStatementProcessingTableId());
//        if(null != parmStatementProcess){
//            if(Objects.equals("1",parmStatementProcess.getOverpaymentAllocatDate())){
//                //溢缴款分配日期选项" = 1（还款日）
//                //如果满足机构参数表(parm_organization_info)中的上次累计日期（last_accrued_thru_day）< 账单账户表中的还款日（payment_due_date) <= 机构参数表(parm_organization_info)中的当前累计日期（accrued_thru_day）
//                paymentDueDate= accountStatementInfo.getPaymentDueDate();
//            }else if(Objects.equals("2",parmStatementProcess.getOverpaymentAllocatDate())){
//                //溢缴款分配日期选项" = 2（宽限日)
//                //如果满足机构参数表(parm_organization_info)中的上次累计日期（last_accrued_thru_day）< 账单账户表中的宽限日（INTEREST_WAIVE_GRACE_DATE) <= 机构参数表(parm_organization_info)中的当前累计日期（accrued_thru_day）
//                paymentDueDate= accountStatementInfo.getInterestWaiveGraceDate();
//            }
//        }
//
//        boolean flag = lastProcessingDay.isBefore(paymentDueDate)
//                        && (paymentDueDate.isBefore(nextProcessingDay) || paymentDueDate.isEqual(nextProcessingDay));
//        if(!flag){
//            log.error("日期不符合,lastProcessingDay:{},paymentDueDate:{},nextProcessingDay:{}",lastProcessingDay,paymentDueDate,nextProcessingDay);
//            return;
//        }
//        AccountStatisticsInfo accountStatisticsInfo = accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(accountManagementInfo.getAccountManagementId(), TransactionTypeCodeEnum.AGGREGATION.getCode(), accountManagementInfo.getOrganizationNumber());
//        if(!(accountStatisticsInfo.getCurrency().equals(accountManagementInfo.getCurrency())
//                && accountStatisticsInfo.getBalance().compareTo(BigDecimal.ZERO) <0)){
//            log.error("币种不一致或余额小于0");
//            return;
//        }
//        BigDecimal overAmt = accountStatisticsInfo.getBalance().abs();
//        List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByOrgAndCid(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
//        List<AccountManagementInfo> otherManageList = accountManagementInfos.stream().filter(x -> !Objects.equals(accountManagementInfo.getAccountManagementId(), x.getAccountManagementId())
//                && Objects.equals(accountManagementInfo.getCurrency(), x.getCurrency())).collect(Collectors.toList());
//
//        BigDecimal balAmt = BigDecimal.ZERO;
//        for (AccountManagementInfo otherManagementInfo : otherManageList) {
//            AccountStatisticsInfo otherAccountStatisticsInfo = accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(otherManagementInfo.getAccountManagementId(), TransactionTypeCodeEnum.AGGREGATION.getCode(), otherManagementInfo.getOrganizationNumber());
//            if(otherAccountStatisticsInfo.getBalance().compareTo(BigDecimal.ZERO) > 0){
//                balAmt = balAmt.add(otherAccountStatisticsInfo.getBalance());
//            }
//        }
//        BigDecimal trfOutAmt = overAmt.compareTo(balAmt) >= 0 ? balAmt : overAmt;
//        if(trfOutAmt.compareTo(BigDecimal.ZERO) <= 0){
//            log.error("转出金额小于等于0：{}",trfOutAmt);
//            return;
//        }
//        //溢缴款转出转入交易入账
//        //3.缴款转出交易码TRF-OUT-CDE
//        Recorded outRecorded = buildRecord(accountManagementInfo, "3", trfOutAmt, nextProcessingDay);
//        // 4.缴款转出转入交易码TRF-IN-CDE
//        Recorded inRecorded = buildRecord(accountManagementInfo, "4", trfOutAmt, nextProcessingDay);
//        txnRecordedService.txnRecorded(outRecorded);
//        txnRecordedService.txnRecorded(inRecorded);
//    }
//
//    /**
//     * 入账参数
//     * @param accountManagementInfo
//     * @param type
//     * @param amount
//     * @param nextProcessingDay
//     * @return
//     */
//    private Recorded buildRecord(AccountManagementInfo accountManagementInfo,String type,BigDecimal amount,LocalDate nextProcessingDay) {
//       String txnTransactionCode = null;
//        Recorded recorded = new Recorded();
//        if(Objects.equals("3",type)){
//            // 3.缴款转出交易码TRF-OUT-CDE
//            txnTransactionCode = parmTransactionCodeConfigSelfMapper.selectByOrgAndType(accountManagementInfo.getOrganizationNumber(), "3").getTransactionCode();
//            recorded.setTxnAccountManageId(accountManagementInfo.getAccountManagementId());
//        }else if(Objects.equals("4",type)){
//            // 4.缴款转出转入交易码TRF-IN-CDE
//            txnTransactionCode = parmTransactionCodeConfigSelfMapper.selectByOrgAndType(accountManagementInfo.getOrganizationNumber(), "4").getTransactionCode();
//            List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByOrgNumberAndCustomerId(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
//            List<CardAuthorizationInfo> authorizationInfos = cardAuthorizationInfos.stream().filter(x -> Objects.equals("0", x.getStatus())
//                    || Objects.equals("1", x.getStatus())
//                    || Objects.equals("2", x.getStatus())).collect(Collectors.toList());
//            if(CollectionUtils.isNotEmpty(authorizationInfos)){
//                recorded.setTxnCardNumber(authorizationInfos.get(0).getCardNumber());
//            }
//        }
//        // 入账金额
//        recorded.setTxnBillingAmount(amount);
//        // 入账币种
//        recorded.setTxnBillingCurrency(accountManagementInfo.getCurrency());
//        // 入账日期
//        recorded.setTxnBillingDate(nextProcessingDay);
//        recorded.setTxnPostMethod(RecordedEnum.REAL_TIME.getCode());
//        recorded.setTxnReverseFeeIndicator(RecordedEnum.YES.getCode());

//        // 交易码参数表
//        TransactionCodeResDTO codeConfig = transactionCodeService.findTransactionCode(accountManagementInfo.getOrganizationNumber(), txnTransactionCode);
//        // 交易码
//        recorded.setTxnTransactionCode(txnTransactionCode);
//        log.info("txnTransactionCode:{},Description：{}",txnTransactionCode,codeConfig.getDescription());
//        recorded.setTxnTransactionDescription(codeConfig.getDescription());
//        // 交易日期
//        recorded.setTxnTransactionDate(nextProcessingDay);
//        // 交易金额
//        recorded.setTxnTransactionAmount(amount);
//        // 交易币种
//        recorded.setTxnTransactionCurrency(accountManagementInfo.getCurrency());
//        // 清算金额
//        recorded.setTxnTransactionAmount(amount);
//        // 清算币种
//        recorded.setTxnSettlementCurrency(accountManagementInfo.getCurrency());
//        recorded.setTxnExchangeRate(BigDecimal.ZERO);
//        recorded.setTxnAuthorizationMatchIndicator(RecordedEnum.NOT_MATCH_AUTH.getCode());
//        recorded.setTxnReleaseAuthorizationAmount(RecordedEnum.NOT_RECOVER.getCode());
//        recorded.setTxnOutstandingAmount(BigDecimal.ZERO);
//        recorded.setCustomerId(accountManagementInfo.getCustomerId());
//        return recorded;
//    }
}
