package com.anytech.anytxn.account.base.constants;

/**
 * 延滞滚动标志
 *
 * @Author: <PERSON><PERSON>
 * @Date: 2018/9/21 19:51
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 */
public enum Delinquency {

    /**
     * 0：于还款日进行延滞处理
     * 1：于账单日进行延滞处理
     */
    DELINQUENCY_REPAYMENT_DAY("0","于还款日进行延滞处理"),
    DELINQUENCY_ORDER_DAY("1","于账单日进行延滞处理");

    private String code;
    private String desc;

    Delinquency(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
