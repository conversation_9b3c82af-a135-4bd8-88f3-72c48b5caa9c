package com.anytech.anytxn.accounting.base.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.anytech.anytxn.common.core.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 总分核对表
 * 表:ACCOUNTANT_GLBALCHK
 *
 * <AUTHOR>
 * @date 2019-10-11
 */
public class TAmsGlbalchkResDTO extends BaseEntity {
    /**
     * 技术主键
     * 表字段:ID
     */
    private Long id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 币种
     * 表字段:CURR_CODE
     */
    private String currCode;

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    private String glAcct;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    private LocalDate postingDate;

    /**
     * 总账余额
     * 表字段:GL_BALANCE
     */
    private BigDecimal glBalance;

    /**
     * 分户余额
     * 表字段:AC_BALANCE
     */
    private BigDecimal acBalance;

    /**
     * 总分差额
     * 表字段:GL_GAP_BAL
     */
    private BigDecimal glGapBal;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;


    private String glAcctName;

    public String getGlAcctName() {
        return glAcctName;
    }

    public void setGlAcctName(String glAcctName) {
        this.glAcctName = glAcctName;
    }

    /**
     * 获取技术主键
     *
     * @return id Long
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置技术主键
     *
     * @param id 技术主键
     */
    public void setId(Long id) {
        this.id = id;
    }
    /**
     * 获取分行号
     *
     * @return branchid String
     */
    public String getBranchid() {
        return branchid;
    }

    /**
     * 设置分行号
     *
     * @param branchid 分行号
     */
    public void setBranchid(String branchid) {
        this.branchid = branchid == null ? null : branchid.trim();
    }

    /**
     * 获取币种
     *
     * @return currCode String
     */
    public String getCurrCode() {
        return currCode;
    }

    /**
     * 设置币种
     *
     * @param currCode 币种
     */
    public void setCurrCode(String currCode) {
        this.currCode = currCode == null ? null : currCode.trim();
    }

    /**
     * 获取科目号
     *
     * @return glAcct String
     */
    public String getGlAcct() {
        return glAcct;
    }

    /**
     * 设置科目号
     *
     * @param glAcct 科目号
     */
    public void setGlAcct(String glAcct) {
        this.glAcct = glAcct == null ? null : glAcct.trim();
    }

    /**
     * 获取入账日期
     *
     * @return postingDate LocalDate
     */
    public LocalDate getPostingDate() {
        return postingDate;
    }

    /**
     * 设置入账日期
     *
     * @param postingDate 入账日期
     */
    public void setPostingDate(LocalDate postingDate) {
        this.postingDate = postingDate;
    }

    /**
     * 获取总账余额
     *
     * @return glBalance BigDecimal
     */
    public BigDecimal getGlBalance() {
        return glBalance;
    }

    /**
     * 设置总账余额
     *
     * @param glBalance 总账余额
     */
    public void setGlBalance(BigDecimal glBalance) {
        this.glBalance = glBalance;
    }

    /**
     * 获取分户余额
     *
     * @return acBalance BigDecimal
     */
    public BigDecimal getAcBalance() {
        return acBalance;
    }

    /**
     * 设置分户余额
     *
     * @param acBalance 分户余额
     */
    public void setAcBalance(BigDecimal acBalance) {
        this.acBalance = acBalance;
    }

    /**
     * 获取总分差额
     *
     * @return glGapBal BigDecimal
     */
    public BigDecimal getGlGapBal() {
        return glGapBal;
    }

    /**
     * 设置总分差额
     *
     * @param glGapBal 总分差额
     */
    public void setGlGapBal(BigDecimal glGapBal) {
        this.glGapBal = glGapBal;
    }

    /**
     * 获取创建日期
     *
     * @return createTime LocalDateTime
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建日期
     *
     * @param createTime 创建日期
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新日期
     *
     * @return updateTime LocalDateTime
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新日期
     *
     * @param updateTime 更新日期
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新用户
     *
     * @return updateBy String
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新用户
     *
     * @param updateBy 更新用户
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    /**
     * 获取版本号
     *
     * @return versionNumber Long
     */
    public Long getVersionNumber() {
        return versionNumber;
    }

    /**
     * 设置版本号
     *
     * @param versionNumber 版本号
     */
    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }
}