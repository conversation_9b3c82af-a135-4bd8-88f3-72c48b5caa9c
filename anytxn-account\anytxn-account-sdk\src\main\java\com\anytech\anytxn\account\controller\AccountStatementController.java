package com.anytech.anytxn.account.controller;

import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.service.IAccountStatementInfoService;
import com.anytech.anytxn.account.base.utils.AnyTxnHttpResponseHelper;
import com.anytech.anytxn.business.base.account.domain.dto.ModifyPaymentDueDateDTO;
import com.anytech.anytxn.business.base.transaction.domain.dto.CardStatementDTO2;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;

import com.anytech.anytxn.business.base.transaction.domain.dto.CardStatementDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账单API
 *
 * <AUTHOR>
 * @date 2019-01-18 18:28
 **/
@RestController
@Tag(name = "账单账户操作")
public class AccountStatementController extends BizBaseController {

    @Autowired
    private IAccountStatementInfoService accountStatementInfoService;

    /**
     * 查询最近24期内的账单
     */
    @Operation(summary = "查询最近24期内的账单")
    @GetMapping("/account/lastedStatementInfo")
    AnyTxnHttpResponse<List<AccountStatementInfoDTO>> queryLastedStatementInfo(
            @Parameter(name = "accountManageInfoId", required = false, description = "管理账户Id")@RequestParam(value = "accountManageInfoId", required = false) String accountManageInfoId,
            @Parameter(name = "cardNumber", required = false, description = "卡号")@RequestParam(value = "cardNumber", required = false) String cardNumber) {
            return AnyTxnHttpResponse.success(accountStatementInfoService.findLastedStatementInfo(accountManageInfoId, cardNumber));
    }

    @Operation(summary = "更新延迟还款日")
    @PutMapping("/account/modifyPaymentDueDate")
    public AnyTxnHttpResponse modifyPaymentDueDate(@RequestBody ModifyPaymentDueDateDTO modifyPaymentDueDateDto) {
            accountStatementInfoService.modifyPaymentDueDate(modifyPaymentDueDateDto);
        return AnyTxnHttpResponseHelper.success(AccountingRepDetailEnum.US.getCnMsg());
    }

    /**
     * 查询个人卡账单信息
     */
    @Operation(summary = "查询个人卡账单")
    @PostMapping("/account/personalStatement")
    AnyTxnHttpResponse<PageResultDTO<CardStatementDTO>> queryList(@RequestBody CardStatementDTO2 req) {
        PageResultDTO<CardStatementDTO> result = accountStatementInfoService.findCardStatementInfos(req);
        return AnyTxnHttpResponse.success(result);
    }
}
