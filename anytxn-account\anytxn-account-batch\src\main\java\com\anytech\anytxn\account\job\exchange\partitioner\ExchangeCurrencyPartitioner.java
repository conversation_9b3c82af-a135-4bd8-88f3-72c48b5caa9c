//package com.anytech.anytxn.accounting.job.exchange.partitioner;
//
//import com.anytech.anytxn.accounting.service.IAccountManageInfoService;
//import org.springframework.batch.core.partition.support.Partitioner;
//import org.springframework.batch.item.ExecutionContext;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//
//import java.util.*;
//
///**
// * <AUTHOR>
// * @date 2018-11-28
// **/
//public class ExchangeCurrencyPartitioner implements Partitioner {
//
//    @Value("#{jobParameters['job.partitionKey']}")
//    private String partitionKey;
//
//    @Autowired
//    private IAccountManageInfoService accountManageInfoService;
//
//    @Override
//    public Map<String, ExecutionContext> partition(int gridSize) {
//        Map<String, ExecutionContext> result = new HashMap<>(2);
//
//        int max = accountManageInfoService.getAccountManagementIdCount4Exchange(partitionKey);
//        int min = 1;
//        if (max < 1) {
//            return result;
//        }
//        int start = min;
//        //每个分区数量
//        int size = (max - min) / gridSize + 1;
//        int end = start + size - 1;
//        //用于接收rownum
//        ArrayList<Integer> arr = new ArrayList<>();
//
//        while (start <= max) {
//            if (end >= max) {
//                end = max;
//            }
//            arr.add(start);
//            arr.add(end);
//            start += size;
//            end += size;
//        }
//        List<String> list = accountManageInfoService.queryAccountManagementIds4Exchange(partitionKey, arr);
//
//        int resultMin = 1;
//        int resultMax = list.size();
//
//        int num = 1;
//        int resultSize = 2;
//        int resultStart = resultMin;
//        int resultEnd = resultStart + 1;
//        while (resultStart <= resultMax) {
//            if (resultEnd >= resultMax) {
//                resultEnd = resultMax;
//            }
//            ExecutionContext context = new ExecutionContext();
//            context.put("fromId", list.get(resultStart - 1));
//            context.put("endId", list.get(resultEnd - 1));
//
//            context.put("threadName", "Thread" + num);
//            result.put("partition" + num, context);
//
//            resultStart += resultSize;
//            resultEnd += resultSize;
//            num++;
//        }
//        return result;
//    }
//
//}
