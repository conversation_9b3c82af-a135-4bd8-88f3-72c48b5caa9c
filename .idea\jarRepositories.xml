<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="sgb-snapshots" />
      <option name="name" value="sgb-snapshots" />
      <option name="url" value="http://10.0.9.13:8083/nexus/content/repositories/sgb-snapshots/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="public-snapshot" />
      <option name="name" value="public-snapshot" />
      <option name="url" value="http://10.0.9.13:8083/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="public" />
      <option name="name" value="public" />
      <option name="url" value="http://10.0.9.13:8081/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="anytxn-product-snapshots" />
      <option name="name" value="anytxn-product-snapshots" />
      <option name="url" value="http://10.0.9.13:8083/nexus/content/repositories/anytxn-product-snapshots/" />
    </remote-repository>
  </component>
</project>