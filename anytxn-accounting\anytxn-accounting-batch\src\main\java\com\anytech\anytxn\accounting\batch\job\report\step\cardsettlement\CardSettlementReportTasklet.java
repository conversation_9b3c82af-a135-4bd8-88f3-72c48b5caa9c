package com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement;

import com.anytech.anytxn.accounting.base.enums.ChannelEnum;
import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement.dto.rate.RateCardSettlement;
import com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement.dto.norate.CardSettlement;
import com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement.model.CardSettleReport;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.anytech.anytxn.business.dao.transaction.mapper.SettlementLogSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.SettlementLog;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmCurrencyRate;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateSelfMapper;
import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.config.CardSettleReportConfigurer;
import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportListTasklet;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 发卡清算
 * <AUTHOR>
 * @date 2020/9/13
 */
@Slf4j
public class CardSettlementReportTasklet extends AbstractReportListTasklet {

    private TimeTypeEnum timeTypeEnum;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private ParmTransactionCodeSelfMapper parmTransactionCodeSelfMapper;
    @Autowired
    private ParmCurrencyRateSelfMapper parmCurrencyRateSelfMapper;
    @Autowired
    private SettlementLogSelfMapper settlementLogSelfMapper;

    public CardSettlementReportTasklet(BaseConfigurer baseConfigurer, TimeTypeEnum timeTypeEnum) {
        super(baseConfigurer);
        this.timeTypeEnum = timeTypeEnum;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        if (reportConfigurer.isAbleReport()) {
            List<SettlementLog> settlementLogs = loadTransactionModels();
            // 无需汇率转换
            List<SettlementLog> noRateSettlementLogs = Lists.newArrayList();
            // 需要汇率转换
            List<SettlementLog> rateSettlementLogs = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(settlementLogs)) {
                for (SettlementLog settlementLog : settlementLogs) {
                    if (settlementLog.getTxnSettlementCurrency() == null
                            || settlementLog.getTxnSettlementCurrency().equals("156")
                            || settlementLog.getTxnBillingCurrency().equals(settlementLog.getTxnSettlementCurrency())) {
                        noRateSettlementLogs.add(settlementLog);
                    } else {
                        rateSettlementLogs.add(settlementLog);
                    }
                }
            }

            Pair<List<CardSettleReport>, List<CardSettleReport>> data = modelToReport(noRateSettlementLogs, rateSettlementLogs);
            out(data);
        }

        return RepeatStatus.FINISHED;
    }

    /**
     * 当前日期所有已入账交易
     * @return
     */
    private List<SettlementLog> loadTransactionModels(){
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        // TODO  换成settlementLog
        switch (timeTypeEnum) {
            case D:
                // 测试数据
//                organizationInfo.setToday(LocalDate.of(2001,1,27));
                return settlementLogSelfMapper.selectBackByPostDate(organizationInfo.getToday(),OrgNumberUtils.getOrg());
            case M:
                // 测试数据
//                organizationInfo.setToday(LocalDate.of(2001,2,28));
                if (TimeUtils.isEndMonth(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.mothRand(organizationInfo.getToday());
                    return settlementLogSelfMapper.selectBackByPostDate2(mothRand.getKey(), mothRand.getValue(),OrgNumberUtils.getOrg());
                }
                return null;
            case Q:
                // 测试数据
//                organizationInfo.setToday(LocalDate.of(2001,3,31));
                if (TimeUtils.isEndQuarter(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> quarterRand = TimeUtils.quarterRand(organizationInfo.getToday());
                    return settlementLogSelfMapper.selectBackByPostDate2(quarterRand.getKey(), quarterRand.getValue(),OrgNumberUtils.getOrg());
                }
                return null;
            case Y:
                // 测试数据
//                organizationInfo.setToday(LocalDate.of(2001,12,31));
                if (TimeUtils.isEndYear(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> yearRand = TimeUtils.yearRand(organizationInfo.getToday());
                    return settlementLogSelfMapper.selectBackByPostDate2(yearRand.getKey(), yearRand.getValue(),OrgNumberUtils.getOrg());
                }
                return null;
            default:
                return null;
        }
    }

    /**
     * 业务数据转报表数据
     * @return 入账汇总,入账会计,入账差异汇总,入账差异会计
     */
    private Pair<List<CardSettleReport>,List<CardSettleReport>> modelToReport(List<SettlementLog> noRateSettlements, List<SettlementLog> rateSettlements){
        List<CardSettleReport> noRateReport = Lists.newArrayList();
        List<CardSettleReport> rateReport = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(noRateSettlements)) {
            noRateReport = toReport(noRateSettlements, false);
        }

        if (CollectionUtils.isNotEmpty(rateSettlements)) {
            rateReport = toReport(rateSettlements, true);
        }

        return Pair.of(noRateReport, rateReport);
    }

    private List<CardSettleReport> toReport(List<SettlementLog> rateSettlements, boolean ableRate){
        List<CardSettleReport> result = Lists.newArrayList();
        // 基于交易来源 + 交易码 + 清算币种分组
        Map<String, List<SettlementLog>> group = rateSettlements.stream().collect(Collectors.groupingBy(x -> x.getTxnTransactionSource() + x.getTxnTransactionCode() + x.getTxnSettlementCurrency()));
        for (Map.Entry<String, List<SettlementLog>> entry : group.entrySet()) {
            List<SettlementLog> value = entry.getValue();
            SettlementLog temple = value.get(0);

            // 汇总金额
            Optional<BigDecimal> settleAmount = value.stream().filter(x-> x.getTxnSettlementAmount() != null).map(SettlementLog::getTxnSettlementAmount).reduce(BigDecimal::add);

            CardSettleReport report = new CardSettleReport();
            // 入账日期
            report.setPostDate(temple.getTxnBillingDate());
            // 清算金额
            report.setSettleAmount(settleAmount.orElse(BigDecimal.ZERO));
            // 清算币种
            report.setSettleCurrCode(temple.getTxnSettlementCurrency());
            // 交易来源
            try {
                report.setTransactionSource(ChannelEnum.getDes(temple.getTxnTransactionSource()));
            }catch (Exception e){

            }
            // 交易码
            report.setTxnCodeOrig(temple.getTxnTransactionCode());
            // 描述
            try {
                ParmTransactionCode transactionCode = parmTransactionCodeSelfMapper.selectByOrgNumberAndCode(OrgNumberUtils.getOrg(), temple.getTxnTransactionCode());
                report.setDescription(transactionCode.getDescription());
            }catch (Exception e){

            }
            // 数据量
            report.setGroupSize(value.size());
            if (ableRate && temple.getTxnSettlementCurrency() != null && !temple.getTxnSettlementCurrency().equals("156")) {
                // 转人命币汇率
                ParmCurrencyRate currencyRate = parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(OrgNumberUtils.getOrg(), temple.getTxnSettlementCurrency(), "156", "0");
                if (currencyRate == null) {
                    log.warn("币种 {} 没有转换人民币参数", temple.getTxnSettlementCurrency());
                } else {
                    BigDecimal rmb = report.getSettleAmount().multiply(BigDecimal.valueOf(currencyRate.getRateValue())).divide(BigDecimal.valueOf(10000L), 2, BigDecimal.ROUND_HALF_UP);

                    // 汇率
                    report.setRate(BigDecimal.valueOf(currencyRate.getRateValue()).divide(BigDecimal.valueOf(10000L), 4, BigDecimal.ROUND_HALF_UP));
                    // 转人民币金额
                    report.setRmbAmount(rmb);
                }
            }

            result.add(report);
        }

        return result;
    }


    /**
     * 输出报表（2个）
     * @param data
     */
    private void out(Pair<List<CardSettleReport>,List<CardSettleReport>> data) throws FileNotFoundException {
        List<CardSettleReport> noRate = data.getKey();
        List<CardSettleReport> rate = data.getValue();

        CardSettleReportConfigurer reportConfigurer0 = (CardSettleReportConfigurer)reportConfigurer;

        Map<String, Object> param = new HashMap<>();
        if (CollectionUtils.isNotEmpty(noRate)) {
            reportConfigurer0.putParam(param, false, timeTypeEnum);
            outReport(Lists.newArrayList(CardSettlement.resetReportData(noRate, timeTypeEnum)), reportConfigurer0.outFile(false, timeTypeEnum), reportConfigurer0.outFileName(false, timeTypeEnum), reportConfigurer0.templeFile(false, timeTypeEnum), param);
        }
        if (CollectionUtils.isNotEmpty(rate)) {
            reportConfigurer0.putParam(param, true, timeTypeEnum);
            outReport(Lists.newArrayList(RateCardSettlement.resetReportData(rate, timeTypeEnum)), reportConfigurer0.outFile(true, timeTypeEnum), reportConfigurer0.outFileName(true, timeTypeEnum), reportConfigurer0.templeFile(true, timeTypeEnum), param);
        }
    }

}
