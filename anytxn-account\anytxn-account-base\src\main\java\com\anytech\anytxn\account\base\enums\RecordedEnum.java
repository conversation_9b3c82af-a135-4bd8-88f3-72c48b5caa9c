package com.anytech.anytxn.account.base.enums;

/**
 * 接口参数的枚举类
 *
 * <AUTHOR>
 * @date 2018-9-22
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 */
public enum RecordedEnum {

    /**
     * 入账方式
     * 0=实时入账
     * 1=批量入账
     */
    REAL_TIME("0", "实时入账"),
    BATCH("1", "批量入账"),

    /**
     * 拒绝重入账标志
     * 0=普通交易
     * 1=拒绝重入账交易
     */
    NORMAL_TRANS("0", "普通交易"),
    REPEAT_TRANS("1", "拒绝重入账交易"),

    /**
     * 冲减交易费用标识
     * 0=是
     * 1=否
     */
    YES("0", "冲减交易费用标识:是"),
    NO("1", "冲减交易费用标识:否"),

    /**
     * 授权匹配标志
     * 0=未匹配授权；1=匹配授权
     */
    NOT_MATCH_AUTH("0", "未匹配授权"),
    MATCH_AUTH("1", "匹配授权"),


    /**
     * 是否恢复授权占用额度标志
     * Y=需要；N=不需要
     */
    RECOVER("Y", "需要恢复授权占用额度标志"),
    NOT_RECOVER("N", "不需要恢复授权占用额度标志");


    private String code;
    private String reason;


    RecordedEnum(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }
}
