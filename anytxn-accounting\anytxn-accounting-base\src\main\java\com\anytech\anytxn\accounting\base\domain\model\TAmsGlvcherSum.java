package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.anytxn.common.core.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 汇总传票表 
 * 表:ACCOUNTANT_GLVCHER_SUM
 * <AUTHOR> 
 * @date 2019-10-11 
 */
public class TAmsGlvcherSum extends BaseEntity {
    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 币种
     * 表字段:CURR_CODE
     */
    private String currCode;

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    private String glAcct;

    /**
     * 借方发生额
     * 表字段:OCCUR_DB
     */
    private BigDecimal occurDb;

    /**
     * 贷方发生额
     * 表字段:OCCUR_CR
     */
    private BigDecimal occurCr;

    /**
     * 借方发生笔数
     * 表字段:CNT_DR
     */
    private BigDecimal cntDr;

    /**
     * 贷方发生笔数
     * 表字段:CNT_CR
     */
    private BigDecimal cntCr;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    private LocalDate postingDate;

    /**
     * 处理标识
     * 表字段:PROCESS_IND
     */
    private String processInd;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

    private String fundId;

    /**
     * 获取技术主键
     * @return id Long
     */
    public String getId() {
        return id;
    }

    /**
     * 设置技术主键
     * @param id 技术主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取分行号
     * @return branchid String
     */
    public String getBranchid() {
        return branchid;
    }

    /**
     * 设置分行号
     * @param branchid 分行号
     */
    public void setBranchid(String branchid) {
        this.branchid = branchid == null ? null : branchid.trim();
    }

    /**
     * 获取币种
     * @return currCode String
     */
    public String getCurrCode() {
        return currCode;
    }

    /**
     * 设置币种
     * @param currCode 币种
     */
    public void setCurrCode(String currCode) {
        this.currCode = currCode == null ? null : currCode.trim();
    }

    /**
     * 获取科目号
     * @return glAcct String
     */
    public String getGlAcct() {
        return glAcct;
    }

    /**
     * 设置科目号
     * @param glAcct 科目号
     */
    public void setGlAcct(String glAcct) {
        this.glAcct = glAcct == null ? null : glAcct.trim();
    }

    /**
     * 获取借方发生额
     * @return occurDb BigDecimal
     */
    public BigDecimal getOccurDb() {
        return occurDb;
    }

    /**
     * 设置借方发生额
     * @param occurDb 借方发生额
     */
    public void setOccurDb(BigDecimal occurDb) {
        this.occurDb = occurDb;
    }

    /**
     * 获取贷方发生额
     * @return occurCr BigDecimal
     */
    public BigDecimal getOccurCr() {
        return occurCr;
    }

    /**
     * 设置贷方发生额
     * @param occurCr 贷方发生额
     */
    public void setOccurCr(BigDecimal occurCr) {
        this.occurCr = occurCr;
    }

    /**
     * 获取入账日期
     * @return postingDate LocalDate
     */
    public LocalDate getPostingDate() {
        return postingDate;
    }

    /**
     * 设置入账日期
     * @param postingDate 入账日期
     */
    public void setPostingDate(LocalDate postingDate) {
        this.postingDate = postingDate;
    }

    /**
     * 获取处理标识
     * @return processInd String
     */
    public String getProcessInd() {
        return processInd;
    }

    /**
     * 设置处理标识
     * @param processInd 处理标识
     */
    public void setProcessInd(String processInd) {
        this.processInd = processInd == null ? null : processInd.trim();
    }

    /**
     * 获取创建日期
     * @return createTime LocalDateTime
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建日期
     * @param createTime 创建日期
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新日期
     * @return updateTime LocalDateTime
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新日期
     * @param updateTime 更新日期
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新用户
     * @return updateBy String
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新用户
     * @param updateBy 更新用户
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    /**
     * 获取版本号
     * @return versionNumber Long
     */
    public Long getVersionNumber() {
        return versionNumber;
    }

    /**
     * 设置版本号
     * @param versionNumber 版本号
     */
    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getFundId() {
        return fundId;
    }

    public void setFundId(String fundId) {
        this.fundId = fundId;
    }

    public BigDecimal getCntDr() {
        return cntDr;
    }

    public void setCntDr(BigDecimal cntDr) {
        this.cntDr = cntDr;
    }

    public BigDecimal getCntCr() {
        return cntCr;
    }

    public void setCntCr(BigDecimal cntCr) {
        this.cntCr = cntCr;
    }
}