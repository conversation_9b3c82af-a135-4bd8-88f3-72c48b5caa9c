package com.anytech.anytxn.accounting.base.domain.bo;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsSumDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvatbalDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherAbsDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherDTO;
import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 流水生成传票DTO
 * @author: ZXL
 * @create: 2019-10-08 09:43
 */
@Getter
@Setter
public class GenerateVoucherBO extends BaseEntity {

    /**
     * 会计传票
     */
    private List<TAmsGlvcherDTO> tAmsGlvchers;
    /**
     * abs传票
     */
    private List<TAmsGlvcherAbsDTO>  amsGlvcherAbs;
    /**
     * 新交易流水汇总
     */
    private List<TAmsGlamsSumDTO> newAmsGlamsSums;
    /**
     * 旧交易流水汇总
     */
    private List<TAmsGlamsSumDTO> oldAmsGlamsSums;
    /**
     * 新增值税分户余额
     */
    private List<TAmsGlvatbalDTO> newTamsGlVatBals;

    /**
     * 旧增值税分户余额
     */
    private List<TAmsGlvatbalDTO> oldTamsGlVatBals;

    /**
     * 更新流水
     */
    private List<TAmsGlamsDTO>   upddateGlAms;

    public GenerateVoucherBO(List<TAmsGlvcherDTO> tAmsGlvchers, List<TAmsGlamsSumDTO> newAmsGlamsSums,
                             List<TAmsGlvatbalDTO> newTamsGlVatBals, List<TAmsGlvatbalDTO> oldTamsGlVatBals,
                             List<TAmsGlamsSumDTO> oldAmsGlamsSums, List<TAmsGlvcherAbsDTO> amsGlvcherAbs,
                             List<TAmsGlamsDTO>   upddateGlAms) {
        this.tAmsGlvchers = tAmsGlvchers;
        this.newAmsGlamsSums = newAmsGlamsSums;
        this.newTamsGlVatBals = newTamsGlVatBals;
        this.oldTamsGlVatBals = oldTamsGlVatBals;
        this.oldAmsGlamsSums = oldAmsGlamsSums;
        this.amsGlvcherAbs = amsGlvcherAbs;
        this.upddateGlAms=upddateGlAms;
    }

    @Override
    public String toString() {
        return "GenerateVoucherBO{" +
                "tAmsGlvchers=" + tAmsGlvchers +
                ", tAmsGlamsSums=" + newAmsGlamsSums +
                ", newTamsGlVatBals=" + newTamsGlVatBals +
                ", oldTamsGlVatBals=" + oldTamsGlVatBals +
                ", absAmsGlamsSums=" + oldAmsGlamsSums +
                ", amsGlvcherAbs=" + amsGlvcherAbs +
                ", upddateGlAms=" + upddateGlAms +
                '}';
    }
}
