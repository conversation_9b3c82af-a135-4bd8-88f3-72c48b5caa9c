package com.anytech.anytxn.account.service;

import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.base.service.IStampTaxSumLimitService;
import com.anytech.anytxn.business.dao.account.model.StampTaxSumLimit;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.limit.base.domain.model.LimitChangeHistoryDetail;
import com.anytech.anytxn.limit.base.domain.model.LimitChangeHistorySummary;
import com.anytech.anytxn.limit.enums.ChangeSourceEnum;
import com.anytech.anytxn.limit.enums.ChangeTypeEnum;
import com.anytech.anytxn.limit.mapper.LimitChangeHistoryDetailSelfMapper;
import com.anytech.anytxn.limit.mapper.LimitChangeHistorySummarySelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-06-25
 */
@Service
@Slf4j
public class StampTaxSumLimitServiceImpl implements IStampTaxSumLimitService {
    @Autowired
    private LimitChangeHistorySummarySelfMapper summarySelfMapper;
    @Autowired
    private LimitChangeHistoryDetailSelfMapper detailSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Override
    public StampTaxSumLimit shardSumProcess() {
        StampTaxSumLimit stampTaxSumLimit = new StampTaxSumLimit();
        stampTaxSumLimit.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        stampTaxSumLimit.setOrgNumber(OrgNumberUtils.getOrg());
        stampTaxSumLimit.setLimitTypeCo("SC99");
        stampTaxSumLimit.setLimitTypeCur("156");
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        if (organizationInfo == null){
            log.error("根据机构号:{}查询机构参数 数据不存在", OrgNumberUtils.getOrg());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
        }
        //当前机构日期
        LocalDate today = organizationInfo.getToday();
        //授信历史概要表(LIMIT_CHANGE_HISTORY_SUMMARY)筛选条件：CHANGE_SOURCE CHANGE_TYPE，当前系统日期 取出对应的BIZ_TRACE_NUMBER授信业务流水号
        List<LimitChangeHistorySummary> summaryList = summarySelfMapper.selectByOrgNum(OrgNumberUtils.getOrg());
        //汇总固定额度
        BigDecimal sumFixLimitAmo = BigDecimal.ZERO;
        //汇总调整固定额度
        BigDecimal sumPermLimitAft = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(summaryList)){
            //筛选条件:当前系统日期=createTime，changeSource=2,changeType = 4
            List<LimitChangeHistorySummary> historySummaries = summaryList.stream().filter(
                    x -> x.getCreateTime()!=null && (today.equals(x.getCreateTime().toLocalDate()))
                            && ChangeSourceEnum.CHANGE_SOURCE_TWO.getCode().equals(x.getChangeSource())
                            && ChangeTypeEnum.CHANGE_TYPE_FOUR.getCode().equals(x.getChangeType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(historySummaries)){
                Map<String, List<LimitChangeHistoryDetail>> detailMap = new HashMap<>();
                List<LimitChangeHistoryDetail> detailList = new ArrayList<>(16);
                for (LimitChangeHistorySummary summary : historySummaries) {
                    List<LimitChangeHistoryDetail> historyDetails = detailSelfMapper.selectByBizTraceNumAndOrgNum(OrgNumberUtils.getOrg(), summary.getBizTraceNumber());
                    if (CollectionUtils.isNotEmpty(historyDetails)) {
                        for (LimitChangeHistoryDetail limitChangeHistoryDetail : historyDetails) {
                            if (detailMap.containsKey(limitChangeHistoryDetail.getCustomerId())) {
                                detailMap.get(limitChangeHistoryDetail.getCustomerId()).add(limitChangeHistoryDetail);
                            } else {
                                detailMap.put(limitChangeHistoryDetail.getCustomerId(),historyDetails);
                            }
                        }
                    }
                }
                for (Map.Entry<String, List<LimitChangeHistoryDetail>> entry : detailMap.entrySet()) {
                    List<LimitChangeHistoryDetail> details = entry.getValue();
                    CustomerBasicInfo customerBasicInfo = customerBasicInfoSelfMapper.selectByOrgAndCustId(historySummaries.get(0).getOrganizationNumber(),details.get(0).getCustomerId());
                    if (!customerBasicInfo.getOpenDate().equals(today)){
                        continue;
                    }
                    List<LimitChangeHistoryDetail> collect = details.stream()
                            .sorted(Comparator.comparing(LimitChangeHistoryDetail::getAfterValue).reversed()
                                    .thenComparing(LimitChangeHistoryDetail::getCreateTime)).collect(Collectors.toList());
                    detailList.add(collect.get(0));
                }
                for (LimitChangeHistoryDetail detail : detailList){
                    sumFixLimitAmo = sumFixLimitAmo.add(new BigDecimal(detail.getAfterValue()));
                }

            }
            //筛选条件:当前系统日期=createTime，changeSource=1,changeType = 1
            List<LimitChangeHistorySummary> changeHistorySummaries = summaryList.stream().filter(
                    x -> x.getCreateTime()!=null && (today.equals(x.getCreateTime().toLocalDate()))
                            && ChangeSourceEnum.CHANGE_SOURCE_ONE.getCode().equals(x.getChangeSource())
                            && ChangeTypeEnum.CHANGE_TYPE_ONE.getCode().equals(x.getChangeType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(changeHistorySummaries)){
                for (LimitChangeHistorySummary summary : changeHistorySummaries){
                    List<LimitChangeHistoryDetail> historyDetails = detailSelfMapper.selectByBizTraceNumAndOrgNum(OrgNumberUtils.getOrg(), summary.getBizTraceNumber());
                    if (CollectionUtils.isNotEmpty(historyDetails)){
                        for (LimitChangeHistoryDetail detail : historyDetails){
                            BigDecimal after = new BigDecimal(detail.getAfterValue());
                            BigDecimal before = new BigDecimal(detail.getBeforeValue());
                            sumPermLimitAft = sumPermLimitAft.add(after.subtract(before));
                        }
                    }
                }
            }
        }
        if (sumPermLimitAft.compareTo(BigDecimal.ZERO) < 0){
            sumPermLimitAft = BigDecimal.ZERO;
        }
        stampTaxSumLimit.setSumFixLimitAmo(sumFixLimitAmo);
        stampTaxSumLimit.setSumPermLimitAft(sumPermLimitAft);
        stampTaxSumLimit.setCreateTime(today.atTime(LocalTime.now()));
        stampTaxSumLimit.setVersionNumber(1L);
        return stampTaxSumLimit;
    }
}
