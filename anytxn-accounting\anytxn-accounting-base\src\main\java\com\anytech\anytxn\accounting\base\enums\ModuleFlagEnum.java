package com.anytech.anytxn.accounting.base.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 模块标识
 * @date 2020-02-16 14:06
 **/
public enum ModuleFlagEnum {
    /**
     * 0：核心账务
     * 4：结转
     * 5: 会计结转_增值税
     * 7: 会计结转-年终决算
     * 8: 分期递延
     * 9: 分期ABS
     * A: 会计ABS
     * B: 全账户ABS
     * C: 不良ABS
     */
    CORE("0", "核心账务"),
    CHARGE_OFF("4", "结转"),
    TAX("5", "会计结转_增值税"),
    YEAR_END("7", "会计结转-年终决算"),
    INST("8", "分期递延"),
    INST_ABS("9", "分期ABS"),
    GL_ABS("A", "会计ABS"),
    ALL_ABS("B", "全账户ABS"),
    BAD_ABS("C", "不良ABS"),

    ;
    private String code;
    private String desc;


    ModuleFlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String getValue(String code) {
        if (code == null) {
            return null;
        }
        ModuleFlagEnum[] var2 = values();
        int var3 = var2.length;
        for (int var4 = 0; var4 < var3; ++var4) {
            ModuleFlagEnum moduleFlagEnum = var2[var4];
            if (Objects.equals(code, moduleFlagEnum.getCode())) {
                return moduleFlagEnum.getDesc();
            }
        }
        return null;
    }
}
