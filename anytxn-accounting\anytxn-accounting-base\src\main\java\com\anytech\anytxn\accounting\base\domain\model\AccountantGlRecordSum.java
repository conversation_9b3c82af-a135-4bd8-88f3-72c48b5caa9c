package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.anytxn.common.core.base.BaseEntity;
import com.anytech.batch.file.SeparatedType;
import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TransferFileObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@TransferFileObject(separatedType = SeparatedType.SEPARATOR, lineTailSeparator = false,
        encoding = "UTF-8", description = "记账汇总文件", postfix = ".csv",separator = ",")
public class AccountantGlRecordSum extends BaseEntity implements Comparable<AccountantGlRecordSum>, Serializable {
    /**
     * CP_FINEXT.CB_EXT_REF_NO 8 汇总日期
     */
    @FileField(order = 1, length = 50)
    private String sumDate;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 科目号
     */
    @FileField(order = 2, length = 50)
    private String subjectCodeId;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 120 科目描述
     */
    @FileField(order = 3, length = 50)
    private String subjectDesc;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 2 借贷标志
     */
    @FileField(order = 4, length = 50)
    private String debitCreditSide;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 总金额
     */
    @FileField(order = 5, length = 50)
    private BigDecimal totalAmt;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 3 币种
     */
    @FileField(order = 6, length = 50)
    private String currency;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 6 账产品
     */
    @FileField(order = 7, length = 50)
    private String accountProduct;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 模块标志
     */
    @FileField(order = 8, length = 50)
    private String moduleFlag;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 卡组织
     */
    @FileField(order = 9, length = 50)
    private String crdOrganization;

    @Override
    public int compareTo(AccountantGlRecordSum o) {
        return 0;
    }
}
