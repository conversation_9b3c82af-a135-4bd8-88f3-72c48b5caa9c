package com.anytech.anytxn.accounting.batch.job.report.step.amountdetail.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @Date 2020/9/11 11:46
 */
@Getter
@Setter
@ToString
public class ReceivablesSub {
    private List<ReceivablesDetail> detailList;

    private List<ReceivablesSubTotal> subTotalList;

    private boolean end = false;
}
