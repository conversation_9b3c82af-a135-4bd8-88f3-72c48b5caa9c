package com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance;

import com.anytech.anytxn.accounting.base.enums.AccountRepDetailEnum;
import com.anytech.anytxn.accounting.base.exception.AnyTxnAccountantException;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2020/4/3
 */
public class AccountBalanceReader extends JdbcPagingItemReader<AccountBalanceInfoDTO> {

    public AccountBalanceReader(DataSource dataSource) {
        super();
        this.setRowMapper(new BeanPropertyRowMapper<>(AccountBalanceInfoDTO.class));
        this.setQueryProvider(oraclePagingQueryProvider(dataSource));
    }

    private PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();


        providerFactoryBean.setSelectClause("ACCOUNT_MANAGEMENT_ID,TRANSACTION_TYPE_CODE,ORGANIZATION_NUMBER,CURRENCY,INTEREST_INDICATOR,ABS_STATUS,ABS_PRODUCT_CODE,sum(BALANCE) as BALANCE ");
        providerFactoryBean.setFromClause("ACCOUNT_BALANCE_INFO");
        String orgConditionStr = " ORGANIZATION_NUMBER = "+ OrgNumberUtils.getOrg();
        providerFactoryBean.setWhereClause(orgConditionStr);
        providerFactoryBean.setGroupClause("ACCOUNT_MANAGEMENT_ID,TRANSACTION_TYPE_CODE,ORGANIZATION_NUMBER,CURRENCY,INTEREST_INDICATOR,ABS_STATUS,ABS_PRODUCT_CODE");
        providerFactoryBean.setSortKey("ACCOUNT_MANAGEMENT_ID");
        providerFactoryBean.setDataSource(dataSource);
        try {
            return  providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("PagingQueryProvider exception",e);
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.D_DATABASE_FAULT, AccountRepDetailEnum.BATCH_PAGE_QUERY_ERROR);
        }
    }
}
