package com.anytech.anytxn.account.job.paymentfile.step.auto;

import com.anytech.anytxn.account.job.paymentfile.model.AutoPaymentOut;
import com.anytech.anytxn.business.dao.account.mapper.AutoPaymentLogSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AutoPaymentLog;
import org.springframework.batch.item.*;
import org.springframework.batch.item.file.FlatFileItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 输出约定扣款文件
 * TODO 不存在数据时会有空文件产生
 * <AUTHOR>
 * @date 2021/1/7
 */
public class AutoPaymentFileWriter implements ItemWriter<AutoPaymentLog>, ItemStream {

    private FlatFileItemWriter<AutoPaymentOut> delegates;

    public AutoPaymentFileWriter() {
    }

    public AutoPaymentFileWriter(final FlatFileItemWriter<AutoPaymentOut> delegates) {
        this.delegates = delegates;
    }

    @Autowired
    private AutoPaymentLogSelfMapper autoPaymentLogSelfMapper;

    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        if (delegates != null) {
            delegates.open(executionContext);
        }
    }

    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {
        if (delegates != null) {
            delegates.update(executionContext);
        }
    }

    @Override
    public void close() throws ItemStreamException {
        if (delegates != null) {
            delegates.close();
        }
    }

    @Override
    public void write(Chunk<? extends AutoPaymentLog> chunk) throws Exception {
        List<? extends AutoPaymentLog> items = chunk.getItems();
        if (!CollectionUtils.isEmpty(items)) {
            // 文件输出
            List<AutoPaymentOut> autoPaymentOuts = items.stream().map(x-> new AutoPaymentOut().create(x)).collect(Collectors.toList());
            // 将List转换为Chunk
            Chunk<AutoPaymentOut> outChunk = new Chunk<>(autoPaymentOuts);
            delegates.write(outChunk);

            // 更新outFile
            autoPaymentLogSelfMapper.updateFileBatch(items.stream().map(AutoPaymentLog::getAutoPaymentId).collect(Collectors.toList()));
        }
    }
}
