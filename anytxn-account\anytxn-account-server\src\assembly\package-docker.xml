<assembly>
	<id>make-assembly</id>
	<formats>
		<format>tar.gz</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<fileSets>
		<fileSet>
			<directory>${project.basedir}</directory>
			<includes>
				<include>README.md</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>logs</directory>
			<outputDirectory>/logs</outputDirectory>
			<excludes>
				<exclude>**.md</exclude>
				<exclude>/*.log</exclude>
				<exclude>/*.gz</exclude>
				<exclude>/backup/*.log</exclude>
			</excludes>
			<useDefaultExcludes>true</useDefaultExcludes>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/src/main/resources</directory>
			<outputDirectory>config</outputDirectory>
			<includes>
				<include>/**</include>
			</includes>
		</fileSet>
	</fileSets>
	<dependencySets>
		<dependencySet>
			<useProjectArtifact>true</useProjectArtifact>
			<outputDirectory>/lib</outputDirectory>
			<scope>runtime</scope>
		</dependencySet>
	</dependencySets>
</assembly>
