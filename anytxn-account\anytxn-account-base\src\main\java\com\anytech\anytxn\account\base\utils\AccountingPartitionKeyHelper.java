package com.anytech.anytxn.account.base.utils;

/**
 * @description: 生成分区键值.都是对管理账户id(19位)进行取模
 * @author: zhangnan
 * @create: 2020-04-23
 **/
public class AccountingPartitionKeyHelper {

    public static Long getPartitionKeyLong(String source){
        Long partition = Long.valueOf(source) % 8192;
        return partition;
    }

    public static Integer getPartitionKeyInt(String source){
        return getPartitionKeyLong(source).intValue();
    }
}
