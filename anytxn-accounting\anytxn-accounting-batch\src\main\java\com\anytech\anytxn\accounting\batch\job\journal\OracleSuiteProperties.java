package com.anytech.anytxn.accounting.batch.job.journal;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: sukang
 * @Date: 2022/11/22 12:50
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "anytxn.accountant.oracle-api")
public class OracleSuiteProperties {

    String accountId;

    String consumeKey;

    String consumeSecret;

    String tokenId;

    String tokenSecret;

    String companyUrl;

    String restService = "services/rest";

    String version = "1.0";

    String signatureMethod = "HMAC-SHA256";




}
