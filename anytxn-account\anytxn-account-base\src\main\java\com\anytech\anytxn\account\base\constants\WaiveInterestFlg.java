package com.anytech.anytxn.account.base.constants;

/**
 * 停止计息（所有交易类型）
 *
 * <AUTHOR>
 * @date 2018-9-22
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 */
public enum WaiveInterestFlg {

    /**
     * 0=正常计息
     * 1=停止计息（延滞原因，免除已累积利息），
     * 2=停止计息（其他原因，免除累积利息）
     * 3=停止计息（其他原因，正常收取累积利息）
     */
    ZERO("0", "正常计息"),

    ONE("1", "停止计息（延滞原因，免除已累积利息）"),

    TWO("2", "停止计息（其他原因，免除累积利息）"),

    THREE("3", "停止计息（其他原因，正常收取累积利息）");

    private String code;
    private String reason;


    WaiveInterestFlg(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }
}
