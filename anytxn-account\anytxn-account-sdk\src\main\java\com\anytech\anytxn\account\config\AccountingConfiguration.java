package com.anytech.anytxn.account.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;



/**
 * <AUTHOR>
 */
@Configuration
public class AccountingConfiguration {

    /**
     * 由于会计插入流水号没改24位，大量并发会有问题。先选择性不生成
     */
    @Value("${anytxn.accounting.custom.skip-glams:false}")
    private boolean accountingSkipGlams;

    public boolean isAccountingSkipGlams() {
        return accountingSkipGlams;
    }

    public void setAccountingSkipGlams(boolean accountingSkipGlams) {
        this.accountingSkipGlams = accountingSkipGlams;
    }
}
