package com.anytech.anytxn.account.base.constants;

/**
 * 约定扣款类型
 *
 * <AUTHOR>
 * @date 2018-9-22
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 *
 */
public enum AutoPaymentType {

    /**
     * 1：直接借记账户自动扣款
     * 9：购汇扣款
     */

    NOT_SIGN("0", "未签约约定扣款"),

    DIRECT("1", "直接借记账户自动扣款"),

    OTHER("2", "他行借记账户自动扣款"),

    INDIRECT("9", "购汇扣款");

    private String code;
    private String reason;


    AutoPaymentType(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }
}
