package com.anytech.anytxn.account.base.enums;

import com.anytech.anytxn.common.core.constants.RespCodePrefix;
import com.anytech.anytxn.account.base.enums.AccountingRepMessageEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;

/**
 * 响应码枚举类
 * 作为自定义异常对象的入参,为使用自定义异常必须针对异常进系枚举定义.这里的XXX/YYY均需要替换成各个真实的变量.
 * 变量的命名规则:保证简介无歧义,不可过长(>10)也不能太短导致无法辨识含义
 * <AUTHOR>
 *
 */
public enum AnyTxnAccountingRespCodeEnum implements RespCodePrefix {


	REQ_SUCCESS(PREFIX_ACCOUNTING_N , AccountingRepMessageEnum.SUCCESS, ""),

	/**
	 * 参数类型响应码枚举定义
	 */
	P_ERR(PREFIX_ACCOUNTING_P + "10000", AccountingRepMessageEnum.PE, ""),
	P_NOT_NULL(PREFIX_ACCOUNTING_P + "10001", AccountingRepMessageEnum.PCE, ""),

	P_TYPE_FAULT(PREFIX_ACCOUNTING_P + "10002", AccountingRepMessageEnum.PTW, ""),
	P_VALUE_FAULT(PREFIX_ACCOUNTING_P + "10003", AccountingRepMessageEnum.AP_VR, ""),


	/**
	 * 数据类型响应码枚举定义
	 */
	D_ERR(PREFIX_ACCOUNTING_D + "20000", AccountingRepMessageEnum.AD, ""),

	//数据相关
	D_DUPLICATEKEY(PREFIX_ACCOUNTING_D + "20001", AccountingRepMessageEnum.VUC, ""),
	D_NOT_EXIST(PREFIX_ACCOUNTING_D + "20002", AccountingRepMessageEnum.DE, ""),
	D_STATUS_FAULT(PREFIX_ACCOUNTING_D + "20003", AccountingRepMessageEnum.DSE, ""),
	D_UPDATE_FAIL(PREFIX_ACCOUNTING_D + "20004", AccountingRepMessageEnum.D_UI_E, ""),
	D_TAB1_DEL_INUSER(PREFIX_ACCOUNTING_D + "20005", AccountingRepMessageEnum.DDU, ""),
	D_COL_VALUE_ILLEGAL(PREFIX_ACCOUNTING_D + "20006", AccountingRepMessageEnum.FA_RR, ""),
	D_COL_NOT_NULL(PREFIX_ACCOUNTING_D + "20007", AccountingRepMessageEnum.FA, ""),
	D_RESULT_MULTIPLE(PREFIX_ACCOUNTING_D + "20008", AccountingRepMessageEnum.Q_MR, ""),
	D_INSERT_FAIL(PREFIX_ACCOUNTING_D + "20009", AccountingRepMessageEnum.I0, ""),

	//数据库相关
	D_DATABASE_ERROR(PREFIX_ACCOUNTING_D + "21000", AccountingRepMessageEnum.DBE, ""),
    D_OPT_LOCK_CONFLICT(PREFIX_ACCOUNTING_D + "21001", AccountingRepMessageEnum.OLC, ""),


	//配置相关
	D_MYBATIS_FAIL(PREFIX_ACCOUNTING_D + "22000", AccountingRepMessageEnum.CM, ""),
	D_CONFIG_NOT_EXIST(PREFIX_ACCOUNTING_D + "22001", AccountingRepMessageEnum.CE, ""),
	D_CONFIG_STATUS_FAULT(PREFIX_ACCOUNTING_D + "22002", AccountingRepMessageEnum.CSE, ""),
	D_CONFIG_UPDATE_FAIL(PREFIX_ACCOUNTING_D + "22003", AccountingRepMessageEnum.CUE, ""),
	D_CONFIG_DEL_INUSER(PREFIX_ACCOUNTING_D + "22004", AccountingRepMessageEnum.CDU, ""),
	D_CONFIG_COL_VALUE_ILLEGAL(PREFIX_ACCOUNTING_D + "22005", AccountingRepMessageEnum.ACF, ""),
	D_CONFIG_COL_NOT_NULL(PREFIX_ACCOUNTING_D + "22006", AccountingRepMessageEnum.CFA, ""),
	S_ACCOUNTING_RULE_MATCH_FAILE("22007", AccountingRepMessageEnum.RU_MA, ""),
	D_BLK_FAIL("22008", AccountingRepMessageEnum.BLK_FAIL, ""),
	D_UN_BLK_FAIL("22009", AccountingRepMessageEnum.UN_BLK_FAIL, ""),

	UNKONWN_ERR(MAX_RESPONSE_CODE, AccountingRepMessageEnum.UE, ""),
	S_SERVER_NO_INSTANCE(PREFIX_CARD_S + "20010", AccountingRepMessageEnum.SOURCE, "");

	/**
	 * 响应码编码,格式为 3位业务编码+2位响应码类型编码+5位具体响应码
	 */
	private String code;
	/**
	 * 中文通用描述
	 */
	private AccountingRepMessageEnum msg;
	/**
	 * 需要提供具体数据时使用.
	 * 方便后续分析查看.
	 * 可以在抛出异常处通过对此属性进行赋值记录现场
	 */
	private String detail;

	AnyTxnAccountingRespCodeEnum(String code, AccountingRepMessageEnum msg,  String detail) {
		this.code = code;
		this.msg = msg;
		this.detail = detail;
	}

	@Override
	public String getCode() {
		return code;
	}
	@Override
	public String getMsg() {
		return msg.message();
	}
	@Override
	public String getDetail() {
		return detail;
	}

}
