package com.anytech.anytxn.accounting.batch.job.journal;

import com.google.common.collect.ImmutableMap;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.web.reactive.function.client.WebClient;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;


/**
 * @Author: sukang
 * @Date: 2022/11/22 13:44
 */
public class Oauth1 implements Signature {

    @Override
    public void signature(SignatureDTO signatureDTO, OracleSuiteProperties oracleSuiteProperties,
                          WebClient.RequestHeadersSpec<?> requestHeadersSpec) throws Exception {
        String baseString = getBaseString(signatureDTO, oracleSuiteProperties);



        String signature = getSignature(oracleSuiteProperties, baseString);

        String authorizedHeader =  getAuthorizationHeader(signatureDTO, signature, oracleSuiteProperties);

        requestHeadersSpec.header("Authorization", authorizedHeader);
    }

    private String getAuthorizationHeader(SignatureDTO signatureDTO,
                                          String signature,
                                          OracleSuiteProperties oracleSuiteProperties) {

        ImmutableMap<String, String> immutableMap = ImmutableMap.<String,String>builder()
                .put("realm", "\"" + oracleSuiteProperties.getAccountId() + "\"" + ",")
                .put("oauth_consumer_key", "\"" + oracleSuiteProperties.getConsumeKey() + "\"" + ",")
                .put("oauth_token", "\"" + oracleSuiteProperties.getTokenId() + "\"" + ",")
                .put("oauth_signature_method", "\"" + oracleSuiteProperties.getSignatureMethod() + "\"" + ",")
                .put("oauth_timestamp", "\"" + signatureDTO.getTimestamp() + "\"" + ",")
                .put("oauth_nonce", "\"" + signatureDTO.getNonce() + "\"" + ",")
                .put("oauth_version", "\"" + oracleSuiteProperties.getVersion() + "\"" + ",")
                .put("oauth_signature", "\"" + signature + "\"").build();
        StringBuilder stringBuilder = new StringBuilder("OAuth ");

        immutableMap.forEach( (key,value) -> {
            stringBuilder.append(key).append("=").append(value);
        });

        return stringBuilder.toString();
    }




    private String getSignature(OracleSuiteProperties oracleSuiteProperties,String baseString) throws Exception {
        Mac sha256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec((oracleSuiteProperties.getConsumeSecret()
                + "&" + oracleSuiteProperties.getTokenSecret())
                .getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256.init(secretKey);
        byte[] hash = sha256.doFinal(baseString.getBytes(StandardCharsets.UTF_8));
        return  URLEncoder.encode(Base64.encodeBase64String(hash),"utf-8");
    }


    private String getBaseString(SignatureDTO signatureDTO, OracleSuiteProperties oracleSuiteProperties) throws UnsupportedEncodingException {

        String urlBase = signatureDTO.getHttpMethod() + "&"
                + URLEncoder.encode(signatureDTO.getUrl(), "utf-8") + "&";


        String timestamp = String.valueOf(System.currentTimeMillis());
        timestamp = timestamp.substring(0,10);
        signatureDTO.setTimestamp(timestamp);

        String generateNonce = RandomStringUtils.randomAlphanumeric(10);
        signatureDTO.setNonce(generateNonce);

        String stringBuilder = "oauth_consumer_key=" + oracleSuiteProperties.getConsumeKey() +
                "&oauth_nonce=" + generateNonce +
                "&oauth_signature_method=" + "HMAC-SHA256" +
                "&oauth_timestamp=" + timestamp +
                "&oauth_token=" + oracleSuiteProperties.getTokenId() +
                "&oauth_version=" + oracleSuiteProperties.getVersion();
        return urlBase + URLEncoder.encode(stringBuilder, "utf-8");

    }

}
