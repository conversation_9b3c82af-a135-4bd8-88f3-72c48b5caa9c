package com.anytech.anytxn.account.job.incollection.step;

import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
/**
 *
 * <AUTHOR>
 * @date 2019-09-02
 **/
public class IncollectionReader extends JdbcPagingItemReader<AccountManagementInfo> {

    public IncollectionReader(DataSource dataSource) {
        super();
        this.setRowMapper(new BeanPropertyRowMapper<>(AccountManagementInfo.class));
        this.setQueryProvider(IncollectionReader.oraclePagingQueryProvider(dataSource));
    }

    private static PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean provider = new SqlPagingQueryProviderFactoryBean();

        // 获得入口数据
        provider.setSelectClause("ACCOUNT_MANAGEMENT_ID,CUSTOMER_ID,TOTAL_DUE_AMOUNT,IN_COLLECTION_INDICATOR");
        provider.setFromClause("ACCOUNT_MANAGEMENT_INFO");

        // 拼接sql,(卡片基本信息表.NEXT_ANNUAL_FEE_DATE <= 机构表.ACCRUED_THRU_DAY).
        String orgConditionStr = " and ORGANIZATION_NUMBER = "+ OrgNumberUtils.getOrg();
        provider.setWhereClause("LAST_AGED_DATE is not null" + orgConditionStr);

        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("ACCOUNT_MANAGEMENT_ID", Order.ASCENDING);
        provider.setSortKeys(sortKey);
        provider.setDataSource(dataSource);
        try {
            return provider.getObject();
        } catch (Exception e) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.UNKONWN_ERR);
        }
    }
}
