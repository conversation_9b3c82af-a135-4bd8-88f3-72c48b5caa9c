package com.anytech.anytxn.accounting.batch.job.glamsfromupi.dto;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
public interface UPIMessage extends Serializable {
    static <T extends UPIMessage> T parse(String line, Class<T> tClazz) {
        try {
            T t = tClazz.newInstance();
            Class<?> clazz = tClazz;
            TreeMap<Integer, Field> treeMap = new TreeMap<>();
            int count = 0;
            while (clazz != null) {
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    Order order = field.getAnnotation(Order.class);
                    if (order == null) {
                        continue;
                    }
                    int orderValue = order.value();
                    treeMap.put(orderValue, field);
                    count++;
                }
                clazz = clazz.getSuperclass();
            }
            if (treeMap.size() != count) {
                throw new RuntimeException(String.format("Order值有重复，请检查Class[%s]", tClazz.getName()));
            }
            int sum = 0;
            for (Integer orderValue : treeMap.keySet()) {
                sum += orderValue;
            }
            int sn = treeMap.lastEntry().getKey() * treeMap.size() >>> 1;
            if (sum != sn) {
                throw new RuntimeException(String.format("Order值非从0开始的等差数列，请检查Class[%s]", tClazz.getName()));
            }
            int index = 0;
            for (Map.Entry<Integer, Field> entry : treeMap.entrySet()) {
                Field field = entry.getValue();
                Size size = field.getAnnotation(Size.class);
                if (size == null) {
                    throw new RuntimeException("字段长度未配置: " + field.getName());
                }
                final int max = size.max();
                final int nextIndex = index + max;
                String value;
                if (index <= line.length() && nextIndex > line.length()) {
                    value = line.substring(index);
                } else {
                    value = line.substring(index, nextIndex);
                }
                ///System.out.println(size + ": " + field.getName() + ": " + value);
                field.setAccessible(true);
                Class<?> fieldType = field.getType();
                Object objValue;
                if (String.class == fieldType) {
                    objValue = value.trim();
                } else {
                    objValue = StringUtils.isBlank(value) ? null : fieldType.getConstructor(String.class).newInstance(value.trim());
                }
                field.set(t, objValue);
                index = nextIndex;
            }
            return t;
        } catch (Exception e) {
            throw new RuntimeException(String.format("格式转换时抛出异常: %s", line), e);
        }
    }
}
