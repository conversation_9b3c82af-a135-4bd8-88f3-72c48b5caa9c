<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.anytech</groupId>
        <artifactId>anytxn-parent</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>anytxn-account</artifactId>
    <name>anytxn-account</name>
    <description>信用卡核心系统-账户模块</description>
    <packaging>pom</packaging>

    <modules>
        <module>anytxn-account-base</module>
        <module>anytxn-account-client</module>
        <module>anytxn-account-sdk</module>
        <module>anytxn-account-server</module>
        <module>anytxn-account-batch</module>
    </modules>

<!--    &lt;!&ndash; 公共属性 &ndash;&gt;-->
<!--    <properties>-->

<!--        &lt;!&ndash;anytxn 快照版本管理&ndash;&gt;-->
<!--&lt;!&ndash;        <anytxn.version>${project.version}</anytxn.version>&ndash;&gt;-->

<!--&lt;!&ndash;        <anytxn-parameter.version>${anytxn.version}</anytxn-parameter.version>&ndash;&gt;-->
<!--&lt;!&ndash;        <anytxn-bis-core.version>${anytxn.version}</anytxn-bis-core.version>&ndash;&gt;-->
<!--&lt;!&ndash;        <anytxn-biz-common.version>1.1.0</anytxn-biz-common.version>&ndash;&gt;-->
<!--&lt;!&ndash;        <anytxn-rule.version>1.0.0</anytxn-rule.version>&ndash;&gt;-->
<!--&lt;!&ndash;        <anytxn-transaction.version>${anytxn.version}</anytxn-transaction.version>&ndash;&gt;-->
<!--&lt;!&ndash;        <anytxn-accounting-base.version>${anytxn.version}</anytxn-accounting-base.version>&ndash;&gt;-->
<!--&lt;!&ndash;        <anytxn-number.version>1.0.0</anytxn-number.version>&ndash;&gt;-->


<!--        &lt;!&ndash;稳定版本号管理&ndash;&gt;-->

<!--&lt;!&ndash;        <anytxn-batch-sdk.version>2.0.0</anytxn-batch-sdk.version>&ndash;&gt;-->
<!--        <nacos.version>2.2.0.RELEASE</nacos.version>-->
<!--        <jasypt.version>2.1.2</jasypt.version>-->
<!--        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>-->
<!--&lt;!&ndash;        <spring.boot.version>2.0.5.RELEASE</spring.boot.version>&ndash;&gt;-->
<!--&lt;!&ndash;        <lombok.version>1.18.10</lombok.version>&ndash;&gt;-->
<!--        <joda.time.version>2.9.9</joda.time.version>-->
<!--&lt;!&ndash;        <spring-boot-maven-plugin.version>2.0.0.M2</spring-boot-maven-plugin.version>&ndash;&gt;-->
<!--        <jackson.version>2.9.0.pr2</jackson.version>-->
<!--        <io.springfox.version>2.9.2</io.springfox.version>-->
<!--        <spring.boot.version>3.4.2</spring.boot.version> &lt;!&ndash; 或最新版本 &ndash;&gt;-->

<!--        <java.version>17</java.version>-->


<!--        &lt;!&ndash; docker &ndash;&gt;-->
<!--        <fabric8.maven.plugin.version>0.35.0</fabric8.maven.plugin.version>-->
<!--&lt;!&ndash;        <anytxn-batch-sdk.version>2.2.0</anytxn-batch-sdk.version>&ndash;&gt;-->
<!--        <harbor.auth.user>admin</harbor.auth.user>-->
<!--        <harbor.auth.passd>Harbor12345</harbor.auth.passd>-->
<!--        <harbor.registry>k8s.jrx.com</harbor.registry>-->
<!--        &lt;!&ndash;<harbor.auth.user>admin</harbor.auth.user>-->
<!--        <harbor.auth.passd>Changeme_123</harbor.auth.passd>-->
<!--        <harbor.registry>harbor.jiangrongxin.com</harbor.registry>&ndash;&gt;-->
<!--    </properties>-->

<!--    &lt;!&ndash; 会被子模块直接继承 &ndash;&gt;-->
<!--    <dependencyManagement>-->
<!--        <dependencies>-->

<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-bis-core</artifactId>-->
<!--                <version>${anytxn.bis.core.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-accounting-base</artifactId>-->
<!--                <version>${anytxn.accounting.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-accounting-sdk</artifactId>-->
<!--                <version>${anytxn.accounting.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-bis-core-sdk</artifactId>-->
<!--                <version>${anytxn.bis.core.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-bis-core-dao</artifactId>-->
<!--                <version>${anytxn.bis.core.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-common-core</artifactId>-->
<!--                <version>${anytxn.common.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-number</artifactId>-->
<!--                <version>${anytxn.number.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-parameter-parent</artifactId>-->
<!--                <version>${anytxn.parameter.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-transaction-parent</artifactId>-->
<!--                <version>${anytxn.transaction.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->


<!--            <dependency>-->
<!--                <groupId>com.jrx.anytech</groupId>-->
<!--                <artifactId>anytxn-rule-parent</artifactId>-->
<!--                <version>${anytxn.rule.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-starter-test</artifactId>-->
<!--                <version>${spring.boot.version}</version>-->
<!--            </dependency>-->
<!--            &lt;!&ndash; 开发工具 热部署 修改代码不用重启 设置在Intellij->Preferences->Build,Excution,Deployment->Compile->make project automatically. ctrl+shift+F9 mark&ndash;&gt;-->
<!--            <dependency>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-devtools</artifactId>-->
<!--                <version>3.4.2</version>  &lt;!&ndash; 替换为实际使用的Spring Boot版本 &ndash;&gt;-->
<!--                <optional>true</optional>-->
<!--                <scope>provided</scope>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>joda-time</groupId>-->
<!--                <artifactId>joda-time</artifactId>-->
<!--                <version>${joda.time.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>jrx.anyscheduler</groupId>-->
<!--                <artifactId>anyscheduler-batch-sdk</artifactId>-->
<!--                <version>${anytxn.batch.sdk.version}</version>-->
<!--            </dependency>-->

<!--        </dependencies>-->
<!--    </dependencyManagement>-->

<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <source>${java.version}</source>-->
<!--                    <target>${java.version}</target>-->
<!--                    <compilerArgs>-->
<!--                        <arg>&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>-->
<!--                        <arg>&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>-->
<!--                        <arg>&#45;&#45;add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>-->
<!--                    </compilerArgs>-->
<!--                    <annotationProcessorPaths>-->
<!--                        &lt;!&ndash; 确保注解处理器路径正确 &ndash;&gt;-->
<!--                        <path>-->
<!--                            <groupId>org.projectlombok</groupId>-->
<!--                            <artifactId>lombok</artifactId>-->
<!--                            <version>1.18.30</version>-->
<!--                        </path>-->
<!--                    </annotationProcessorPaths>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-surefire-plugin</artifactId>-->
<!--                <version>2.18.1</version>-->
<!--                <configuration>-->
<!--                    <skipTests>true</skipTests>-->
<!--                </configuration>-->
<!--            </plugin>-->

<!--            <plugin>-->
<!--                <groupId>org.codehaus.mojo</groupId>-->
<!--                <artifactId>versions-maven-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <generateBackupPoms>false</generateBackupPoms>-->
<!--                </configuration>-->
<!--            </plugin>-->

<!--        </plugins>-->
<!--    </build>-->


</project>
