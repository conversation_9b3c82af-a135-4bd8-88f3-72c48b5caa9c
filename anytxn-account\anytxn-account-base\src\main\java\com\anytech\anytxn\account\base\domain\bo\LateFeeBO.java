package com.anytech.anytxn.account.base.domain.bo;

import com.anytech.anytxn.common.core.base.BaseEntity;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import com.anytech.anytxn.account.base.enums.RecordedEnum;

/**
 * <AUTHOR>
 * @date 2020-03-24
 */
public class LateFeeBO extends BaseEntity {
    private RecordedEnum recorded;
    private CustomerBasicInfo customerBasicInfo;

    public RecordedEnum getRecorded() {
        return recorded;
    }

    public void setRecorded(RecordedEnum recorded) {
        this.recorded = recorded;
    }

    public CustomerBasicInfo getCustomerBasicInfo() {
        return customerBasicInfo;
    }

    public void setCustomerBasicInfo(CustomerBasicInfo customerBasicInfo) {
        this.customerBasicInfo = customerBasicInfo;
    }
}
