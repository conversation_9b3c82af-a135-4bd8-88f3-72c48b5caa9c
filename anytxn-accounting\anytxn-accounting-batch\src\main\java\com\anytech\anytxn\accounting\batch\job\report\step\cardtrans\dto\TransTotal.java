package com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.dto;

import com.anytech.anytxn.accounting.base.enums.ChannelEnum;
import com.anytech.anytxn.accounting.base.enums.CurrencyCodeEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.model.CardTrans;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/10/15 18:03
 */
@Getter
@Setter
public class TransTotal {

    private String currencyType;


    //只给一个TransTotalDetailBase
    private List<TransTotalDetailBase> transTotalDetailBaseList;

    //汇总
    private List<TransTotalTotal> transTotalTotalList;


    private List<TransTotalVisaDetail> transTotalVisaDetailList;

    private String  visaCount;
    private String  visaAmount;
    private String  visaTotal;

    public static List<TransTotal> resetReportData(List<CardTrans> data){
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }
        TransTotal result = new TransTotal();
        result.setCurrencyType("156");

        // 明细
        List<TransTotalDetail> details = Lists.newArrayList();

        // 汇总
        List<TransTotalTotal> totals = Lists.newArrayList();
        result.setTransTotalTotalList(totals);

        // 基于币种
        Map<String, List<CardTrans>> group = data.stream().filter(x-> x.getCurrencyCode() != null).collect(Collectors.groupingBy(x-> x.getCurrencyCode() + x.getTransactionCode()));
        for (Map.Entry<String, List<CardTrans>> entry  : group.entrySet()) {
            List<CardTrans> vs = entry.getValue();

            // 外层分组
            TransTotalDetail de = new TransTotalDetail();
            // 币种
            de.setCurrency(vs.get(0).getCurrencyCode());

            // 基于交易码分组
            Map<String, List<CardTrans>> group2 = vs.stream().filter(x-> x.getTransactionCode() != null).collect(Collectors.groupingBy(CardTrans::getTransactionCode));
            // 明细列表
            List<TransTotalDetailSubDetail> sub = Lists.newArrayList();
            de.setTransTotalDetailSubDetailList(sub);
            // 小计列表
            List<TransTotalDetailSub> sub2 = Lists.newArrayList();
            de.setTransTotalDetailSubList(sub2);

            // 遍历内层分组
            for (Map.Entry<String, List<CardTrans>> entry2 : group2.entrySet()) {

                List<CardTrans> vs2 = entry2.getValue();

                // 贷记卡汇总
                List<CardTrans> c1 = vs2.stream().filter(x -> x.getCardType().equals("贷记卡")).collect(Collectors.toList());
                // 准贷记卡汇总
                List<CardTrans> c2 = vs2.stream().filter(x -> x.getCardType().equals("准贷记卡")).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(c1)) {
                    TransTotalDetailSubDetail d1 = new TransTotalDetailSubDetail();
                    d1.setTransCode(c1.get(0).getTransactionCode());
                    d1.setCredDesc(c1.get(0).getDescription());
                    d1.setCardType(c1.get(0).getCardType());
                    // 币种描述
                    String currencyDes = CurrencyCodeEnum.getDes(c1.get(0).getCurrencyCode());
                    // 贷记卡金额汇总
                    BigDecimal decimal1 = c1.stream().map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    d1.setSettlementDesc(currencyDes + " " + decimal1);
                    d1.setTransSource(c1.get(0).getTransactionSource());
                    d1.setTransCount(String.valueOf(c1.size()));
                    // 币种描述
//                    String currencyDes2 = CurrencyCode.getDes(c1.get(0).getSettlementCurrency());
                    // 贷记卡清算金额汇总
                    BigDecimal decimal2 = c1.stream().map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    d1.setClearAmountDesc(currencyDes + " " + decimal2);
                    d1.setTransResult(c1.get(0).getResult());
                    d1.setDebitSubject(c1.get(0).getCglAcct());
                    d1.setCredSubject(c1.get(0).getDglAcct());
                    sub.add(d1);
                }

                if (CollectionUtils.isNotEmpty(c2)) {
                    TransTotalDetailSubDetail d1 = new TransTotalDetailSubDetail();
                    d1.setTransCode(c2.get(0).getTransactionCode());
                    d1.setCredDesc(c2.get(0).getDescription());
                    d1.setCardType(c2.get(0).getCardType());
                    // 币种描述
                    String currencyDes = CurrencyCodeEnum.getDes(c2.get(0).getCurrencyCode());
                    // 准贷记卡金额汇总
                    BigDecimal decimal1 = c2.stream().map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    d1.setSettlementDesc(currencyDes + " " + decimal1);
                    d1.setTransSource(c2.get(0).getTransactionSource());
                    d1.setTransCount(String.valueOf(c2.size()));
                    // 币种描述
//                    String currencyDes2 = CurrencyCode.getDes(c2.get(0).getSettlementCurrency());
                    // 准贷记卡清算金额汇总
                    BigDecimal decimal2 = c2.stream().map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    d1.setClearAmountDesc(currencyDes + " " + decimal2);
                    d1.setTransResult(c2.get(0).getResult());
                    d1.setDebitSubject(c2.get(0).getCglAcct());
                    d1.setCredSubject(c2.get(0).getDglAcct());
                    sub.add(d1);
                }

                TransTotalDetailSub s1 = new TransTotalDetailSub();
                if (CollectionUtils.isNotEmpty(c1)) {
                    // 币种描述
                    String currencyDes = CurrencyCodeEnum.getDes(c1.get(0).getCurrencyCode());
                    // 准贷记卡金额汇总
                    BigDecimal decimal1 = c1.stream().map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    s1.setTransCode(c1.get(0).getTransactionCode());
                    s1.setCredCardSubAmountDesc("贷记卡 " + currencyDes + " " + decimal1 + " " + c1.size());
                } else if (CollectionUtils.isNotEmpty(c2)){
                    s1.setTransCode(c2.get(0).getTransactionCode());
                    // 币种描述
                    String currencyDes = CurrencyCodeEnum.getDes(c2.get(0).getCurrencyCode());
                    s1.setCredCardSubAmountDesc("贷记卡 " +  currencyDes + " 0.00 0");
                }

                TransTotalDetailSub s2 = new TransTotalDetailSub();
                if (CollectionUtils.isNotEmpty(c2)) {
                    // 币种描述
                    String currencyDes = CurrencyCodeEnum.getDes(c2.get(0).getCurrencyCode());
                    // 准贷记卡金额汇总
                    BigDecimal decimal1 = c2.stream().map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    s2.setTransCode(c2.get(0).getTransactionCode());
                    s2.setCredCardSubAmountDesc("准贷记卡 " + currencyDes + " " + decimal1 + " " + c2.size());
                } else if (CollectionUtils.isNotEmpty(c1)){
                    s2.setTransCode(c1.get(0).getTransactionCode());
                    // 币种描述
                    String currencyDes = CurrencyCodeEnum.getDes(c1.get(0).getCurrencyCode());
                    s2.setCredCardSubAmountDesc("准贷记卡 " +  currencyDes + " 0.00 0");
                }

                // 有一方存在再保存
                if (CollectionUtils.isNotEmpty(c1) || CollectionUtils.isNotEmpty(c2)) {
                    sub2.add(s1);
                    sub2.add(s2);
                }
            }

            sub.sort(Comparator.comparing(TransTotalDetailSubDetail::getCardType));
            Collections.reverse(sub);

            details.add(de);
        }

        // 合计
        Map<String, List<CardTrans>> groupx = data.stream().collect(Collectors.groupingBy(CardTrans::getCurrencyCode));
        for (Map.Entry<String, List<CardTrans>> entry : groupx.entrySet()) {
            // 基于借贷分组
            Map<Integer, List<CardTrans>> group4 = entry.getValue().stream().collect(Collectors.groupingBy(x -> {
                if (StringUtils.equalsAny(x.getTransactionCode(), "30100", "40100")) {
                    return 1;
                } else {
                    return 2;
                }
            }));

            // 借方处理
            List<CardTrans> cardTrans = group4.get(1);
            // 贷方处理
            List<CardTrans> cardTrans1 = group4.get(2);
            // 币种
            String currencyDes0 = CurrencyCodeEnum.getDes(entry.getKey());
            // 贷记卡借
            long c1;
            BigDecimal d1;
            // 准贷记卡借
            long c2;
            BigDecimal d2;
            if (CollectionUtils.isEmpty(cardTrans)){
                c1 = 0;
                c2 = 0;
                d1 = BigDecimal.ZERO;
                d2 = BigDecimal.ZERO;
            } else {
                c1 = cardTrans.stream().filter(x -> x.getCardType().equals("贷记卡")).count();
                d1 = cardTrans.stream().filter(x -> x.getCardType().equals("贷记卡")).map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                c2 = cardTrans.stream().filter(x -> x.getCardType().equals("准贷记卡")).count();
                d2 = cardTrans.stream().filter(x -> x.getCardType().equals("准贷记卡")).map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }

            // 贷记卡贷
            long c3;
            BigDecimal d3;
            // 准贷记卡贷
            long c4;
            BigDecimal d4;
            if (CollectionUtils.isEmpty(cardTrans1)){
                c3 = 0;
                c4 = 0;
                d3 = BigDecimal.ZERO;
                d4 = BigDecimal.ZERO;
            } else {
                c3 = cardTrans1.stream().filter(x -> x.getCardType().equals("贷记卡")).count();
                d3 = cardTrans1.stream().filter(x -> x.getCardType().equals("贷记卡")).map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                c4 = cardTrans1.stream().filter(x -> x.getCardType().equals("准贷记卡")).count();
                d4 = cardTrans1.stream().filter(x -> x.getCardType().equals("准贷记卡")).map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }

            TransTotalTotal t = new TransTotalTotal();
            t.setDebitSettleAmountDesc(currencyDes0 + " " + d3);
            t.setCredSettleAmountDesc(currencyDes0 + " " + d1);
            t.setDebitTransCount(String.valueOf(c3));
            t.setCreditTransCount(String.valueOf(c1));
            t.setQuaDebitSettleAmountDesc(currencyDes0 + " " + d4);
            t.setQuaCredSettleAmountDesc(currencyDes0 + " " + d2);
            t.setQuaDebitTransCount(String.valueOf(c4));
            t.setQuaCreditTransCount(String.valueOf(c2));
            t.setTotalDebitSettleAmountDesc(currencyDes0 + " " + d3.add(d4));
            t.setTotalCredSettleAmountDesc(currencyDes0 + " " + d1.add(d2));
            t.setTotalSettleAmountDesc(currencyDes0 + " " + (d3.add(d4)).subtract(d1.add(d2)));
            t.setTotalDebitTransCount(String.valueOf(CollectionUtils.isEmpty(cardTrans1) ? 0 : cardTrans1.size()));
            t.setTotalCreditTransCount(String.valueOf(CollectionUtils.isEmpty(cardTrans) ? 0 : cardTrans.size()));
            t.setTotalTransCount(String.valueOf(groupx.size()));

            totals.add(t);
        }


        List<TransTotalDetailBase> detailBase = Lists.newArrayList();
        detailBase.add(new TransTotalDetailBase(details));
        result.setTransTotalDetailBaseList(detailBase);


        List<CardTrans> data2 = data.stream().filter(x -> x.getTransactionSource() != null && x.getTransactionSource().equals(ChannelEnum.L.getDes())).collect(Collectors.toList());
        result.setVisaTotal(String.valueOf(data2.size()));
        result.setVisaCount(String.valueOf(data2.size()));
        result.setVisaAmount("" + data2.stream().map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

        List<TransTotalVisaDetail> visaDetails = Lists.newArrayList();
        result.setTransTotalVisaDetailList(visaDetails);

        Map<String, List<CardTrans>> group3 = data2.stream().collect(Collectors.groupingBy(CardTrans::getCurrencyCode));

        for (Map.Entry<String, List<CardTrans>> enrty : group3.entrySet()) {

            List<CardTrans> value = enrty.getValue();

            TransTotalVisaDetail d = new TransTotalVisaDetail();
            visaDetails.add(d);

            d.setTransSource(ChannelEnum.V.getDes());
            d.setTransCount(value.size());
            d.setTransCurrency(enrty.getKey());
            d.setPostingAmount(value.stream().map(CardTrans::getTransactionAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            d.setPostingCurrency("156");
            d.setClearAmountDesc("" + CurrencyCodeEnum.getRel(enrty.getKey(), d.getPostingAmount()));
            d.setQuotePrice(CurrencyCodeEnum.getRat(enrty.getKey()));
            d.setWoff("D");
            d.setCode("C");
        }


        return Lists.newArrayList(result);
    }

}
