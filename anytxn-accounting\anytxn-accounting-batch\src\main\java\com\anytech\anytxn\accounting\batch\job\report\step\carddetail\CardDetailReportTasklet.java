package com.anytech.anytxn.accounting.batch.job.report.step.carddetail;

import com.anytech.anytxn.accounting.base.enums.ChannelEnum;
import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmProductInfoSelfMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.config.CardDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportListTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.carddetail.dto.CardDetailReport;
import com.anytech.anytxn.accounting.batch.job.report.step.carddetail.model.CardDetail;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlacgn;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发卡收入明细（日/月/季/年）
 * <AUTHOR>
 * @date 2020/9/14
 */
// TODO 有点慢
public class CardDetailReportTasklet extends AbstractReportListTasklet {

    private TimeTypeEnum timeTypeEnum;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private TPmsGlacgnSelfMapper tPmsGlacgnSelfMapper;
    @Autowired
    private AccountantGlvcherSelfMapper accountantGlvcherSelfMapper;
    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper;
    @Autowired
    private ParmProductInfoSelfMapper parmProductInfoSelfMapper;
    @Autowired
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;

    public CardDetailReportTasklet(BaseConfigurer baseConfigurer, TimeTypeEnum timeTypeEnum) {
        super(baseConfigurer);
        this.timeTypeEnum = timeTypeEnum;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        if (reportConfigurer.isAbleReport()) {
            List<AccountantGlvcher> glvcherList = loadGlvcherModels();

            List<CardDetail> data = modelToReport(glvcherList);
            out(data);
        }

        return RepeatStatus.FINISHED;
    }

    /**
     * 当前日期损益类传票（已处理）
     * @return
     */
    private List<AccountantGlvcher> loadGlvcherModels(){
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        List<AccountantGlvcher> result = null;
        switch (timeTypeEnum) {
            case D:
//                organizationInfo.setToday(LocalDate.of(2020,5,12));
                result = accountantGlvcherSelfMapper.selectByPostDateAndR2(organizationInfo.getOrganizationNumber(), organizationInfo.getToday());
                break;
            case M:
//                organizationInfo.setToday(LocalDate.of(2020,5,31));
                if (TimeUtils.isEndMonth(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.mothRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndR2(organizationInfo.getOrganizationNumber(),mothRand.getKey(), mothRand.getValue());
                }
                break;
            case Q:
//                organizationInfo.setToday(LocalDate.of(2020,6,30));
                if (TimeUtils.isEndQuarter(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.quarterRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndR2(organizationInfo.getOrganizationNumber(),mothRand.getKey(), mothRand.getValue());
                }
                break;
            case Y:
//                organizationInfo.setToday(LocalDate.of(2020,12,31));
                if (TimeUtils.isEndYear(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> yearRand = TimeUtils.yearRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndR2(organizationInfo.getOrganizationNumber(),yearRand.getKey(), yearRand.getValue());
                }
                break;
            default:
                break;
        }

        if (CollectionUtils.isNotEmpty(result)) {
            // 损益类
            List<TPmsGlacgn> pmsGlacgns = tPmsGlacgnSelfMapper.findByGlClass(Lists.newArrayList("PROF"),OrgNumberUtils.getOrg());
            Set<String> glAcct = pmsGlacgns.stream().map(TPmsGlacgn::getGlAcct).collect(Collectors.toSet());

            result = result.stream().filter(x-> glAcct.contains(x.getGlAcct())).collect(Collectors.toList());
        }

        return result;
    }

    /**
     * 业务数据转报表数据
     */
    private List<CardDetail> modelToReport(List<AccountantGlvcher> accountantGlvchers){
        List<CardDetail> result = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(accountantGlvchers)) {
            Map<String, List<PostedTransaction>> pCache = Maps.newHashMap();
            Map<String, CardAuthorizationInfo> cCache = Maps.newHashMap();

            // 先根据交易流水分组
            Map<String, List<AccountantGlvcher>> group = accountantGlvchers.stream().collect(Collectors.groupingBy(x -> x.getGlobalFlowNo() + x.getTxnCodeOrig() + x.getCurrCode() + x.getGlAcct() + x.getDrcr()));
            List<CardDetail> cache = Lists.newArrayList();

            for (Map.Entry<String, List<AccountantGlvcher>> entry : group.entrySet()) {
                List<AccountantGlvcher> value = entry.getValue();
                AccountantGlvcher temple = value.get(0);

                CardDetail detail = new CardDetail();

                try {
                    List<PostedTransaction> transactionList;
                    if (pCache.containsKey(temple.getGlobalFlowNo())) {
                        transactionList = pCache.get(temple.getGlobalFlowNo());
                    } else {
                        transactionList = postedTransactionSelfMapper.selectCardByGlobalNumber(temple.getGlobalFlowNo());
                        pCache.put(temple.getGlobalFlowNo(), transactionList);
                    }

                    String cardNumber = CollectionUtils.isNotEmpty(transactionList) ? transactionList.get(0).getCardNumber() : "";
                    if (StringUtils.isNotEmpty(cardNumber)) {
                        CardAuthorizationInfo cardAuthorizationInfo;
                        if (cCache.containsKey(cardNumber)) {
                            cardAuthorizationInfo = cCache.get(cardNumber);
                        } else {
                            cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNumber,OrgNumberUtils.getOrg());
                            cCache.put(cardNumber, cardAuthorizationInfo);
                        }

                        ParmCardProductInfo cardProductInfo = parmCardProductInfoSelfMapper.selectOrgNumberAndProNumber(cardAuthorizationInfo.getOrganizationNumber(), cardAuthorizationInfo.getProductNumber());

                        if (cardProductInfo == null) {
                            // 跳过
                            continue;
                        }
                        // 卡组织
                        detail.setCardBin(ChannelEnum.getDes(cardProductInfo.getScheme()));
                        // 卡产品
                        detail.setCardProductNumber(cardAuthorizationInfo.getProductNumber());
                        // 卡产品名称
                        detail.setCardProductName(cardProductInfo.getDescription());
                    } else {
                        // 跳过
                        continue;
                    }
                }catch (Exception e){
                    // 跳过
                    continue;
                }

                // 币种
                detail.setCurrCode(temple.getCurrCode());
                // 交易码
                detail.setTxnCodeOrig(temple.getTxnCodeOrig());
                // 交易码描述
                try {
//                    ParmProductInfo parmProductInfo = parmProductInfoSelfMapper.selectByProdNumber(temple.getAcctLogo()).get(0);
                    ParmAcctProductMainInfo acctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(OrgNumberUtils.getOrg(), temple.getAcctLogo());
                    detail.setDescription(acctProductMainInfo.getDescription());
                }catch (Exception e){

                }
                // 入账日期
                detail.setPostDate(temple.getPostingDate());
                // 科目号
                detail.setGlAcct(temple.getGlAcct());
                // 科目名称
                TPmsGlacgn pmsGlacgn = tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(temple.getOrganizationNumber(), temple.getBranchid(), temple.getGlAcct(), temple.getCurrCode());
                detail.setGlAcctName(pmsGlacgn.getGlAcctName());
                // 借贷方向
                detail.setDrcr(temple.getDrcr());
                // 发生额
                Optional<BigDecimal> decimal = value.stream().map(AccountantGlvcher::getGlAmount).reduce(BigDecimal::add);
                detail.setGlAmount(decimal.orElse(BigDecimal.ZERO));

                cache.add(detail);
            }

            // 再根据卡产品分组
            Map<String, List<CardDetail>> group2 = cache.stream().collect(Collectors.groupingBy(x -> x.getCardProductNumber() + x.getTxnCodeOrig() + x.getCurrCode() + x.getGlAcct() + x.getDrcr()));
            for (Map.Entry<String, List<CardDetail>> entry : group2.entrySet()) {
                List<CardDetail> value = entry.getValue();

                Optional<BigDecimal> decimal = value.stream().map(CardDetail::getGlAmount).reduce(BigDecimal::add);
                CardDetail temple = value.get(0);
                temple.setGlAmount(decimal.orElse(BigDecimal.ZERO));

                result.add(temple);
            }
        }

        return result;
    }

    /**
     * 输出报表
     * @param data
     */
    private void out(List<CardDetail> data) throws FileNotFoundException {
        if (CollectionUtils.isNotEmpty(data)) {
            CardDetailReportConfigurer reportConfigurer0 = (CardDetailReportConfigurer)reportConfigurer;
            Map<String, Object> param = new HashMap<>();
            reportConfigurer0.putParam(param, timeTypeEnum);

            outReport(CardDetailReport.resetReportData(data, timeTypeEnum), reportConfigurer0.outFile(timeTypeEnum), reportConfigurer0.outFileName(timeTypeEnum), reportConfigurer0.templeFile(timeTypeEnum), param);
        }
    }
}
