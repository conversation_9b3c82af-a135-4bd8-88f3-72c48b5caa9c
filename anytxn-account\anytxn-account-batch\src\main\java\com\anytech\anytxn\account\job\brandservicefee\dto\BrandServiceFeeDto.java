package com.anytech.anytxn.account.job.brandservicefee.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021-08-04
 */
@Getter
@Setter
public class BrandServiceFeeDto {

    /**
     * 技术主键
     * 表字段:id
     */
    private String id;

    /**
     * 机构号
     * 表字段:organization_number
     */
    private String organizationNumber;

    /**
     * 中心日期时间（该批次的业务处理日期）
     * 表字段:process_date
     */
    private LocalDate processDate;

    /**
     * 发卡方一级分行代码
     * 表字段:branch_code1
     */
    private String branchCode1;

    /**
     * 是否银联标准卡
     * 表字段:is_card
     */
    private String isCard;

    /**
     * 借贷记卡标志
     * 表字段:debit_sign
     */
    private String debitSign;

    /**
     * 终端类型
     * 表字段:terminal_type
     */
    private String terminalType;

    /**
     * 卡介质
     * 表字段:media_card
     */
    private String mediaCard;

    /**
     * 交易地域标志
     * 表字段:trade_area_indication
     */
    private String tradeAreaIndication;

    /**
     * 商户类型
     * 表字段:merchant_category_code
     */
    private String merchantCategoryCode;

    /**
     * 发卡方二级分行代码
     * 表字段:branch_code2
     */
    private String branchCode2;

    /**
     * 受理机构代码
     * 表字段:acceptance_org_code
     */
    private String acceptanceOrgCode;

    /**
     * 转发机构代码(33号域)
     * 表字段:forwarding_org_code_33
     */
    private String forwardingOrgCode33;

    /**
     * 发卡机构代码
     * 表字段:card_issuing_org_code
     */
    private String cardIssuingOrgCode;

    /**
     * 转发机构代码(100号域)
     * 表字段:forwarding_org_code_100
     */
    private String forwardingOrgCode100;

    /**
     * 收单机构代码
     * 表字段:receiving_org_code
     */
    private String receivingOrgCode;

    /**
     * 报文类型
     * 表字段:message_type
     */
    private String messageType;

    /**
     * 交易处理码
     * 表字段:transaction_process_code
     */
    private String transactionProcessCode;

    /**
     * 服务点条件码
     * 表字段:service_condition_code
     */
    private String serviceConditionCode;

    /**
     * 系统跟踪号
     * 表字段:system_tracking_number
     */
    private String systemTrackingNumber;

    /**
     * 交易传输日期时间
     * 表字段:transaction_transfer_date_time
     */
    private String transactionTransferDateTime;

    /**
     * 主账号
     * 表字段:card_number
     */
    private String cardNumber;

    /**
     * 转出账号
     * 表字段:out_card_number
     */
    private String outCardNumber;

    /**
     * 转入账号
     * 表字段:in_card_number
     */
    private String inCardNumber;

    /**
     * 原始交易信息
     * 表字段:original_transaction_info
     */
    private String originalTransactionInfo;

    /**
     * 受卡方终端标识码
     * 表字段:card_terminal_code
     */
    private String cardTerminalCode;

    /**
     * 受卡方标识码
     * 表字段:card_sign_code
     */
    private String cardSignCode;

    /**
     * 检索参考号
     * 表字段:search_number
     */
    private String searchNumber;

    /**
     * 交易金额
     * 表字段:transaction_amount
     */
    private BigDecimal transactionAmount;

    /**
     * 保留使用
     * 表字段:reserved_use1
     */
    private String reservedUse1;

    /**
     * 品牌服务费
     * 表字段:brand_service_fee
     */
    private BigDecimal brandServiceFee;

    /**
     * 保留使用
     * 表字段:reserved_use2
     */
    private String reservedUse2;

    /**
     * 保留使用
     * 表字段:reserved_use3
     */
    private String reservedUse3;

    /**
     * 交易发起方式
     * 表字段:transaction_initiation
     */
    private String transactionInitiation;

    /**
     * 保留使用
     * 表字段:reserved_use4
     */
    private String reservedUse4;

    /**
     * 记录创建时间
     * 表字段:create_time
     */
    private LocalDateTime createTime;

    /**
     * 记录最新修改时间
     * 表字段:update_time
     */
    private LocalDateTime updateTime;

    /**
     * 修改人
     * 表字段:update_by
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:version_number
     */
    private Long versionNumber;
}
