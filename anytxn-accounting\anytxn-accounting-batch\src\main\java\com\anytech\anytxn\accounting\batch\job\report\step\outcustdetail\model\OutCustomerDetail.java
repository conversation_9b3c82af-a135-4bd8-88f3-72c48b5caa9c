package com.anytech.anytxn.accounting.batch.job.report.step.outcustdetail.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2020/9/14
 */
@Getter
@Setter
public class OutCustomerDetail {

    /**
     * 分行号
     */
    private String branchId;

    /**
     * 来源
     */
    private String source;

    /**
     * 交易地区
     */
    private String countryCode;

    /**
     * 客户号
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 管理账号
     */
    private String managementId;

    /**
     * 账产品id
     */
    private String productNumber;

    /**
     * 币种
     */
    private String currCode;

    /**
     * 入账日期
     */
    private LocalDate postDate;

    /**
     * 科目号
     */
    private String glAcct;

    /**
     * 科目名称
     */
    private String glAcctName;

    /**
     * 借贷方向
     */
    private String drcr;

    /**
     * 发生额
     */
    private BigDecimal glAmount;
}
