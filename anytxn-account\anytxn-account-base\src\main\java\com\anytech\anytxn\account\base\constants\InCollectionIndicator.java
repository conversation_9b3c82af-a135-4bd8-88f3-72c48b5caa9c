package com.anytech.anytxn.account.base.constants;

/**
 * 催收标志
 *
 * <AUTHOR>
 * @date 2018-9-22
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 */
public enum InCollectionIndicator {

    /**
     * 0 - 未催收
     * 1 - 催收
     */
    NOT_COLLECTION("0", "未催收"),

    COLLECTION("1", "催收");

    private String code;
    private String reason;


    InCollectionIndicator(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }
}
