package com.anytech.anytxn.accounting.batch.job.journal;

import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author: sukang
 * @Date: 2022/11/22 12:48
 */
@Slf4j
public class OracleSuiteApi {

    private OracleSuiteProperties oracleSuiteProperties;

    private WebClient webClient;

    public OracleSuiteApi(OracleSuiteProperties oracleSuiteProperties) {

        this.oracleSuiteProperties = oracleSuiteProperties;
        initWebClient();
    }


    public boolean doRestPost(String path, Object body, Signature signature) throws Exception {
        String restPath =  this.oracleSuiteProperties.getCompanyUrl() + "/"
                + this.oracleSuiteProperties.getRestService() + "/"
                + path;
        log.info(restPath);
        WebClient.RequestBodySpec weRequestBodySpec = getWebClient(restPath, body, signature);

        ClientResponse clientResponse = weRequestBodySpec.exchange().block();

        if (clientResponse == null) {
            log.error("path {} response is empty " , path);
            return false;
        }

        if (Objects.equals(HttpStatus.NO_CONTENT.value(), clientResponse.statusCode().value())){
            return true;
        }

        String block = clientResponse.bodyToMono(String.class).block();

        log.error("path {} response is error , the error message is {}", path, block);

        return false;

    }



    private WebClient.RequestBodySpec getWebClient(String restPath,
                                                   Object body,
                                                   Signature signature) throws Exception {
        WebClient.RequestBodySpec requestBodySpec = this.webClient
                .method(HttpMethod.POST)
                .uri(restPath)
                .contentType(MediaType.APPLICATION_JSON);

        WebClient.RequestHeadersSpec<?> requestHeadersSpec = requestBodySpec.bodyValue(body);

        SignatureDTO signatureDTO = SignatureDTO.SignatureDTOBuilder.aSignatureDTO()
                .withHttpMethod(HttpMethod.POST.name())
                .withUrl(restPath)
                .build();

        signature.signature(signatureDTO, this.oracleSuiteProperties, requestHeadersSpec );

        return requestBodySpec;
    }

    private WebClient.RequestBodySpec getWebClient(String restPath,
                                                   Object body) {
        WebClient.RequestBodySpec requestBodySpec = this.webClient
                .method(HttpMethod.POST)
                .uri(restPath)
                .contentType(MediaType.APPLICATION_JSON);

        WebClient.RequestHeadersSpec<?> requestHeadersSpec = requestBodySpec.bodyValue(body);

        return requestBodySpec;
    }






    private void initWebClient() {

        HttpClient nettyHttpClient = HttpClient.create()
                .compress(true)
                .tcpConfiguration(client ->
                        client.doOnConnected(conn -> conn
                                .addHandlerLast(new ReadTimeoutHandler(600, TimeUnit.SECONDS))
                                .addHandlerLast(new WriteTimeoutHandler(600,TimeUnit.SECONDS))));


        //暂时使用默认连接池
        this.webClient = WebClient
                .builder()
                .clientConnector(new ReactorClientHttpConnector(nettyHttpClient))
                .build();
    }




}
