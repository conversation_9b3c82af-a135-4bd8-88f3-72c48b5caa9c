<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/anytxn-account-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-account/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/anytxn-accounting-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/anytxn-accounting-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/anytxn-accounting-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/anytxn-accounting-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/anytxn-accounting-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/anytxn-accounting-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/anytxn-accounting-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/anytxn-accounting-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-accounting/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/anytxn-authorization-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-authorization/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-dao/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-dao/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/anytxn-business-core-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-business-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/anytxn-card-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-card/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/anytxn-central-processing-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-common-manager/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/anytxn-customer-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/anytxn-customer-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/anytxn-customer-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/anytxn-customer-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/anytxn-customer-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/anytxn-customer-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/anytxn-customer-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/anytxn-customer-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-customer/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-gateway/anytxn-api-gateway-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-gateway/anytxn-api-gateway-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-gateway/anytxn-api-gateway-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-gateway/anytxn-api-gateway-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-gateway/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/anytxn-installment-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/anytxn-installment-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/anytxn-installment-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/anytxn-installment-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/anytxn-installment-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/anytxn-installment-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/anytxn-installment-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/anytxn-installment-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-installment/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/anytxn-limit-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/anytxn-limit-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/anytxn-limit-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/anytxn-limit-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/anytxn-limit-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/anytxn-limit-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/anytxn-limit-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/anytxn-limit-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-limit/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/anytxn-mapping-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/anytxn-mapping-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/anytxn-mapping-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/anytxn-mapping-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/anytxn-mapping-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/anytxn-mapping-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/anytxn-mapping-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/anytxn-mapping-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-mapping/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/anytxn-monetary-processing-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/anytxn-monetary-processing-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/anytxn-monetary-processing-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/anytxn-monetary-processing-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/anytxn-monetary-processing-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/anytxn-monetary-processing-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/anytxn-monetary-processing-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/anytxn-monetary-processing-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-monetary-processing/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/anytxn-parameter-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parameter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-loadbalancer/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-loadbalancer/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-rule/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-rule/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-sequence/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-sequence/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-sharding/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/anytxn-common-sharding/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/anytxn-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-parent/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-rule/anytxn-rule-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-rule/anytxn-rule-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-rule/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-rule/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/anytxn-settlement-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/anytxn-settlement-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/anytxn-settlement-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/anytxn-settlement-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/anytxn-settlement-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/anytxn-settlement-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/anytxn-settlement-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/anytxn-settlement-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-settlement/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/anytxn-file-manager-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/anytxn-file-manager-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/anytxn-file-manager-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/anytxn-file-manager-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-file-manager/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/anytxn-hsm-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/anytxn-hsm-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/anytxn-hsm-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/anytxn-hsm-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-hsm/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/anytxn-notification-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/anytxn-notification-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/anytxn-notification-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/anytxn-notification-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/anytxn-notification/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-third-party-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-batch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-batch/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/anytxn-transaction-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/anytxn-transaction/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
  </component>
</project>