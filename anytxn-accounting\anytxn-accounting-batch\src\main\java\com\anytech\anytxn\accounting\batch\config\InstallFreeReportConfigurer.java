package com.anytech.anytxn.accounting.batch.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 入账分期手续费配置
 * <AUTHOR>
 * @date 2020/9/12
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.report.install-free")
public class InstallFreeReportConfigurer extends BaseConfigurer{

}
