# 更新日志

## [未发布]

### 新增
- 创建 CHANGELOG.md 文件用于记录项目修改历史 

### 检查
- 检查 memory-bank 目录结构，确认包含 docs、data、templates 三个子目录，目前均为空目录 

### 重构
- 批量将 anytxn-account-sdk 模块下所有 AnyTxnAccountingRespCode、AccountingRepDetail、TransactionTypeCode、AutoPaymentBo 等旧类名引用，全部替换为新命名 Enum（如 AnyTxnAccountingRespCodeEnum、AccountingRepDetailEnum、TransactionTypeCodeEnum、AutoPaymentBO 等），并修正相关 import、方法签名、异常抛出等。 

### [命名规范全面检查与修正]
- AccountStatementInfoServiceImpl.java：检查无旧类名遗留，无需修改。
- AccountStatisticsInfoServiceImpl.java：检查无旧类名遗留，无需修改。
- AutoEbitSignUpInforServiceImpl.java：修正所有 AnyTxnAccountingRespCode → AnyTxnAccountingRespCodeEnum，AccountingRepDetail → AccountingRepDetailEnum，并修正 import 及相关方法调用。
- AutoPaymentLogHistoryServiceImpl.java：检查无旧类名遗留，无需修改。
- AutoPaymentLogServiceImpl.java：检查无旧类名遗留，无需修改。
- CustomerDebtServiceImpl.java：检查无旧类名遗留，无需修改。
- OverpayAllocationServiceImpl.java：部分 import 路径为 business.base.account.enums.TransactionTypeCodeEnum，建议统一为 account.base.enums.TransactionTypeCodeEnum（如需统一请补充说明）。
- StampTaxSumLimitServiceImpl.java：检查无旧类名遗留，无需修改。
- 其它 controller、feign、config、domain、enums 包：全部合规，无需修改。

### [controller包命名规范检查与修正]
- CustomerDebtController.java：无旧类名遗留，无需修改。
- WeChatController.java：将AccountingRepDetail替换为AccountingRepDetailEnum，并修正import和方法调用。
- AutoEbitSignUpInforController.java：将AccountingRepDetail替换为AccountingRepDetailEnum，并修正import和方法调用。
- AutoPaymentLogController.java：无旧类名遗留，无需修改。
- AutoPaymentLogHistoryController.java：无旧类名遗留，无需修改。
- AccountStatementController.java：将AccountingRepDetail替换为AccountingRepDetailEnum，并修正import和方法调用。
- AccountStaticsController.java：无旧类名遗留，无需修改。
- AccountLoyaltyInfoController.java：无旧类名遗留，无需修改。
- AccountManagementController.java：将AccountingRepDetail和AnyTxnAccountingRespCode替换为AccountingRepDetailEnum和AnyTxnAccountingRespCodeEnum，并修正import和方法调用。
- AccountOptinalInfoController.java：将AccountingRepDetail替换为AccountingRepDetailEnum，并修正import和方法调用。 

### [service包命名规范检查与修正]
- AccountOptinalInfoServiceImpl.java：将AnyTxnAccountingRespCode全部替换为AnyTxnAccountingRespCodeEnum，并修正import和相关异常抛出。
- AccountManageInfoServiceImpl.java：将AutoPaymentBo、AccountingRepDetail、AnyTxnAccountingRespCode、TransactionTypeCode全部替换为AutoPaymentBO、AccountingRepDetailEnum、AnyTxnAccountingRespCodeEnum、TransactionTypeCodeEnum，并修正import、类型声明、异常抛出、方法调用等。
- OverpayAllocationServiceImpl.java：将TransactionTypeCode全部替换为TransactionTypeCodeEnum，并修正import和相关方法调用。
- AutoEbitSignUpInforServiceImpl.java：将AnyTxnAccountingRespCode和AccountingRepDetail全部替换为AnyTxnAccountingRespCodeEnum和AccountingRepDetailEnum，并修正import和相关方法调用、注释。
- 其它service实现类均无旧类名遗留，无需修改。

### [feign与AccountLoyaltyInfoServiceImpl命名规范修正]
- TxnToLoyaltyFeignFallBack.java：将AnyTxnAccountingRespCode全部替换为AnyTxnAccountingRespCodeEnum，并修正import和相关方法调用。
- AccountLoyaltyInfoServiceImpl.java：将AccountingRepDetail和AnyTxnAccountingRespCode全部替换为AccountingRepDetailEnum和AnyTxnAccountingRespCodeEnum，并修正import、异常抛出、方法调用等。
- AccountManageInfoServiceImpl.java：已无旧类名遗留，无需修改。 

## anytxn-account-batch 模块重构

### 2024-05-20

- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/autopayment/config/AutoPayBatchConfig.java` 文件:**
  - 将 `AutoPaymentBo` 类名及其引用修正为 `AutoPaymentBO`。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/autopayment/step/AutoPayProcessor.java` 文件:**
  - 将 `AutoPaymentBo` 类名及其引用修正为 `AutoPaymentBO`。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/autopayment/step/AutoPayReader.java` 文件:**
  - 将 `AnyTxnAccountingRespCode` 类名及其引用修正为 `AnyTxnAccountingRespCodeEnum`。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/autopayment/step/AutoPayWriter.java` 文件:**
  - 将 `AutoPaymentBo` 类名及其引用修正为 `AutoPaymentBO`。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/brandservicefee/step2/WriteGlamsTasklet.java` 文件:**
  - 将 `AnyTxnAccountingRespCode` 类名及其引用修正为 `AnyTxnAccountingRespCodeEnum`。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/exchange/step/ExchangePaymentReader.java` 文件:**
  - 将 `AnyTxnAccountingRespCode` 类名及其引用修正为 `AnyTxnAccountingRespCodeEnum`。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/paymentfile/config/AutoPaymentFileConfig.java` 文件:**
  - 将 `AccountingRepDetail` 类名及其引用修正为 `AccountingRepDetailEnum`。
  - 将 `AnyTxnAccountingRespCode` 类名及其引用修正为 `AnyTxnAccountingRespCodeEnum`。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/paymentfile/config/RepaymentFileConfig.java` 文件:**
  - 将 `AccountingRepDetail` 类名及其引用修正为 `AccountingRepDetailEnum`。
  - 将 `AnyTxnAccountingRespCode` 类名及其引用修正为 `AnyTxnAccountingRespCodeEnum`。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/paymentfile/step/auto/AutoPaymentLogReader.java` 文件:**
  - 将 `AccountingRepDetail` 类名及其引用修正为 `AccountingRepDetailEnum`。
  - 将 `AnyTxnAccountingRespCode` 类名及其引用修正为 `AnyTxnAccountingRespCodeEnum`。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/paymentfile/step/repay/RepaymentFileSplitTasklet.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/paymentfile/step/repay/RepaymentFileWriter.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/stamptaxsumlimit/shard/config/ShardConfig.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/stamptaxsumlimit/shard/step/ShardTasklet.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/utils/AccountBatchPartitionGroup.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/utils/AccountBatchPartitionHelper.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/utils/FileConstant.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/utils/FileUtil.java` 文件:**
  - 将 `AccountingRepDetail` 类名及其引用修正为 `AccountingRepDetailEnum`。
  - 将 `AnyTxnAccountingRespCode` 类名及其引用修正为 `AnyTxnAccountingRespCodeEnum`。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/incollection/config/IncollectionBatchConfig.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/incollection/step/IncollectionProcessor.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/incollection/step/IncollectionReader.java` 文件:**
  - 将 `AnyTxnAccountingRespCode` 类名及其引用修正为 `AnyTxnAccountingRespCodeEnum`。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/newautopay/config/AutoPaymentBatchConfig.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **修正 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/newautopay/config/NewAutoPaymentFileConfig.java` 文件:**
  - 将 `AnyTxnAccountingRespCode` 类名及其引用修正为 `AnyTxnAccountingRespCodeEnum`。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/newautopay/step/AutoPaymentProcessor.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/newautopay/step/AutoPaymentWriter.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/overpayallocation/config/OverpayAllocationConfig.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/overpayallocation/partitioner/OverpayAllocationPartitioner.java` 文件:**
  - 未找到此文件，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/overpayallocation/step/OverpayAllocationProcessor.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/overpayallocation/step/OverpayAllocationReader.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **检查 `anytxn-account-batch/src/main/java/com/anytech/anytxn/account/job/overpayallocation/step/OverpayAllocationWriter.java` 文件:**
  - 未发现旧类名引用，无需修正。
- **全局搜索 `anytxn-account-batch` 模块:**
  - 确认所有 `AutoPaymentBo` 的引用都已修正为 `AutoPaymentBO`。
  - 确认所有 `AnyTxnAccountingRespCode` 的引用都已修正为 `AnyTxnAccountingRespCodeEnum`。
  - 确认所有 `AccountingRepDetail` 的引用都已修正为 `AccountingRepDetailEnum`。