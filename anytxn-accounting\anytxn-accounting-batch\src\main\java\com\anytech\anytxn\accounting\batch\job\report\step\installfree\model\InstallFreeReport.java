package com.anytech.anytxn.accounting.batch.job.report.step.installfree.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2020/9/11
 */
@Getter
@Setter
public class InstallFreeReport {
    /**
     * 技术id
     */
    private String id;

    /**
     * 机构
     */
    private String organizationNumber;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 入账日期
     */
    private LocalDate postingDate;

    /**
     * 分期产品编号
     */
    private String productCode;

    /**
     * 分期产品描述
     */
    private String productDescription;

    /**
     * 币种
     */
    private String currCode;

    /**
     * 交易码
     */
    private String transactionCode;

    /**
     * 交易码描述
     */
    private String discription;

    /**
     * 交易金额
     */
    private BigDecimal glAmount;

    /**
     * 贷科目
     */
    private String cAcct;

    /**
     * 借科目
     */
    private String dAcct;
}
