package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.anytxn.common.core.base.BaseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 会计日历表 
 * 表:ACCOUNTANT_GLACGP
 * <AUTHOR> 
 * @date 2019-10-11 
 */
public class TAmsGlacgp extends BaseEntity {
    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 年
     * 表字段:GL_YEAR
     */
    private String glYear;

    /**
     * 月
     * 表字段:GL_MONTH
     */
    private String glMonth;

    /**
     * 开始日期
     * 表字段:GL_PERIOD_START
     */
    private LocalDate glPeriodStart;

    /**
     * 结束日期
     * 表字段:GL_PERIOD_END
     */
    private LocalDate glPeriodEnd;

    /**
     * 状态
     * 表字段:GL_PERIOD_STATUS
     */
    private String glPeriodStatus;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

    /**
     * 获取技术主键
     * @return id Long
     */
    public String getId() {
        return id;
    }

    /**
     * 设置技术主键
     * @param id 技术主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取分行号
     * @return branchid String
     */
    public String getBranchid() {
        return branchid;
    }

    /**
     * 设置分行号
     * @param branchid 分行号
     */
    public void setBranchid(String branchid) {
        this.branchid = branchid == null ? null : branchid.trim();
    }

    /**
     * 获取年
     * @return glYear String
     */
    public String getGlYear() {
        return glYear;
    }

    /**
     * 设置年
     * @param glYear 年
     */
    public void setGlYear(String glYear) {
        this.glYear = glYear == null ? null : glYear.trim();
    }

    /**
     * 获取月
     * @return glMonth String
     */
    public String getGlMonth() {
        return glMonth;
    }

    /**
     * 设置月
     * @param glMonth 月
     */
    public void setGlMonth(String glMonth) {
        this.glMonth = glMonth == null ? null : glMonth.trim();
    }

    /**
     * 获取开始日期
     * @return glPeriodStart LocalDate
     */
    public LocalDate getGlPeriodStart() {
        return glPeriodStart;
    }

    /**
     * 设置开始日期
     * @param glPeriodStart 开始日期
     */
    public void setGlPeriodStart(LocalDate glPeriodStart) {
        this.glPeriodStart = glPeriodStart;
    }

    /**
     * 获取结束日期
     * @return glPeriodEnd LocalDate
     */
    public LocalDate getGlPeriodEnd() {
        return glPeriodEnd;
    }

    /**
     * 设置结束日期
     * @param glPeriodEnd 结束日期
     */
    public void setGlPeriodEnd(LocalDate glPeriodEnd) {
        this.glPeriodEnd = glPeriodEnd;
    }

    /**
     * 获取状态
     * @return glPeriodStatus String
     */
    public String getGlPeriodStatus() {
        return glPeriodStatus;
    }

    /**
     * 设置状态
     * @param glPeriodStatus 状态
     */
    public void setGlPeriodStatus(String glPeriodStatus) {
        this.glPeriodStatus = glPeriodStatus == null ? null : glPeriodStatus.trim();
    }

    /**
     * 获取创建日期
     * @return createTime LocalDateTime
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建日期
     * @param createTime 创建日期
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新日期
     * @return updateTime LocalDateTime
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新日期
     * @param updateTime 更新日期
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新用户
     * @return updateBy String
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新用户
     * @param updateBy 更新用户
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    /**
     * 获取版本号
     * @return versionNumber Long
     */
    public Long getVersionNumber() {
        return versionNumber;
    }

    /**
     * 设置版本号
     * @param versionNumber 版本号
     */
    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }
}