package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherAdjDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;

/**
 * <AUTHOR>
public interface IGlVoucherAdjService {
/**
 * TODO
  * @param no
 * @return com.anytech.anytxn.accountant.dto.TAmsGlvcherAdjDTO
 * <AUTHOR> @date 2020-7-28
 */
    TAmsGlvcherAdjDTO getGlAmsVcherAdjByNo(String no);
/**
 * TODO
  * @param page
 * @param rows
 * @param postingDate
 * @param org
 * @param no
 * @return com.anytech.anytxn.biz.common.dto.PageResultDTO<com.anytech.anytxn.accountant.dto.TAmsGlvcherAdjDTO>
 * <AUTHOR> @date 2020-7-28
 */
    PageResultDTO<TAmsGlvcherAdjDTO> getGlAmsVcherAdjByPage(Integer page, Integer rows, String postingDate, String org, String no);
/**
 * TODO
  * @param data
 * @return
 * <AUTHOR> @date 2020-7-28
 */
    void add(TAmsGlvcherAdjDTO data);
/**
 * TODO
  * @param data
 * @return
 * <AUTHOR> @date 2020-7-28
 */
    void modify(TAmsGlvcherAdjDTO data);

/**
 * TODO
  * @param no
 * @return
 * <AUTHOR> @date 2020-7-28
 */
    void delete(String  no);

}
