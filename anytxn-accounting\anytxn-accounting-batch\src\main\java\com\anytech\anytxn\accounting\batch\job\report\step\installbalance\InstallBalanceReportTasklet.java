package com.anytech.anytxn.accounting.batch.job.report.step.installbalance;

import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.installbalance.dto.InstallBalanceReport;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallProductInfo;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoSelfMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.job.report.step.installbalance.model.InstallBalance;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 分期余额
 * <AUTHOR>
 * @date 2020/9/11
 */
@Slf4j
public class InstallBalanceReportTasklet extends AbstractReportTasklet<AccountBalanceInfo, InstallBalanceReport> {

    @Autowired
    private InstallProductInfoSelfMapper installProductInfoSelfMapper;
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    public InstallBalanceReportTasklet(BaseConfigurer baseConfigurer) {
        super(baseConfigurer);
    }

    @Override
    protected List<AccountBalanceInfo> loadModels() {
        return accountBalanceInfoSelfMapper.selectOrder(OrgNumberUtils.getOrg());
    }

    @Override
    protected List<InstallBalanceReport> modelToReport(List<AccountBalanceInfo> models) {
        if (CollectionUtils.isEmpty(models)) {
            return Lists.newArrayList();
        }

        List<InstallBalance> result = Lists.newArrayList();
        Map<String, InstallOrder> orderMap = Maps.newHashMap();
        Map<String, AccountManagementInfo> managementMap = Maps.newHashMap();
        for (AccountBalanceInfo model : models) {
            InstallBalance balance = new InstallBalance();

            // 分行号
            AccountManagementInfo managementInfo = managementMap.containsKey(model.getAccountManagementId()) ? managementMap.get(model.getAccountManagementId()) : accountManagementInfoMapper.selectByPrimaryKey(model.getAccountManagementId());
            managementMap.put(managementInfo.getAccountManagementId(), managementInfo);
            balance.setBranchId(managementInfo.getBranchNumber());

            // 分期产品
            InstallOrder installOrder = orderMap.containsKey(model.getInstallmentOrderNumber()) ? orderMap.get(model.getInstallmentOrderNumber()) : installOrderMapper.selectByPrimaryKey(model.getInstallmentOrderNumber());
            orderMap.put(installOrder.getOrderId(), installOrder);
            balance.setProductNumber(installOrder.getProductCode());
            // 描述
            InstallProductInfo productInfo = installProductInfoSelfMapper.selectByIndex(installOrder.getOrganizationNumber(), installOrder.getProductCode());
            balance.setDescription(productInfo.getProductDesc());
            // 币种
            balance.setCurrCode(model.getCurrency());
            // 金额
            balance.setBalance(model.getBalance());

            result.add(balance);
        }

        return Lists.newArrayList(InstallBalanceReport.resetReportData(result));
    }
}
