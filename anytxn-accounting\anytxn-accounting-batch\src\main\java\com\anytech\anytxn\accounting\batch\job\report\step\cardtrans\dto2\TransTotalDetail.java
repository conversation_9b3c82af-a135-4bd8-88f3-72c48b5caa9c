package com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.dto2;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/10/15 18:05
 */
@Getter
@Setter
@ToString
public class TransTotalDetail {

    private String currency;

    //币种交易详情
    private List<TransTotalDetailSubDetail> transTotalDetailSubDetailList = new ArrayList<>();

    //币种小计
    private List<TransTotalDetailSub> transTotalDetailSubList = new ArrayList<>();


}
