package com.anytech.anytxn.accounting.batch.job.report.step.installfree.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 入账分期手续费报表
 * <AUTHOR>
 * @Date 2020/9/11 11:48
 */
@Getter
@Setter
public class PostInstallFeeDetail {

    //卡号
    private String cardNumber;
    //订单号
    private String orderNo;
    //产品ID
    private String productId;
    //产品描述
    private String productDesc;
    //币种
    private String currencyCode;
    //入账日期
    private String postDate;
    //金额
    private BigDecimal amount;
    //交易码
    private String transactionCode;
    //描述
    private String desc;
    //借记科目
    private String debitSubject;
    //贷记科目
    private String crebitSubject;
}
