package com.anytech.anytxn.accounting.batch.job.report.step.orgsum.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 根据分期产品ID+币种维度
 * <AUTHOR>
 * @Date 2020/9/11 11:46
 */
@Getter
@Setter
@ToString
public class DistIOrganizationPostSub {
    private List<DistIOrganizationPostDetail> detailList;
    /**小计**/
    //分行号
    private String bankNumber;
    //币种
    private String currencyCode;
    //发送科目数
    private Integer subjectCount;
    private boolean end = false;
}
