package com.anytech.anytxn.accounting.base.utils;

import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgd;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgdB;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgm;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgmB;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgyB;
import com.anytech.anytxn.accounting.base.constants.AccountantConstants;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgpDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherAbsDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherSumDTO;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlacgy;

import java.time.LocalDateTime;

/**
 * 类转换工具,避免使用copy反射
 *
 * <AUTHOR>
 * @date 2019-10-08
 */
public class BeanCopyUtils {

  public static TAmsGlvcherDTO copyTAmsGlvcher(TAmsGlvcherDTO source) {
    TAmsGlvcherDTO tAmsGlvcher = new TAmsGlvcherDTO();
    tAmsGlvcher.setChannelId(source.getChannelId());
    tAmsGlvcher.setSubchannelId(source.getSubchannelId());
    tAmsGlvcher.setFundId(source.getFundId());
    tAmsGlvcher.setPlatformId(source.getPlatformId());
    tAmsGlvcher.setMerchantNumber(source.getMerchantNumber());
    tAmsGlvcher.setId(source.getId());
    tAmsGlvcher.setOrganizationNumber(source.getOrganizationNumber());
    tAmsGlvcher.setBranchid(source.getBranchid());
    tAmsGlvcher.setAccountManagementId(source.getAccountManagementId());
    tAmsGlvcher.setAcctLogo(source.getAcctLogo());
    tAmsGlvcher.setGlobalFlowNo(source.getGlobalFlowNo());
    tAmsGlvcher.setModuleFlag(source.getModuleFlag());
    tAmsGlvcher.setVpTxnCode(source.getVpTxnCode());
    tAmsGlvcher.setCurrCode(source.getCurrCode());
    tAmsGlvcher.setGlAcct(source.getGlAcct());
    tAmsGlvcher.setDrcr(source.getDrcr());
    tAmsGlvcher.setGlAmount(source.getGlAmount());
    tAmsGlvcher.setPostingDate(source.getPostingDate());
    tAmsGlvcher.setProcessType(source.getProcessType());
    tAmsGlvcher.setOrderId(source.getOrderId());
    tAmsGlvcher.setAssetNo(source.getAssetNo());
    tAmsGlvcher.setAbsStatus(source.getAbsStatus());
    tAmsGlvcher.setTxnCodeOrig(source.getTxnCodeOrig());
    tAmsGlvcher.setCreateTime(source.getCreateTime());
    tAmsGlvcher.setUpdateTime(source.getUpdateTime());
    tAmsGlvcher.setUpdateBy(source.getUpdateBy());
    tAmsGlvcher.setVersionNumber(source.getVersionNumber());
    return tAmsGlvcher;
  }

  public static TAmsGlvcherSumDTO copyTAmsGlvcherSum(TAmsGlvcherDTO source) {
    TAmsGlvcherSumDTO tAmsGlvcherSum = new TAmsGlvcherSumDTO();
    tAmsGlvcherSum.setOrganizationNumber(source.getOrganizationNumber());
    tAmsGlvcherSum.setBranchid(source.getBranchid());
    tAmsGlvcherSum.setFundId(source.getFundId());
    tAmsGlvcherSum.setCurrCode(source.getCurrCode());
    tAmsGlvcherSum.setGlAcct(source.getGlAcct());
    tAmsGlvcherSum.setOccurDb(source.getGlAmount());
    tAmsGlvcherSum.setOccurCr(source.getGlAmount());
    tAmsGlvcherSum.setPostingDate(source.getPostingDate());
    tAmsGlvcherSum.setCreateTime(LocalDateTime.now());
    tAmsGlvcherSum.setUpdateTime(LocalDateTime.now());
    tAmsGlvcherSum.setUpdateBy(AccountantConstants.DEFAULT_USER);
    return tAmsGlvcherSum;
  }

  public static TAmsGlacgpDTO copyTAmsGlacgp(TAmsGlacgpDTO source) {
    TAmsGlacgpDTO amsGlacgp = new TAmsGlacgpDTO();
    amsGlacgp.setOrganizationNumber(source.getOrganizationNumber());
    amsGlacgp.setBranchid(source.getBranchid());
    amsGlacgp.setGlYear(source.getGlYear());
    amsGlacgp.setGlMonth(source.getGlMonth());
    amsGlacgp.setGlPeriodStart(source.getGlPeriodStart());
    amsGlacgp.setGlPeriodEnd(source.getGlPeriodEnd());
    amsGlacgp.setGlPeriodStatus(source.getGlPeriodStatus());

    amsGlacgp.setCreateTime(LocalDateTime.now());
    amsGlacgp.setUpdateTime(LocalDateTime.now());
    amsGlacgp.setUpdateBy(AccountantConstants.DEFAULT_USER);
      amsGlacgp.setVersionNumber(1L);
    return amsGlacgp;
  }

    public static TAmsGlacgdB copyTAmsGlacgdB(TAmsGlacgd source) {
        TAmsGlacgdB amsGlacgdb = new TAmsGlacgdB();
        amsGlacgdb.setOrganizationNumber(source.getOrganizationNumber());
        amsGlacgdb.setBranchid(source.getBranchid());
        amsGlacgdb.setDateD(source.getDateD());
        amsGlacgdb.setGlAcct(source.getGlAcct());
        amsGlacgdb.setCurr(source.getCurr());
        amsGlacgdb.setGlClass(source.getGlClass());
        amsGlacgdb.setGlName(source.getGlName());
        amsGlacgdb.setPrevBalDr(source.getCurrBalDr());
        amsGlacgdb.setPrevBalCr(source.getCurrBalCr());
        amsGlacgdb.setOccurDr(source.getOccurDr());
        amsGlacgdb.setOccurCr(source.getOccurCr());
        amsGlacgdb.setCurrBalDr(source.getPrevBalDr());
        amsGlacgdb.setCurrBalCr(source.getCurrBalCr());
        amsGlacgdb.setCreateTime(LocalDateTime.now());
        amsGlacgdb.setUpdateTime(LocalDateTime.now());
        amsGlacgdb.setUpdateBy(AccountantConstants.DEFAULT_USER);
        amsGlacgdb.setVersionNumber(1L);
        return amsGlacgdb;
    }

    public static TAmsGlacgmB copyTAmsGlacgmB(TAmsGlacgm source) {
        TAmsGlacgmB amsGlacgmb = new TAmsGlacgmB();
        amsGlacgmb.setOrganizationNumber(source.getOrganizationNumber());
        amsGlacgmb.setBranchid(source.getBranchid());
        amsGlacgmb.setDateM(source.getDateM());
        amsGlacgmb.setGlAcct(source.getGlAcct());
        amsGlacgmb.setCurr(source.getCurr());
        amsGlacgmb.setGlClass(source.getGlClass());
        amsGlacgmb.setGlName(source.getGlName());
        amsGlacgmb.setPrevBalDr(source.getCurrBalDr());
        amsGlacgmb.setPrevBalCr(source.getCurrBalCr());
        amsGlacgmb.setOccurDr(source.getOccurDr());
        amsGlacgmb.setOccurCr(source.getOccurCr());
        amsGlacgmb.setCurrBalDr(source.getPrevBalDr());
        amsGlacgmb.setCurrBalCr(source.getCurrBalCr());
        amsGlacgmb.setCreateTime(LocalDateTime.now());
        amsGlacgmb.setUpdateTime(LocalDateTime.now());
        amsGlacgmb.setUpdateBy(AccountantConstants.DEFAULT_USER);
        amsGlacgmb.setVersionNumber(1L);
        return amsGlacgmb;
    }

    public static TAmsGlacgyB copyTAmsGlacgyB(TAmsGlacgy source) {
        TAmsGlacgyB amsGlacgyb = new TAmsGlacgyB();
        amsGlacgyb.setOrganizationNumber(source.getOrganizationNumber());
        amsGlacgyb.setBranchid(source.getBranchid());
        amsGlacgyb.setDateY(source.getDateY());
        amsGlacgyb.setGlAcct(source.getGlAcct());
        amsGlacgyb.setCurr(source.getCurr());
        amsGlacgyb.setGlClass(source.getGlClass());
        amsGlacgyb.setGlName(source.getGlName());
        amsGlacgyb.setPrevBalDr(source.getCurrBalDr());
        amsGlacgyb.setPrevBalCr(source.getCurrBalCr());
        amsGlacgyb.setOccurDr(source.getOccurDr());
        amsGlacgyb.setOccurCr(source.getOccurCr());
        amsGlacgyb.setCurrBalDr(source.getPrevBalDr());
        amsGlacgyb.setCurrBalCr(source.getCurrBalCr());
        amsGlacgyb.setCreateTime(LocalDateTime.now());
        amsGlacgyb.setUpdateTime(LocalDateTime.now());
        amsGlacgyb.setUpdateBy(AccountantConstants.DEFAULT_USER);
        amsGlacgyb.setVersionNumber(1L);
        return amsGlacgyb;
    }

    public static TAmsGlvcherAbsDTO copyTAmsGlvcherAbs(TAmsGlvcherDTO source) {
        TAmsGlvcherAbsDTO tAmsGlvcherAbs = new TAmsGlvcherAbsDTO();
        tAmsGlvcherAbs.setChannelId(source.getChannelId());
        tAmsGlvcherAbs.setSubchannelId(source.getSubchannelId());
        tAmsGlvcherAbs.setFundId(source.getFundId());
        tAmsGlvcherAbs.setPlatformId(source.getPlatformId());
        tAmsGlvcherAbs.setMerchantNumber(source.getMerchantNumber());
        tAmsGlvcherAbs.setOrganizationNumber(source.getOrganizationNumber());
        tAmsGlvcherAbs.setBranchid(source.getBranchid());
        tAmsGlvcherAbs.setAccountManagementId(source.getAccountManagementId());
        tAmsGlvcherAbs.setAcctLogo(source.getAcctLogo());
        tAmsGlvcherAbs.setGlobalFlowNo(source.getGlobalFlowNo());
        tAmsGlvcherAbs.setModuleFlag(source.getModuleFlag());
        tAmsGlvcherAbs.setVpTxnCode(source.getVpTxnCode());
        tAmsGlvcherAbs.setCurrCode(source.getCurrCode());
        tAmsGlvcherAbs.setGlAcct(source.getGlAcct());
        tAmsGlvcherAbs.setDrcr(source.getDrcr());
        tAmsGlvcherAbs.setGlAmount(source.getGlAmount());
        tAmsGlvcherAbs.setGlCnt(source.getGlCnt());
        tAmsGlvcherAbs.setPostingDate(source.getPostingDate());
        tAmsGlvcherAbs.setProcessType(source.getProcessType());
        tAmsGlvcherAbs.setOrderId(source.getOrderId());
        tAmsGlvcherAbs.setAssetNo(source.getAssetNo());
        tAmsGlvcherAbs.setAbsStatus(source.getAbsStatus());
        tAmsGlvcherAbs.setTxnCodeOrig(source.getTxnCodeOrig());
        tAmsGlvcherAbs.setFiveTypeIndicator(source.getFiveTypeIndicator());
        tAmsGlvcherAbs.setAbsType(source.getAbsType());

        tAmsGlvcherAbs.setCreateTime(LocalDateTime.now());
        tAmsGlvcherAbs.setUpdateTime(LocalDateTime.now());
        tAmsGlvcherAbs.setUpdateBy(AccountantConstants.DEFAULT_USER);
        tAmsGlvcherAbs.setVersionNumber(1L);
        return tAmsGlvcherAbs;
    }




}
