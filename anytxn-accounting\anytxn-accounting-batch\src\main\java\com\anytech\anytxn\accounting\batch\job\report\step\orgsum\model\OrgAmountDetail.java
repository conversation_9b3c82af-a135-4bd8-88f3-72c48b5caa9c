package com.anytech.anytxn.accounting.batch.job.report.step.orgsum.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2020/9/14
 */
@Getter
@Setter
public class OrgAmountDetail {

    /**
     * 分行号
     */
    private String branchId;

    /**
     * 网点号
     */
    private String fundId;

    /**
     * 币种
     */
    private String currCode;

    /**
     * 科目号
     */
    private String glAcct;

    /**
     * 科目名称
     */
    private String glAcctName;

    /**
     * 科目分类
     */
    private String glAcctType;

    /**
     * 入账日期
     */
    private LocalDate postDate;

    /**
     * 借发生额
     */
    private BigDecimal dMount;

    /**
     * 贷发生额
     */
    private BigDecimal cMount;

}
