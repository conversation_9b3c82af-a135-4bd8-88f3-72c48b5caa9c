package com.anytech.anytxn.account.service;

import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.base.service.ICustomerDebtService;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountDebtDTO;
import com.anytech.anytxn.business.base.account.domain.dto.CurrencyDebtDTO;
import com.anytech.anytxn.business.base.account.domain.dto.CustomerDebtsDTO;
import com.anytech.anytxn.business.base.account.domain.dto.TransactionTypeInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户欠款实现类
 * <AUTHOR>
 * @date 2020-10-21
 **/
@Service
public class CustomerDebtServiceImpl implements ICustomerDebtService {

    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;
    @Autowired
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;
    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;

    /**
     * 根据查询类型(客户号:C  证件号:I   EcIf号:E)查询客户欠款信息
     * @param searchType  searchType
     * @param number number
     * @return CustomerDebtsDTO
     */
    @Override
    public CustomerDebtsDTO getCustomerDebtBySearchCondition(String searchType, String number) {
        CustomerDebtsDTO customerDebtsDTO = new CustomerDebtsDTO();
        CustomerAuthorizationInfo customerAuthorizationInfo = null ;
        //根据客户号查询
        if ("C".equals(searchType)) {
            customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByOrgNumberAndCustomerId(OrgNumberUtils.getOrg(), number == null ? null : number.trim());
            if(ObjectUtils.isEmpty(customerAuthorizationInfo)){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.CU_AU);
            }
        }else if("I".equals(searchType)){
            //根据证件号查询
            customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByOrgNumberAndIdNumber(OrgNumberUtils.getOrg(), number == null ? null : number.trim());
            if(ObjectUtils.isEmpty(customerAuthorizationInfo)){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.CU_AU);
            }
        }


        customerDebtsDTO.setMobilePhone(customerAuthorizationInfo.getMobilePhone());
        customerDebtsDTO.setChineseName(customerAuthorizationInfo.getChineseName());
        customerDebtsDTO.setIdNumber(customerAuthorizationInfo.getIdNumber());
        customerDebtsDTO.setStatus(customerAuthorizationInfo.getStatus());


        List<AccountManagementInfo> accountManagementInfos = accountManagementInfoMapper.selectByCustomerIdAndOrganizationNumber(customerAuthorizationInfo.getCustomerId(), OrgNumberUtils.getOrg());
        if(CollectionUtils.isEmpty(accountManagementInfos)){
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.CU_AU);
        }

        List<AccountDebtDTO> accountDebtDTOS = new ArrayList<>();
        BigDecimal sumDebt = new BigDecimal(0);
        List<CurrencyDebtDTO> currencyDebtDTOS = new ArrayList<>();
        for (int i = 0; i <accountManagementInfos.size() ; i++) {
            AccountDebtDTO accountDebtDTO = new AccountDebtDTO();
            accountDebtDTO.setSerialNumber(String.valueOf(i+1));
            accountDebtDTO.setProductNumber(accountManagementInfos.get(i).getProductNumber());
            accountDebtDTO.setCurrency(accountManagementInfos.get(i).getCurrency());
            accountDebtDTO.setBlockCode(accountManagementInfos.get(i).getBlockCode());
            accountDebtDTO.setLastStatementDate(accountManagementInfos.get(i).getLastStatementDate());

            AccountStatementInfo accountStatementInfo = accountStatementInfoSelfMapper.selectAccountStatementInfo(OrgNumberUtils.getOrg(),accountManagementInfos.get(i).getAccountManagementId(),accountManagementInfos.get(i).getLastStatementDate());

            if(!ObjectUtils.isEmpty(accountStatementInfo)){
                accountDebtDTO.setPaymentAmount(accountStatementInfo.getPaymentAmount());
                accountDebtDTO.setTotalDueAmount(accountStatementInfo.getTotalDueAmount());
            }

            AccountStatisticsInfo accountStatisticsInfo = accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(accountManagementInfos.get(i).getAccountManagementId(), "00000", OrgNumberUtils.getOrg());
            if(!ObjectUtils.isEmpty(accountStatisticsInfo)){
                accountDebtDTO.setBalance(accountStatisticsInfo.getBalance());
            }

            List<AccountStatisticsInfo> accountStatisticsInfos = accountStatisticsInfoSelfMapper.selectByIdAndOrgan(accountManagementInfos.get(i).getAccountManagementId(), OrgNumberUtils.getOrg());
            if(!CollectionUtils.isEmpty(accountStatisticsInfos)){
                List<TransactionTypeInfoDTO> transactionTypeInfos = new ArrayList<>();
                for (AccountStatisticsInfo accountStatisticsInfo1:accountStatisticsInfos) {
                    TransactionTypeInfoDTO transactionTypeInfo = new TransactionTypeInfoDTO();
                    transactionTypeInfo.setTransactionType(accountStatisticsInfo1.getTransactionTypeCode());

                    AccountStatisticsInfo accountStatisticsInfo2 = accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(accountManagementInfos.get(i).getAccountManagementId(), accountStatisticsInfo1.getTransactionTypeCode(), OrgNumberUtils.getOrg());

                    if(!ObjectUtils.isEmpty(accountStatisticsInfo2)){
                        transactionTypeInfo.setBalance(accountStatisticsInfo2.getBalance());
                    }

                    List<AccountBalanceInfo> accountBalanceInfos = accountBalanceInfoSelfMapper.selectByOrgMidAndTransTypeAndStatementIndicator(OrgNumberUtils.getOrg(), accountManagementInfos.get(i).getAccountManagementId(), accountStatisticsInfo1.getTransactionTypeCode(), "0");
                    if(!CollectionUtils.isEmpty(accountBalanceInfos)){
                        BigDecimal sumBalance = new BigDecimal(0);
                        for (AccountBalanceInfo accountBalanceInfo:accountBalanceInfos) {

                            sumBalance = sumBalance.add(accountBalanceInfo.getBalance()) ;
                        }
                        transactionTypeInfo.setCurrentBalance(sumBalance);
                        transactionTypeInfo.setPreviousBalance(accountStatisticsInfo2.getBalance().subtract(sumBalance));

                    }else{
                        transactionTypeInfo.setCurrentBalance(BigDecimal.ZERO);
                        transactionTypeInfo.setPreviousBalance(accountStatisticsInfo2.getBalance());
                    }
                    transactionTypeInfos.add(transactionTypeInfo);
                }
                accountDebtDTO.setTransactionTypeInfoDTOList(transactionTypeInfos);
            }


            accountDebtDTOS.add(accountDebtDTO);
            sumDebt = sumDebt.add(accountStatisticsInfo.getBalance());
        }

        //按照货币分组统计欠款
        Map<String, BigDecimal> result =
                accountDebtDTOS.stream()
                        .collect(Collectors.groupingBy(AccountDebtDTO::getCurrency,
                                Collectors.mapping(AccountDebtDTO::getBalance,
                                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        for (Map.Entry<String, BigDecimal> entry :result.entrySet()) {
            CurrencyDebtDTO currencyDebtDTO = new CurrencyDebtDTO();
                currencyDebtDTO.setCurrency(entry.getKey());
                currencyDebtDTO.setCurrencySumDebt(entry.getValue());
                currencyDebtDTOS.add(currencyDebtDTO);
        }

        customerDebtsDTO.setCurrencyDebtDTOList(currencyDebtDTOS);
        customerDebtsDTO.setSumDebt(sumDebt);
        customerDebtsDTO.setAccountDebtDTOList(accountDebtDTOS);

        return customerDebtsDTO;
    }

}
