package com.anytech.anytxn.accounting.batch.job.journal.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: sukang
 * @Date: 2022/11/24 10:43
 */
@Setter
@Getter
public class JournalEntry implements Serializable {

    private String externalId;

    private String memo;

    private Boolean approved;

    private String tranDate;

    private Subsidiary subsidiary;

    private JournalEntryLine line;


    @Getter
    public static class JournalEntryLine {

        private Integer count;

        private List<JournalEntryLineItems> items;


        public void setItems(List<JournalEntryLineItems> items) {
            this.items = items;
            this.count = CollectionUtils.size(items);
        }
    }


}




