spring:
  # 可以放在远程配置文件中
  cloud:
    nacos:
      # 配置中心
      config:
        prefix: accounting
        file-extension: yaml
        server-addr: 172.16.102.15:8848
#        namespace: d88275d4-704f-4ae2-a71e-1da125c9ee2f
        namespace: 8727313f-795b-41a7-b862-41489897cf67
        # 扩展配置(公用db)
        extension-configs[0]:
          data-id: commonDb.yaml
          refresh: true
        extension-configs[1]:
          data-id: bizDb-1.yaml
          refresh: true
        extension-configs[2]:
          data-id: rule.yaml
          refresh: true
        extension-configs[3]:
          data-id: param.yaml
          refresh: true
      discovery:
        server-addr: 172.16.102.15:8848
#        namespace: d88275d4-704f-4ae2-a71e-1da125c9ee2f
        namespace: 8727313f-795b-41a7-b862-41489897cf67
