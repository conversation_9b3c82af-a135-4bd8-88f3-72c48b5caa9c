package com.anytech.anytxn.accounting.base.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.anytech.anytxn.common.core.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 会计传票表
 * 表:ACCOUNTANT_GLVCHER
 * <AUTHOR>
 * @date 2020-02-18
 */
public class TAmsGlvcherDTO extends BaseEntity {
    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;
    private String glAcctName;

    public String getGlAcctName() {
        return glAcctName;
    }

    public void setGlAcctName(String glAcctName) {
        this.glAcctName = glAcctName;
    }
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    private String accountManagementId;

    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    private String acctLogo;

    private String acctLogoDesc;

    /**
     * 全局业务流水号
     * 表字段:GLOBAL_FLOW_NO
     */
    private String globalFlowNo;

    /**
     * 模块标识
     * 表字段:MODULE_FLAG
     */
    private String moduleFlag;

    /**
     * 交易码
     * 表字段:VP_TXN_CODE
     */
    private String vpTxnCode;

    private String vpTxnCodeDesc;

    /**
     * 币种
     * 表字段:CURR_CODE
     */
    private String currCode;

    public String getAcctLogoDesc() {
        return acctLogoDesc;
    }

    public void setAcctLogoDesc(String acctLogoDesc) {
        this.acctLogoDesc = acctLogoDesc;
    }

    public String getVpTxnCodeDesc() {
        return vpTxnCodeDesc;
    }

    public void setVpTxnCodeDesc(String vpTxnCodeDesc) {
        this.vpTxnCodeDesc = vpTxnCodeDesc;
    }

    public String getTxnCodeOrigDesc() {
        return txnCodeOrigDesc;
    }

    public void setTxnCodeOrigDesc(String txnCodeOrigDesc) {
        this.txnCodeOrigDesc = txnCodeOrigDesc;
    }

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    private String glAcct;

    /**
     * 借贷方向
     * 表字段:DRCR
     */
    private String drcr;

    /**
     * 交易金额
     * 表字段:GL_AMOUNT
     */
    private BigDecimal glAmount;

    /**
     * 入账笔数
     * 表字段:POSTING_CNT
     */
    private BigDecimal glCnt;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate postingDate;

    /**
     * 处理标识
     * 表字段:PROCESS_TYPE
     */
    private String processType;

    /**
     * 分期订单号
     * 表字段:ORDER_ID
     */
    private String orderId;

    /**
     * 资产包编号
     * 表字段:ASSET_NO
     */
    private String assetNo;

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    private String absStatus;

    /**
     * 原始交易码
     * 表字段:TXN_CODE_ORIG
     */
    private String txnCodeOrig;

    private String txnCodeOrigDesc;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

    /**
     * 渠道ID
     * 表字段:CHANNEL_ID
     */
    private String channelId;

    /**
     * 子渠道ID
     * 表字段:SUBCHANNEL_ID
     */
    private String subchannelId;

    /**
     * 资金源ID
     * 表字段:FUND_ID
     */
    private String fundId;

    /**
     * 平台ID
     * 表字段:PLATFORM_ID
     */
    private String platformId;

    /**
     * 商户ID
     * 表字段:MERCHANT_NUMBER
     */
    private String merchantNumber;

    /**
     * 五级分类标识
     */
    private String fiveTypeIndicator;

    /**
     * abs类型
     */
    private String absType;

    private Integer partitionKey;

    /**
     * 卡号
     */
    private String crdNumber;

    /**
     * 卡产品
     */
    private String crdProductNumber;

    /**
     * 卡类型
     */
    private String crdType;

    /**
     * 交易金额
     */
    private BigDecimal txnAmt;

    /**
     * 交易币种
     */
    private String txnCurrency;

    /**
     * 清算金额
     */
    private BigDecimal settleAmt;

    /**
     * 清算币种
     */
    private String settleCurrency;

    /**
     * 交易日期
     */
    private LocalDateTime txnDate;

    /**
     * 交易来源
     */
    private String txnSource;

    /**
     * 交易描述
     */
    private String txnDescription;

    /**
     * 汇率
     */
    private String currencyRate;

    /**
     * RRN
     */
    private String rrn;

    /**
     * 收单id
     */
    private String acqId;

    /**
     * TA/VA vataCoupleIndicator
     */
    private String vataCoupleIndicator;

    /**
     * TA/VA vaNumber
     */
    private String vaNumber;

    public String getVataCoupleIndicator() {
        return vataCoupleIndicator;
    }

    public void setVataCoupleIndicator(String vataCoupleIndicator) {
        this.vataCoupleIndicator = vataCoupleIndicator;
    }

    public String getVaNumber() {
        return vaNumber;
    }

    public void setVaNumber(String vaNumber) {
        this.vaNumber = vaNumber;
    }

    public String getCrdNumber() {
        return crdNumber;
    }

    public void setCrdNumber(String crdNumber) {
        this.crdNumber = crdNumber;
    }

    public String getCrdProductNumber() {
        return crdProductNumber;
    }

    public void setCrdProductNumber(String crdProductNumber) {
        this.crdProductNumber = crdProductNumber;
    }

    public String getCrdType() {
        return crdType;
    }

    public void setCrdType(String crdType) {
        this.crdType = crdType;
    }

    public BigDecimal getTxnAmt() {
        return txnAmt;
    }

    public void setTxnAmt(BigDecimal txnAmt) {
        this.txnAmt = txnAmt;
    }

    public String getTxnCurrency() {
        return txnCurrency;
    }

    public void setTxnCurrency(String txnCurrency) {
        this.txnCurrency = txnCurrency;
    }

    public BigDecimal getSettleAmt() {
        return settleAmt;
    }

    public void setSettleAmt(BigDecimal settleAmt) {
        this.settleAmt = settleAmt;
    }

    public String getSettleCurrency() {
        return settleCurrency;
    }

    public void setSettleCurrency(String settleCurrency) {
        this.settleCurrency = settleCurrency;
    }

    public LocalDateTime getTxnDate() {
        return txnDate;
    }

    public void setTxnDate(LocalDateTime txnDate) {
        this.txnDate = txnDate;
    }

    public String getTxnSource() {
        return txnSource;
    }

    public void setTxnSource(String txnSource) {
        this.txnSource = txnSource;
    }

    public String getTxnDescription() {
        return txnDescription;
    }

    public void setTxnDescription(String txnDescription) {
        this.txnDescription = txnDescription;
    }

    public String getCurrencyRate() {
        return currencyRate;
    }

    public void setCurrencyRate(String currencyRate) {
        this.currencyRate = currencyRate;
    }

    public String getRrn() {
        return rrn;
    }

    public void setRrn(String rrn) {
        this.rrn = rrn;
    }

    public String getAcqId() {
        return acqId;
    }

    public void setAcqId(String acqId) {
        this.acqId = acqId;
    }

    /**
     * 获取技术主键
     * @return id Long
     */
    public String getId() {
        return id;
    }

    /**
     * 设置技术主键
     * @param id 技术主键
     */
    public void setId(String id) {
        this.id = id;
    }
    /**
     * 获取分行号
     * @return branchid String
     */
    public String getBranchid() {
        return branchid;
    }

    /**
     * 设置分行号
     * @param branchid 分行号
     */
    public void setBranchid(String branchid) {
        this.branchid = branchid == null ? null : branchid.trim();
    }

    /**
     * 获取账户管理信息id
     * @return accountManagementId String
     */
    public String getAccountManagementId() {
        return accountManagementId;
    }

    /**
     * 设置账户管理信息id
     * @param accountManagementId 账户管理信息id
     */
    public void setAccountManagementId(String accountManagementId) {
        this.accountManagementId = accountManagementId == null ? null : accountManagementId.trim();
    }

    /**
     * 获取产品代码
     * @return acctLogo String
     */
    public String getAcctLogo() {
        return acctLogo;
    }

    /**
     * 设置产品代码
     * @param acctLogo 产品代码
     */
    public void setAcctLogo(String acctLogo) {
        this.acctLogo = acctLogo == null ? null : acctLogo.trim();
    }

    /**
     * 获取全局业务流水号
     * @return globalFlowNo String
     */
    public String getGlobalFlowNo() {
        return globalFlowNo;
    }

    /**
     * 设置全局业务流水号
     * @param globalFlowNo 全局业务流水号
     */
    public void setGlobalFlowNo(String globalFlowNo) {
        this.globalFlowNo = globalFlowNo == null ? null : globalFlowNo.trim();
    }

    /**
     * 获取模块标识
     * @return moduleFlag String
     */
    public String getModuleFlag() {
        return moduleFlag;
    }

    /**
     * 设置模块标识
     * @param moduleFlag 模块标识
     */
    public void setModuleFlag(String moduleFlag) {
        this.moduleFlag = moduleFlag == null ? null : moduleFlag.trim();
    }

    /**
     * 获取交易码
     * @return vpTxnCode String
     */
    public String getVpTxnCode() {
        return vpTxnCode;
    }

    /**
     * 设置交易码
     * @param vpTxnCode 交易码
     */
    public void setVpTxnCode(String vpTxnCode) {
        this.vpTxnCode = vpTxnCode == null ? null : vpTxnCode.trim();
    }

    /**
     * 获取币种
     * @return currCode String
     */
    public String getCurrCode() {
        return currCode;
    }

    /**
     * 设置币种
     * @param currCode 币种
     */
    public void setCurrCode(String currCode) {
        this.currCode = currCode == null ? null : currCode.trim();
    }

    /**
     * 获取科目号
     * @return glAcct String
     */
    public String getGlAcct() {
        return glAcct;
    }

    /**
     * 设置科目号
     * @param glAcct 科目号
     */
    public void setGlAcct(String glAcct) {
        this.glAcct = glAcct == null ? null : glAcct.trim();
    }

    /**
     * 获取借贷方向
     * @return drcr String
     */
    public String getDrcr() {
        return drcr;
    }

    /**
     * 设置借贷方向
     * @param drcr 借贷方向
     */
    public void setDrcr(String drcr) {
        this.drcr = drcr == null ? null : drcr.trim();
    }

    /**
     * 获取交易金额
     * @return glAmount BigDecimal
     */
    public BigDecimal getGlAmount() {
        return glAmount;
    }

    /**
     * 设置交易金额
     * @param glAmount 交易金额
     */
    public void setGlAmount(BigDecimal glAmount) {
        this.glAmount = glAmount;
    }

    /**
     * 获取入账日期
     * @return postingDate LocalDate
     */
    public LocalDate getPostingDate() {
        return postingDate;
    }

    /**
     * 设置入账日期
     * @param postingDate 入账日期
     */
    public void setPostingDate(LocalDate postingDate) {
        this.postingDate = postingDate;
    }

    /**
     * 获取处理标识
     * @return processType String
     */
    public String getProcessType() {
        return processType;
    }

    /**
     * 设置处理标识
     * @param processType 处理标识
     */
    public void setProcessType(String processType) {
        this.processType = processType == null ? null : processType.trim();
    }

    /**
     * 获取分期订单号
     * @return orderId String
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置分期订单号
     * @param orderId 分期订单号
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * 获取资产包编号
     * @return assetNo String
     */
    public String getAssetNo() {
        return assetNo;
    }

    /**
     * 设置资产包编号
     * @param assetNo 资产包编号
     */
    public void setAssetNo(String assetNo) {
        this.assetNo = assetNo == null ? null : assetNo.trim();
    }

    /**
     * 获取资产出表状态
     * @return absStatus String
     */
    public String getAbsStatus() {
        return absStatus;
    }

    /**
     * 设置资产出表状态
     * @param absStatus 资产出表状态
     */
    public void setAbsStatus(String absStatus) {
        this.absStatus = absStatus == null ? null : absStatus.trim();
    }

    /**
     * 获取原始交易码
     * @return txnCodeOrig String
     */
    public String getTxnCodeOrig() {
        return txnCodeOrig;
    }

    /**
     * 设置原始交易码
     * @param txnCodeOrig 原始交易码
     */
    public void setTxnCodeOrig(String txnCodeOrig) {
        this.txnCodeOrig = txnCodeOrig == null ? null : txnCodeOrig.trim();
    }

    /**
     * 获取创建日期
     * @return createTime LocalDateTime
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建日期
     * @param createTime 创建日期
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新日期
     * @return updateTime LocalDateTime
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新日期
     * @param updateTime 更新日期
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新用户
     * @return updateBy String
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新用户
     * @param updateBy 更新用户
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    /**
     * 获取版本号
     * @return versionNumber Long
     */
    public Long getVersionNumber() {
        return versionNumber;
    }

    /**
     * 设置版本号
     * @param versionNumber 版本号
     */
    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }

    /**
     * 获取渠道ID
     * @return channelId String
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * 设置渠道ID
     * @param channelId 渠道ID
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId == null ? null : channelId.trim();
    }

    /**
     * 获取子渠道ID
     * @return subchannelId String
     */
    public String getSubchannelId() {
        return subchannelId;
    }

    /**
     * 设置子渠道ID
     * @param subchannelId 子渠道ID
     */
    public void setSubchannelId(String subchannelId) {
        this.subchannelId = subchannelId == null ? null : subchannelId.trim();
    }

    /**
     * 获取资金源ID
     * @return fundId String
     */
    public String getFundId() {
        return fundId;
    }

    /**
     * 设置资金源ID
     * @param fundId 资金源ID
     */
    public void setFundId(String fundId) {
        this.fundId = fundId == null ? null : fundId.trim();
    }

    /**
     * 获取平台ID
     * @return platformId String
     */
    public String getPlatformId() {
        return platformId;
    }

    /**
     * 设置平台ID
     * @param platformId 平台ID
     */
    public void setPlatformId(String platformId) {
        this.platformId = platformId == null ? null : platformId.trim();
    }

    /**
     * 获取商户ID
     * @return merchantNumber String
     */
    public String getMerchantNumber() {
        return merchantNumber;
    }

    /**
     * 设置商户ID
     * @param merchantNumber 商户ID
     */
    public void setMerchantNumber(String merchantNumber) {
        this.merchantNumber = merchantNumber == null ? null : merchantNumber.trim();
    }

    public String getFiveTypeIndicator() {
        return fiveTypeIndicator;
    }

    public void setFiveTypeIndicator(String fiveTypeIndicator) {
        this.fiveTypeIndicator = fiveTypeIndicator;
    }

    public String getAbsType() {
        return absType;
    }

    public void setAbsType(String absType) {
        this.absType = absType;
    }

    public Integer getPartitionKey() {
        return partitionKey;
    }

    public void setPartitionKey(Integer partitionKey) {
        this.partitionKey = partitionKey;
    }

    public BigDecimal getGlCnt() {
        return glCnt;
    }

    public void setGlCnt(BigDecimal glCnt) {
        this.glCnt = glCnt;
    }
}
