package com.anytech.anytxn.account.base.service;

import com.anytech.anytxn.business.base.account.domain.dto.CustomerDebtsDTO;

/**
 * 客户欠款Service
 * <AUTHOR>
 * @date 2020-10-21-17:35
 **/
public interface ICustomerDebtService {

    /**
     * 根据查询类型(客户号:C  证件号:I   EcIf号:E)查询客户欠款信息
     * @param searchType  searchType
     * @param number number
     * @return CustomerDebtsDTO
     */
    CustomerDebtsDTO getCustomerDebtBySearchCondition(String searchType, String number);
}
