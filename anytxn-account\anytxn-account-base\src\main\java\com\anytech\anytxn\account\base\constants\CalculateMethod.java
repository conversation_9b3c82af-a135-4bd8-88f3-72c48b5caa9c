package com.anytech.anytxn.account.base.constants;

/**
 * 滞纳金计算方式
 *
 * @Author: <PERSON><PERSON>
 * @Date: 2018/9/21 19:41
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 */
public enum CalculateMethod {

    /**
     * 0：以X-DAYS past due 金额为计算金额
     * 1：以所有past due 金额加总为计算金额
     */
    CALCULATE_METHOD_XDAYS("0","以X-DAYS金额为计算金额"),
    CALCULATE_METHOD_ALL("1","所有pastDue金额加总为计算金额");

    private String code;
    private String desc;

    CalculateMethod(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
