package com.anytech.anytxn.accounting.base.enums;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/10/16
 */
public enum CurrencyCodeEnum {
    RMB("156","RMB", "1.000"),
    USD("840","USD", "7.000");

    private String code;
    private String des;
    private String rate;

    CurrencyCodeEnum(String code, String des, String rate) {
        this.code = code;
        this.des = des;
        this.rate = rate;
    }

    public String getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public String getRate() {
        return rate;
    }

    public static String getDes(String code){
        for (CurrencyCodeEnum value : CurrencyCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDes();
            }
        }
        return "RMB";
    }

    public static String getRat(String code){
        for (CurrencyCodeEnum value : CurrencyCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getRate();
            }
        }
        return "1.000";
    }

    public static BigDecimal getRel(String code, BigDecimal amount){
        float v = Float.parseFloat(getRat(code));
        return amount.multiply(new BigDecimal(v));
    }
}
