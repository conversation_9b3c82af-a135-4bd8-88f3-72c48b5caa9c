package com.anytech.anytxn.account.job.incollection.config;

import com.anytech.anytxn.account.job.incollection.step.IncollectionProcessor;
import com.anytech.anytxn.account.job.incollection.step.IncollectionReader;
import com.anytech.anytxn.business.base.account.domain.dto.InCollectionDTO;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.file.FlatFileItemWriter;
import org.springframework.batch.item.file.transform.BeanWrapperFieldExtractor;
import org.springframework.batch.item.file.transform.DelimitedLineAggregator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.FileSystemResource;

import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 * @date 2019-09-02
 **/
@Configuration
public class IncollectionBatchConfig {

    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;

    @Value("${anytxn.batch.stmtProcessJob.stmtMasterStep.grid-size:8}")
    private Integer gridSize;

    @Value("${anytxn.batch.stmtProcessJob.stmtStep.chunk-limit:1000}")
    private Integer chunkLimit;

    @Value("${anytxn.batch.stmtProcessJob.stmtStep.page-size:1000}")
    private Integer pageSize;

    /**
     * 催收批量 Job配置
     * @param step
     * @return
     */
    @Bean
    public Job incollectionJob(@Qualifier("incollectionStep") Step step) {
        return jobs.get("incollectionJob")
                .start(step)
                .build();
    }

    /**
     * 催收批量 step
     * @param incollectionReader
     * @param incollectionProcessor
     * @return
     */
    @Bean
    public Step incollectionStep(
            @Qualifier("incollectionReader") IncollectionReader incollectionReader,
            @Qualifier("incollectionProcessor") IncollectionProcessor incollectionProcessor) {
        return steps.get("incollectionStep").<AccountManagementInfo, InCollectionDTO>chunk(chunkLimit)
                .reader(incollectionReader).processor(incollectionProcessor)
                .writer(getItemWriter()).build();
    }

    @Bean
    @StepScope
    public IncollectionReader incollectionReader(@Qualifier("businessDataSource") DataSource dataSource) {
        IncollectionReader reader = new IncollectionReader(dataSource);
        reader.setDataSource(dataSource);
        reader.setPageSize(pageSize);
        //记录状态信息和断点
        reader.setSaveState(true);
        return reader;
    }

    @Bean
    @StepScope
    public IncollectionProcessor incollectionProcessor() {
        return new IncollectionProcessor();
    }

    @Bean
    public FlatFileItemWriter<InCollectionDTO> getItemWriter() {
        FlatFileItemWriter<InCollectionDTO> txtItemWriter = new FlatFileItemWriter<>();
        txtItemWriter.setShouldDeleteIfExists(true);
        txtItemWriter.setEncoding("UTF-8");
        txtItemWriter.setResource(new FileSystemResource("/home/<USER>/collection/collection.txt"));
        txtItemWriter.setLineAggregator(
                new DelimitedLineAggregator<InCollectionDTO>() {
                    {
                        setDelimiter("/");
                        setFieldExtractor(
                                new BeanWrapperFieldExtractor<InCollectionDTO>() {
                                    {
                                        setNames(
                                                new String[]{
                                                        "collectionFlag",
                                                        "accountManagementId",
                                                        "cardNumber",
                                                        "balance",
                                                        "dueAmount",
                                                        "customerNumber",
                                                        "customerName",
                                                        "idType",
                                                        "idNumber",
                                                        "inCollectionDate",
                                                        "outCollectionDate"
                                                });
                                    }
                                });
                    }
                });
        return txtItemWriter;
    }
}
