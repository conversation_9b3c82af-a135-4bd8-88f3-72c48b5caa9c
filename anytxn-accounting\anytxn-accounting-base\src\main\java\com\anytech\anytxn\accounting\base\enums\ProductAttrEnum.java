package com.anytech.anytxn.accounting.base.enums;

/**
 * <AUTHOR>
 * @date 2020/9/10
 */
public enum  ProductAttrEnum {
    U("U", "贷记卡产品"),
    A("A","准贷记卡产品");


    ProductAttrEnum(String code, String des) {
        this.code = code;
        this.des = des;
    }

    private String code;
    private String des;

    public String getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }
}
