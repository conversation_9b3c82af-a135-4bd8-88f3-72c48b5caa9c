package com.anytech.anytxn.accounting.batch.job.report.step.installbalance.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/9/16
 */
@Getter
@Setter
public class InstallBalanceReportDetail {

    /**
     * 分行号
     */
    private String branchNumber;

    /**
     * 分期产品ID
     */
    private String productID;

    /**
     * 分期产品描述
     */
    private String productDetailed;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     *  余额
     */
    private BigDecimal balance;
}
