package com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement.dto.rate;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement.model.CardSettleReport;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 每月分期付款出账报表
 * <AUTHOR>
 * @Date 2020/9/11 11:43
 */
@Getter
@Setter
public class RateCardSettlement {

    private List<RateCardSettlementSub> list;
    //总数
    private Integer total;


    public static RateCardSettlement resetReportData(List<CardSettleReport> data, TimeTypeEnum timeTypeEnum){
        RateCardSettlement head = new RateCardSettlement();
        head.setTotal(data.size());
        List<RateCardSettlementSub> counts = Lists.newArrayList();
        head.setList(counts);

        Map<String, List<CardSettleReport>> group = data.stream().collect(Collectors.groupingBy(x -> x.getTransactionSource() + x.getSettleCurrCode()));
        int i = 0;
        for (Map.Entry<String, List<CardSettleReport>> entry : group.entrySet()) {
            List<CardSettleReport> value = entry.getValue();

            RateCardSettlementSub sub = new RateCardSettlementSub();
            counts.add(sub);

            sub.setChannel(value.get(0).getTransactionSource());
            sub.setSettlementCurrencyCode(value.get(0).getSettleCurrCode());
            sub.setSubCount(value.size());
            sub.setAmountTotal(value.stream().map(CardSettleReport::getSettleAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            sub.setEnd(++i == group.size());
            List<RateCardSettlementDetail> details = Lists.newArrayList();
            sub.setDetailList(details);

            for (CardSettleReport report : value) {
                RateCardSettlementDetail detail = new RateCardSettlementDetail();
                detail.setChannel(report.getTransactionSource());
                detail.setCurrencyConversionRate(report.getRate());
                detail.setPostDate(TimeUtils.dateToStr(report.getPostDate(), timeTypeEnum));
                detail.setRmbAmount(report.getRmbAmount());
                detail.setSettlementAmount(report.getSettleAmount());
                detail.setSettlementCurrencyCode(report.getSettleCurrCode());
                detail.setTransactionCode(report.getTxnCodeOrig());
                detail.setTransactionCodeDesc(report.getDescription());
                detail.setSettlementCount(report.getGroupSize());

                details.add(detail);
            }
        }

        return head;
    }
}
