package com.anytech.anytxn.account.service;

import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.base.service.AutoEbitSignUpInforService;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import com.anytech.anytxn.business.dao.card.mapper.AutoEbitSignUpInforMapper;
import com.anytech.anytxn.parameter.base.account.service.IStatementProcessService;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDate;

/**
 * 扣款签约信息
 * <AUTHOR>
 * @date 2021-05-13 11:41
 **/
@Service
@Slf4j
public class AutoEbitSignUpInforServiceImpl implements AutoEbitSignUpInforService {

    @Resource
    private AutoEbitSignUpInforMapper autoEbitSignUpInforMapper;

    @Resource
    private IOrganizationInfoService organizationInfoService;
    @Resource
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Resource
    private IProductInfoService productInfoService;
    @Resource
    private IStatementProcessService statementProcessService;
    @Autowired
    private Number16IdGen number16IdGen;
  /*  @Override
    public int addAutoInfo(AutoEbitSignUpInfor autoEbitSignUpInfor) {
        int insert = 0;
        autoEbitSignUpInfor.setId(number16IdGen.generateId(TenantUtils.getTenantId())+"");
        autoEbitSignUpInfor.setOrganizationNumber(OrgNumberUtils.getOrg());
        AutoEbitSignUpInfor autoEbitSignUpInfor1 = autoEbitSignUpInforMapper.selectByCustomerAndOrgAndContractType(autoEbitSignUpInfor.getCustomerId(), autoEbitSignUpInfor.getOrganizationNumber());
        if (ObjectUtils.isEmpty(autoEbitSignUpInfor1)){
            try {
                insert = autoEbitSignUpInforMapper.insertSelective(autoEbitSignUpInfor);
            } catch (Exception e) {
                log.error("存在客户号:{},机构号:{}且为合约状态的记录",autoEbitSignUpInfor.getCustomerId(),autoEbitSignUpInfor.getOrganizationNumber());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR);             }
        }else {
            autoEbitSignUpInfor1.setContractType("1");
            try {
                insert = autoEbitSignUpInforMapper.updateByPrimaryKeySelective(autoEbitSignUpInfor1);
            } catch (Exception e) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR);             }
            try {
                insert = autoEbitSignUpInforMapper.insert(autoEbitSignUpInfor);
            } catch (Exception e) {
                log.error("自动扣款类型不是0-约定自扣 而且 合约类型不是0-签约 ");
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR);             }
        }
        return insert;
    }

    @Override
    public int updateAutoInfo(AutoEbitSignUpInfor autoEbitSignUpInfor) {
        int i = 0;
        AutoEbitSignUpInfor autoEbitSignUpInfor1 = autoEbitSignUpInforMapper.selectByPrimaryKey(autoEbitSignUpInfor.getId());
        autoEbitSignUpInfor1.setContractType("1");
        if ("1".equals(autoEbitSignUpInfor.getContractType())){
           i = autoEbitSignUpInforMapper.updateByPrimaryKeySelective(autoEbitSignUpInfor1);
        }else {
            i = autoEbitSignUpInforMapper.updateByPrimaryKeySelective(autoEbitSignUpInfor1);
            addAutoInfo(autoEbitSignUpInfor);
        }
        return i;
    }

    @Override
    public void selectByCustid(AutoEbitSignUpInfor autoEbitSignUpInfor) {
        AutoEbitSignUpInfor autoEbitSignUpInfor1 = autoEbitSignUpInforMapper.selectByCustomerAndOrgAndContractType(autoEbitSignUpInfor.getCustomerId(), autoEbitSignUpInfor.getOrganizationNumber());
        if (ObjectUtils.isNotEmpty(autoEbitSignUpInfor1)){
            autoEbitSignUpInfor.setId(autoEbitSignUpInfor1.getId());
            updateAutoInfo(autoEbitSignUpInfor);
        }else{
            addAutoInfo(autoEbitSignUpInfor);
        }
    }

    @Override
    public AutoEbitSignUpInfor findStaById(String contractType, String customerId, String autoDebitType) {
        log.info("根据客户号和合约信息查询自动扣款签约信息表,合约类型:{} 自动扣款类型:{}", contractType, autoDebitType);
        try {
            AutoEbitSignUpInfor autoEbitSignUpInfor = autoEbitSignUpInforMapper.selectByIdAndOrg(customerId, autoDebitType, contractType, OrgNumberUtils.getOrg());

            if (ObjectUtils.isEmpty(autoEbitSignUpInfor)){
                return null;
            }
            return autoEbitSignUpInfor;
        } catch (Exception e) {
            log.error("调用[{}]查询数据库表[{}]失败","selectByIdAndOrg", "AUTO_EBIT_SIGN_UP_INFOR", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
    }

    @Override
    public int addAutoTempInfo(AutoEbitSignUpInfor autoEbitSignUpInfor) {
        int insert = 0;
        autoEbitSignUpInfor.setId(number16IdGen.generateId(TenantUtils.getTenantId())+"");
        autoEbitSignUpInfor.setOrganizationNumber(OrgNumberUtils.getOrg());
        //机构信息
        OrganizationInfoResDTO organizationNumberInfo = getOrganizationNumberInfo(autoEbitSignUpInfor.getOrganizationNumber());
        //账户账单信息
        LocalDate firstPostDate = getAccountStateInfo(autoEbitSignUpInfor,organizationNumberInfo);
        if ("1".equals(autoEbitSignUpInfor.getAutoDebitType()) && "0".equals(autoEbitSignUpInfor.getContractType())){
            if (autoEbitSignUpInfor.getTemporaryAutoDebitOperationDate().isAfter(organizationNumberInfo.getNextProcessingDay())){
                if (autoEbitSignUpInfor.getTemporaryAutoDebitOperationDate().isBefore(firstPostDate)){
                    insert = autoEbitSignUpInforMapper.insert(autoEbitSignUpInfor);
                }else {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_ERR, AccountingRepDetailEnum.ST_AL);
                }
            }else {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_ERR, AccountingRepDetailEnum.OR_AL);
            }
        }else {
            log.error("动扣款类型不是 1-临时自扣 而且 合约类型不是0-签约 ");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR);
        }
        return insert;
    }

    @Override
    public int updateAutoTempInfo(AutoEbitSignUpInfor autoEbitSignUpInfor) {
        int i=0;
        AutoEbitSignUpInfor autoEbitSignUpInfor1 = autoEbitSignUpInforMapper.selectByPrimaryKey(autoEbitSignUpInfor.getId());
        autoEbitSignUpInfor1.setContractType("1");
        if ("1".equals(autoEbitSignUpInfor.getContractType())){
            i = autoEbitSignUpInforMapper.updateByPrimaryKeySelective(autoEbitSignUpInfor1);
        }else{
            i = autoEbitSignUpInforMapper.updateByPrimaryKeySelective(autoEbitSignUpInfor1);
            addAutoTempInfo(autoEbitSignUpInfor);
        }
        return i;
    }
*/
    /**
     * 机构信息
     * @param organizationNumber
     * @return
     */
    private OrganizationInfoResDTO getOrganizationNumberInfo(String organizationNumber) {

        OrganizationInfoResDTO orgconfig = organizationInfoService.findOrganizationInfo(organizationNumber);
        if (orgconfig == null) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.GMP);
        }
        return orgconfig;
    }
   /* private LocalDate getAccountStateInfo(AutoEbitSignUpInfor autoEbitSignUpInfor ,OrganizationInfoResDTO organizationInfoResDTO){
        LocalDate firstPostDate;
        List<AccountManagementInfo> accountManagementInfos = accountManagementInfoMapper.selectByCustomerIdAndOrganizationNumber(autoEbitSignUpInfor.getCustomerId(), autoEbitSignUpInfor.getOrganizationNumber());
        if (CollectionUtils.isEmpty(accountManagementInfos)){
            log.error("通过客户号:{},机构号:{}查询管理账户信息无数据",autoEbitSignUpInfor.getCustomerId(),autoEbitSignUpInfor.getOrganizationNumber());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.MA_E);
        }
        List<ProductInfoResDTO> productInfoList = productInfoService.findProductInfo(accountManagementInfos.get(0).getOrganizationNumber(), accountManagementInfos.get(0).getProductNumber(), accountManagementInfos.get(0).getCurrency());

        //账单日
        Short cycleDay = accountManagementInfos.get(0).getCycleDay();
        LocalDate statementDate = calculateStatementDate(organizationInfoResDTO.getNextProcessingDay(), cycleDay);
        StatementProcessResDTO statementParam = statementProcessService.findByOrgAndTableId(organizationInfoResDTO.getOrganizationNumber(), productInfoList.get(0).getStatementProcessingTableId());
        firstPostDate = statementDate.plusDays(statementParam.getDueDays());

//        AccountStatementInfo statementInfo = accountStatementInfoSelfMapper.selectByAccountManagementIdAndDate
//                (accountManagementInfos.get(0).getAccountManagementId(), accountManagementInfos.get(0).getLastStatementDate());
//        if (ObjectUtils.isEmpty(statementInfo)){
//            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.ST_BJ);
//        }
//        return statementInfo;
        return firstPostDate;
    }*/
    /**
     * 下一账单日
     *
     * @param nextProcessingDay 下一处理日
     * @param cycleDate         账单日
     * @return 下一账单日
     */
    private LocalDate calculateStatementDate(LocalDate nextProcessingDay, short cycleDate) {
        int month = 0;
        int year = 0;
        if (nextProcessingDay.getDayOfMonth() > cycleDate) {
            month = nextProcessingDay.getMonthValue() + 1;
        } else {
            month = nextProcessingDay.getMonthValue();
        }
        year = nextProcessingDay.getYear();
        if (month >= 13) {
            month = month - 12;
            year = year + 1;
        }
        return LocalDate.of(year, month, cycleDate);
    }

    @Override
    public int addAutoInfo(AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        return 0;
    }

    @Override
    public int updateAutoInfo(AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        return 0;
    }

    @Override
    public void selectByCustid(AutoEbitSignUpInforDTO autoEbitSignUpInfor) {

    }

    @Override
    public AutoEbitSignUpInforDTO findStaById(String contractType, String customerId, String autoDebitType) {
        return null;
    }

    @Override
    public int addAutoTempInfo(AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        return 0;
    }

    @Override
    public int updateAutoTempInfo(AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        return 0;
    }
}
