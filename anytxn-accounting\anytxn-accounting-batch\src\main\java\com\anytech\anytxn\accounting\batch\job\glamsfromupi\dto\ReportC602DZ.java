package com.anytech.anytxn.accounting.batch.job.glamsfromupi.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.LinkedList;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class ReportC602DZ {
    /**
     * 机构标识码
     * 表字段:IIN
     */
    private String iin;

    /**
     * 机构名称
     * 表字段:Institution_Name
     */
    private String institutionName;

    /**
     * 交易清算日期
     * 表字段:Transaction_Settlement_Date
     */
    private LocalDate transactionSettlementDate;

    /**
     * 报表日期
     * 表字段:Report_Date
     */
    private LocalDate reportDate;

    /**
     * 交易币种
     * 表字段:Transaction_Currency
     */
    private String transactionCurrency;

    /**
     * 清算币种
     * 表字段:Settlement_Currency
     */
    private String settlementCurrency;

    private LinkedList<DetailC602DZ> c602dz01;
    private LinkedList<DetailC602DZ> c602dz02;
    private LinkedList<DetailC602DZ> c602dz03;
    private LinkedList<DetailC602DZ004> c602dz04;
}
