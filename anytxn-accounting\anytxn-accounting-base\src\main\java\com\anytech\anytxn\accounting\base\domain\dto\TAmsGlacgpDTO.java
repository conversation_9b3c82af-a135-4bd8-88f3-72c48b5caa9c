package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 *
 */
@Setter
@Getter
@ToString
public class TAmsGlacgpDTO extends BaseEntity implements Serializable {

    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;

    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 年
     * 表字段:GL_YEAR
     */
    private String glYear;

    /**
     * 月
     * 表字段:GL_MONTH
     */
    private String glMonth;

    /**
     * 开始日期
     * 表字段:GL_PERIOD_START
     */
    private LocalDate glPeriodStart;

    /**
     * 结束日期
     * 表字段:GL_PERIOD_END
     */
    private LocalDate glPeriodEnd;

    /**
     * 状态
     * 表字段:GL_PERIOD_STATUS
     */
    private String glPeriodStatus;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;
}
