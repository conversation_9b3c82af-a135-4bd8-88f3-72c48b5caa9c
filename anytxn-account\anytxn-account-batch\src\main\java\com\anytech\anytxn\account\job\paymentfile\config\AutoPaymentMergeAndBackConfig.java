package com.anytech.anytxn.account.job.paymentfile.config;

import com.anytech.anytxn.account.config.AutoPaymentFileConfigurer;
import com.anytech.anytxn.account.job.paymentfile.step.FileBackTasklet;
import com.anytech.anytxn.account.job.paymentfile.step.auto.AutoFileMergeTasklet;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;
import java.io.File;
import java.time.format.DateTimeFormatter;

/**
 * 约定扣款文件合并与备份处理
 * <AUTHOR>
 * @date 2021/3/24
 */
@Configuration
@Slf4j
public class AutoPaymentMergeAndBackConfig {

    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;

    @Autowired
    private AutoPaymentFileConfigurer autoPaymentFileConfigurer;

    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Resource(name = "autoPaymentFilePathConfig")
    private AnytxnFilePathConfig pathConfigInput;

    @Resource(name = "outPutFilePathConfig")
    private AnytxnFilePathConfig pathConfigOutput;

    @Value("${data-id:-1}")
    private String dataId;

    @Resource(name = "okFileStep")
    private Step okFileStep;

    /**
     * 扣款分区文件合并（仅合并分片，不同机构合并不同文件）
     */
    @Bean
    public Job autoPaymentFileCommonJob(@Qualifier("autoFileMergeStep") Step step,
                                       @Qualifier("autoFileBackStep") Step step2) {
        return jobs.get("autoPaymentFileCommonJob")
                .start(step)
                .next(okFileStep)
                .next(step2)
                .build();
    }

    @Bean
    public Step autoFileMergeStep(
            @Qualifier("autoFileMergeTasklet") Tasklet autoFileMergeTasklet) {
        return steps.get("autoFileMergeStep").tasklet(autoFileMergeTasklet).build();
    }

    @Bean
    public Tasklet autoFileMergeTasklet(){
        String shardingPath = pathConfigInput.getShardingPath();
        return new AutoFileMergeTasklet(shardingPath,  pathConfigOutput.getCommonPath(), joinFileName());
    }

    @Bean
    public Step autoFileBackStep(
            @Qualifier("autoFileBackTasklet") Tasklet autoFileBackTasklet) {
        return steps.get("autoFileBackStep").tasklet(autoFileBackTasklet).build();
    }

    @Bean
    public FileBackTasklet autoFileBackTasklet(){
        return new FileBackTasklet(joinOutFilePath(), joinOutFileBackPath());
    }

    private String joinFileName(){
        String orgNum = OrgNumberUtils.getOrg();
        ParmOrganizationInfo org = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(orgNum);
        // 文件名称由【机构号-文件名-日期】组成
        String today = org.getToday().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return org.getOrganizationNumber() + "-" + autoPaymentFileConfigurer.getFileName() + "-" + today;
    }

    private String joinOutFilePath(){
        // 拼接读取文件目录：前缀/分片号/机构号/文件名
        String pathTrue = pathConfigInput.getShardingPath(Integer.parseInt(getDusId()));
        log.info("目标路径{}",pathTrue + File.separator + joinFileName());
        return pathTrue + File.separator + joinFileName();
    }

    private String joinOutFileBackPath(){
        // 拼接读取文件目录：前缀/分片号/机构号/文件名
        String backPath = pathConfigInput.getShardingBackPath(Integer.parseInt(getDusId()));
        log.info("备份文件完整目录为: {}", backPath + File.separator +  joinFileName());
        return backPath + File.separator + joinFileName();
    }

    private String getDusId(){
        if (dataId.contains(".")) {
            dataId = dataId.split("\\.")[0];
        }
        if (dataId.contains("-")){
            dataId = dataId.split("-")[1];
        }
        return dataId;
    }
}
