package com.anytech.anytxn.accounting.batch.job.report.step.installmonth.dto;

import com.anytech.anytxn.accounting.batch.job.report.step.installmonth.model.InstallMonthReport;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 每月分期付款出账报表
 * <AUTHOR>
 * @Date 2020/9/11 11:43
 */
@Getter
@Setter
public class OrderPlan {
    private List<OrderPlanSub> list;
    //总数
    private Integer total;

    /**
     * 报表数据转换
     * @return
     */
    public static OrderPlan resetReportData(List<InstallMonthReport> data){
        // 总数
        OrderPlan plan = new OrderPlan();
        plan.setTotal(data.size());

        // 分组
        List<OrderPlanSub> orderPlanSubs = Lists.newArrayList();
        plan.setList(orderPlanSubs);

        Map<String, List<InstallMonthReport>> group = data.stream().collect(Collectors.groupingBy(x -> x.getProductCode() + x.getCurrCode()));
        int i = 0;
        for (Map.Entry<String, List<InstallMonthReport>> entry : group.entrySet()) {
            List<InstallMonthReport> value = entry.getValue();
            InstallMonthReport reportData = value.get(0);

            OrderPlanSub orderPlanSub = new OrderPlanSub();
            orderPlanSub.setCurrencyCode(Integer.parseInt(reportData.getCurrCode()));
            orderPlanSub.setProductId(reportData.getProductCode());
            orderPlanSub.setInstallCount(value.size());
            orderPlanSub.setInstallAmountTotal(value.stream().map(InstallMonthReport::getTermAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            orderPlanSub.setInstallFeeTotal(value.stream().map(InstallMonthReport::getFreeAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            orderPlanSub.setEnd(++i == group.size());
            orderPlanSubs.add(orderPlanSub);

            List<OrderPlanDetail> orderPlanDetails = Lists.newArrayList();
            orderPlanSub.setDetailList(orderPlanDetails);

            for (InstallMonthReport reportDTO : value) {
                OrderPlanDetail detail = new OrderPlanDetail();
                detail.setCardNumber(reportDTO.getCardNumber());
                detail.setOrderNo(reportDTO.getOrderId());
                detail.setAmount(reportDTO.getTermAmount());
                detail.setCurrencyCode(Integer.parseInt(reportDTO.getCurrCode()));
                detail.setFee(reportDTO.getFreeAmount());
                detail.setPostDate(reportDTO.getTermPostDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                detail.setProductId(reportDTO.getProductCode());
                detail.setProductDesc(reportDTO.getDescription());

                orderPlanDetails.add(detail);
            }
        }

        return plan;
    }
}
