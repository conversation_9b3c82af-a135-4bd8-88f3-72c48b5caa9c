spring:
  # 可以放在远程配置文件中
  cloud:
    nacos:
      # 配置中心
      config:
        prefix: accountingBatch
        file-extension: yaml
        server-addr: ${server-addr}
        namespace: ${namespace}
        # 扩展配置(公用db)
        extension-configs[0]:
          data-id: commonDb.yaml
          refresh: true
        extension-configs[1]:
          data-id: ${data-id}
#          data-id: bizDb-2.yaml
          refresh: true
        extension-configs[2]:
          data-id: cache.yaml
          refresh: true
        extension-configs[3]:
          data-id: commonDb-batch.yaml
        extension-configs[4]:
          data-id: custIdMapping.yaml
        extension-configs[5]:
          data-id: file.yaml
        extension-configs[6]:
          data-id: redisson.yaml
          refresh: true
      discovery:
        server-addr: ${server-addr}
        namespace: ${namespace}