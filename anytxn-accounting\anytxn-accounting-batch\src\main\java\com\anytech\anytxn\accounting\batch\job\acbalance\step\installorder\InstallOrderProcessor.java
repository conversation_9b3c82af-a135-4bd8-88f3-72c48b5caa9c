package com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder;

import com.anytech.anytxn.accounting.base.service.IBalService;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlinsbalDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2020/4/3
 */
public class InstallOrderProcessor implements ItemProcessor<InstallOrderDTO, AccountantGlinsbalDTO> {

    @Autowired
    private IBalService balService;

    @Override
    public AccountantGlinsbalDTO process(InstallOrderDTO item) throws Exception {
        return balService.installOrderSum(item);
    }
}
