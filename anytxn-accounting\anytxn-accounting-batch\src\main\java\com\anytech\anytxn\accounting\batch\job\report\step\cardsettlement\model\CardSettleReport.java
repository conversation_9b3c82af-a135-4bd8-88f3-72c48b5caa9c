package com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2020/9/13
 */
@Getter
@Setter
public class CardSettleReport {

    /**
     * 交易来源
     */
    private String transactionSource;

    /**
     * 交易码
     */
    private String txnCodeOrig;

    /**
     * 描述
     */
    private String description;

    /**
     * 入账日期
     */
    private LocalDate postDate;

    /**
     * 清算金额
     */
    private BigDecimal settleAmount;

    /**
     * 清算币种
     */
    private String settleCurrCode;

    /**
     * 每组数据量
     */
    private Integer groupSize;

    /**
     * 汇率
     */
    private BigDecimal rate;

    /**
     * 折合人民币金额
     */
    private BigDecimal rmbAmount;
//
//    public List<CardSettleReport> group(List<CardSettleReport> data){
//        List<CardSettleReport> result = Lists.newArrayList();
//
//        Map<String, List<CardSettleReport>> group = data.stream().collect(Collectors.groupingBy(x -> x.getTransactionSource() + x.getTxnCodeOrig() + x.getSettleCurrCode()));
//        for (Map.Entry<String, List<CardSettleReport>> entry : group.entrySet()) {
//            List<CardSettleReport> value = entry.getValue();
//            CardSettleReport temple = value.get(0);
//
//            temple.setSettleAmount(value.stream().map(CardSettleReport::getSettleAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
//            temple.setGroupSize(value.stream().map(CardSettleReport::getGroupSize).reduce(0, Integer::sum));
//            temple.setRmbAmount(value.stream().filter(x-> x.getRmbAmount() != null).map(CardSettleReport::getRmbAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
//
//            result
//        }
//
//
//    }
}
