package com.anytech.anytxn.account.base.service;

import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentInfoHistoryDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogHisSearchKeyDTO;

import java.util.List;

/**
 * 约定扣款service
 *
 * @Author: Wenwu Huang
 * @Date: 2019/1/21 16:03
 */
public interface IAutoPaymentLogHistoryService {
    /**
     * 自动还款历史处理
     * @param list 自动还款历史list
     */
    void batchSave(List<AutoPaymentBO> list);

    /**
     * 根据搜索条件分页查询自动还款历史记录
     * @param searchKey 搜索条件
     * @return PageResultDTO<AutoPaymentLogHisSearchKeyDto>
     */
    PageResultDTO<AutoPaymentInfoHistoryDTO> findByCardNum(AutoPaymentLogHisSearchKeyDTO searchKey);

    /**
     * 根据主键查询自动还款历史明细
     * @param autoPaymentId 自动还款历史id
     * @return AutoPaymentInfoHistoryDto
     */
    AutoPaymentInfoHistoryDTO selectById(String autoPaymentId);
}
