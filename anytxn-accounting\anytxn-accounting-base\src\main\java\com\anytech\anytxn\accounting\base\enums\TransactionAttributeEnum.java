package com.anytech.anytxn.accounting.base.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 交易属性
 * @date 2020-02-16 14:06
 **/
public enum TransactionAttributeEnum {
    /**
     * 1.消费
     *
     */
    ZERO("0", "余额变动类"),
    ONE("1", "消费"),
    TWO("2", "现金"),
    THREE("3", "消费分期抛帐"),
    FOUR("4", "现金分期抛帐"),
    FIVE("5", "消费费用"),
    SIX("6", "现金费用"),
    SEVEN("7", "还款"),
    EIGHT("8", "争议登记"),
    NINE("9", "争议释放"),
    M("M", "备忘交易"),
    A("A", "消费利息"),
    B("B", "现金利息"),
    E("E", "分期贷记"),
    F("F", "分期整笔"),
    G("G", "分期单笔"),
    H("H", "分期费用"),
    I("I", "分期违约金"),
    <PERSON>("J", "费用摊销"),
    X("X", "购汇"),
    Y("Y", "购汇MEMO"),
    ;
    private String code;
    private String desc;


    TransactionAttributeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String getValue(String code) {
        if (code == null) {
            return null;
        }
        TransactionAttributeEnum[] var2 = values();
        int var3 = var2.length;
        for (int var4 = 0; var4 < var3; ++var4) {
            TransactionAttributeEnum moduleFlagEnum = var2[var4];
            if (Objects.equals(code, moduleFlagEnum.getCode())) {
                return moduleFlagEnum.getDesc();
            }
        }
        return null;
    }
}
