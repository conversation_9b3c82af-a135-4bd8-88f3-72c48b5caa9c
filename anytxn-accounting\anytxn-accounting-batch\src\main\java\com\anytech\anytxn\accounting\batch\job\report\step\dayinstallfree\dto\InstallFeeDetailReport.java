package com.anytech.anytxn.accounting.batch.job.report.step.dayinstallfree.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020/9/16 16:56
 */
@Getter
@Setter
@ToString
public class InstallFeeDetailReport {
    //产品ID
    private String productId;
    //产品描述
    private String productDesc;
    //币种
    private String currencyCode;
    //已入账手续费金额
    private BigDecimal isPostFeeAmount;
    //未入账手续费金额
    private BigDecimal noPostFeeAmount;
}
