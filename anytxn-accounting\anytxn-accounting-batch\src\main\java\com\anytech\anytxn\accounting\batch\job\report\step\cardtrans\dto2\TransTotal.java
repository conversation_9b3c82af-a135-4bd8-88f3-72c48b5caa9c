package com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.dto2;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/10/15 18:03
 */
@Getter
@Setter
@ToString
public class TransTotal {

    private String currencyType;


    //只给一个TransTotalDetailBase
  private List<TransTotalDetailBase> transTotalDetailBaseList = new ArrayList<>();

    //汇总
    private List<TransTotalTotal> transTotalTotalList = new ArrayList<>();


    private List<TransTotalVisaDetail> transTotalVisaDetailList = new ArrayList<>();

    private String  visaTotal;


}
