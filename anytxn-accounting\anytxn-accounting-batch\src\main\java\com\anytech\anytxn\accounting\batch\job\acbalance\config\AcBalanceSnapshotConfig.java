package com.anytech.anytxn.accounting.batch.job.acbalance.config;

import com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance.AccountBalanceProcessor;
import com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance.AccountBalanceReader;
import com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance.AccountBalanceWriter;
import com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder.InstallOrderProcessor;
import com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder.InstallOrderReader;
import com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder.InstallOrderWriter;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlactbalDTO;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlinsbalDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.common.core.constants.PropsConstant;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2020/4/1
 *
 * 余额快照处理(分户余额汇总 和 分期余额汇总)
 * alter table T_AMS_GLINSBAL  modify (ID NVARCHAR2(24));
 * alter table T_AMS_GLACTBAL  modify (ID NVARCHAR2(24));
 */
@Configuration
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class AcBalanceSnapshotConfig {

    @Autowired
    private JobBuilderFactory jobBuilderFactory;

    @Autowired
    private StepBuilderFactory stepBuilderFactory;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.acBalanceSnapshotJob.accountBalanceStep.grid-size:500}")
    private Integer accountBalanceSize;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.acBalanceSnapshotJob.installOrderStep.grid-size:500}")
    private Integer installOrderSize;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.acBalanceSnapshotJob.acBalanceSnapshotStep.chunk-limit:500}")
    private Integer chunkLimit;

    @Bean
    public Job acBalanceSnapshotJob(@Qualifier("installOrderStep") Step installOrderStep,
                               @Qualifier("accountBalanceStep") Step accountBalanceStep) {
        return jobBuilderFactory.get("acBalanceSnapshotJob")
                .start(accountBalanceStep)
                .next(installOrderStep)
                .build();
    }

    @Bean
    public Step accountBalanceStep(@Qualifier("accountBalanceReader") AccountBalanceReader accountBalanceReader,
                                      @Qualifier("accountBalanceProcessor") AccountBalanceProcessor accountBalanceProcessor,
                                      @Qualifier("accountBalanceWriter") AccountBalanceWriter accountBalanceWriter) {
        return stepBuilderFactory.get("accountBalanceStep")
                .<AccountBalanceInfoDTO, AccountantGlactbalDTO>chunk(chunkLimit)
                .reader(accountBalanceReader)
                .processor(accountBalanceProcessor)
                .writer(accountBalanceWriter)
                .build();
    }

    @Bean
    public Step installOrderStep(@Qualifier("installOrderReader") InstallOrderReader installOrderReader,
                                      @Qualifier("installOrderProcessor") InstallOrderProcessor installOrderProcessor,
                                      @Qualifier("installOrderWriter") InstallOrderWriter installOrderWriter) {
        return stepBuilderFactory.get("installOrderStep")
                .<InstallOrderDTO, AccountantGlinsbalDTO>chunk(chunkLimit)
                .reader(installOrderReader)
                .processor(installOrderProcessor)
                .writer(installOrderWriter)
                .build();
    }


    @Bean
    @StepScope
    public InstallOrderReader installOrderReader(@Qualifier("businessDataSource") DataSource dataSource) throws Exception {
        InstallOrderReader reader = new InstallOrderReader(dataSource);
        reader.setDataSource(dataSource);
        reader.setPageSize(installOrderSize);
        reader.setSaveState(true);

        reader.afterPropertiesSet();
        return reader;
    }

    @Bean
    @StepScope
    public InstallOrderProcessor installOrderProcessor(){
        return new InstallOrderProcessor();
    }

    @Bean
    @StepScope
    public InstallOrderWriter installOrderWriter(){
        return new InstallOrderWriter();
    }


    @Bean
    @StepScope
    public AccountBalanceReader accountBalanceReader(@Qualifier("businessDataSource") DataSource dataSource) throws Exception {
        AccountBalanceReader reader = new AccountBalanceReader(dataSource);
        reader.setDataSource(dataSource);
        reader.setPageSize(accountBalanceSize);
        reader.setSaveState(true);

        reader.afterPropertiesSet();
        return reader;
    }

    @Bean
    @StepScope
    public AccountBalanceProcessor accountBalanceProcessor(){
        return new AccountBalanceProcessor();
    }

    @Bean
    @StepScope
    public AccountBalanceWriter accountBalanceWriter(){
        return new AccountBalanceWriter();
    }
}
