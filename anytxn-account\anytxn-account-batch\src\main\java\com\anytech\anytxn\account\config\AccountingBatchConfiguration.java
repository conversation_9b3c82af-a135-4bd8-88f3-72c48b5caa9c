package com.anytech.anytxn.account.config;

//import com.anytech.anytxn.number.config.NumberConfigurer;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.file.enums.FilePathConfigTypeEnum;
import com.anytech.anytxn.file.utils.ValueProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020-03-25
 */
@Configuration
//@Import({NumberConfigurer.class})
public class AccountingBatchConfiguration {
    @Bean
    public AnytxnFilePathConfig autoPaymentFilePathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.account.autoPayment", FilePathConfigTypeEnum.INPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig autoPaymentOutFilePathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.account.autoPayment", FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig paymentFileInputPathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.account.repay", FilePathConfigTypeEnum.INPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig paymentFileOutPutPathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.account.repay", FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig outPutFilePathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.account.autoPayment", FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }
    @Bean
    public AnytxnFilePathConfig stampTaxSumLimitFilePathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.account.stamp", FilePathConfigTypeEnum.INPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig matchCupFilePathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.cm.matchCup", FilePathConfigTypeEnum.INPUT, filePathValue);
    }
    @Bean
    public AnytxnFilePathConfig btiBlockCodeUpdateReportOutFilePathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.account.btiBlockCodeUpdateFilePath", FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }
 }
