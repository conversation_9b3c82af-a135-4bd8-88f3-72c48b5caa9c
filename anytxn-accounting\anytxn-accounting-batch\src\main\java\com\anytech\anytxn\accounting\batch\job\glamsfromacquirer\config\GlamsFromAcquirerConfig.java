package com.anytech.anytxn.accounting.batch.job.glamsfromacquirer.config;


import com.anytech.anytxn.accounting.batch.job.glamsfromacquirer.step.GlamsFromAcquirerTasklet;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class GlamsFromAcquirerConfig {

    @Autowired
    private JobBuilderFactory jobBuilderFactory;

    @Autowired
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private GlamsFromAcquirerTasklet glamsFromAcquirerTasklet;



    @Bean("glamsFromAcquirerJob")
    public Job glamsFromAcquirerJob() {
        return jobBuilderFactory.get("glamsFromAcquirerJob")
                .start(glamsFromAcquirerStep())
                .build();
    }

    @Bean("glamsFromAcquirerStep")
    public Step glamsFromAcquirerStep() {
        return stepBuilderFactory.get("glamsFromAcquirerStep")
                .tasklet(glamsFromAcquirerTasklet)
                .build();
    }
}
