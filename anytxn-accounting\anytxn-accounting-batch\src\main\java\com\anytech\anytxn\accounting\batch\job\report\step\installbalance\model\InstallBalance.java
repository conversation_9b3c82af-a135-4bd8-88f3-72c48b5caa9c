package com.anytech.anytxn.accounting.batch.job.report.step.installbalance.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/9/16
 */
@Getter
@Setter
public class InstallBalance {

    /**
     * 分行号
     */
    private String branchId;

    /**
     * 分期产品
     */
    private String productNumber;

    /**
     * 描述
     */
    private String description;

    /**
     * 币种
     */
    private String currCode;

    /**
     * 余额
     */
    private BigDecimal balance;
}
