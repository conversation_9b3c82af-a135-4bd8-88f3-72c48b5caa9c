package com.anytech.anytxn.accounting.batch.config;

import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.file.enums.FilePathConfigTypeEnum;
import com.anytech.anytxn.file.utils.ValueProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FileManagerConfig {
    @Bean
    public AnytxnFilePathConfig recordSumFileFormatPathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.accountant.oriRecordSum",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig txnSumFileFormatPathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.accountant.txnSum",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig ordFileFormatPathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.accountant.oriDetail",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig illGlamsSumFileFormatPathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.accountant.illGlamsSum",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig illGlvcherFileFormatPathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.accountant.illGlvcher",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig upiFilePathConfig(ValueProvider filePathValue){
        return  new AnytxnFilePathConfig("anytxn-file-path.cm.upi", FilePathConfigTypeEnum.INPUT, filePathValue);
    }
}
