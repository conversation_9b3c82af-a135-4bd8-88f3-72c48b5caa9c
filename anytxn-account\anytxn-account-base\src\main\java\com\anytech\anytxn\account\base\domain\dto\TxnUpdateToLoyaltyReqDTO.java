package com.anytech.anytxn.account.base.domain.dto;

import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @Date 2024/4/18  14:34
 * @Version 1.0
 */

@Setter
@Getter
public class TxnUpdateToLoyaltyReqDTO {
  /**
   * txn管理账户号
   */
  private String accountManagementId;


  /**
   * txn卡号
   */
  private String cardNumber;

  /**
   * txn产品号
   */
  private String productNumber;

  /**
   * 权益ID
   */
  private String projectId;

  /**
   * 生效方式
   */
  private String effectMode;

  /**
   * 权益账号类型
   */
  private String type;

  /**
   * 权益账号姓
   */
  private String family;

  /**
   * 权益账号名
   */
  private String given;

  /**
   * 权益账号
   */
  private String accountNumber;

  /**
   * 回馈周期
   */
  private String rewardDuration;
}

