package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 *
 */
@Setter
@Getter
@ToString
public class TAmsGlacgmDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = -1760974192886588163L;

	/**
     * 技术主键
     * 表字段:ID
     */
    private String id;

    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 日期
     * 表字段:DATE_M
     */
    private String dateM;

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    private String glAcct;

    /**
     * 币种
     * 表字段:CURR
     */
    private String curr;

    /**
     * 科目分类
     * 表字段:GL_CLASS
     */
    private String glClass;

    /**
     * 科目名称
     * 表字段:GL_NAME
     */
    private String glName;

    /**
     * 上日借方余额
     * 表字段:PREV_BAL_DR
     */
    private BigDecimal prevBalDr;

    /**
     * 上日贷方余额
     * 表字段:PREV_BAL_CR
     */
    private BigDecimal prevBalCr;

    /**
     * 当日借方发生额
     * 表字段:OCCUR_DR
     */
    private BigDecimal occurDr;

    /**
     * 当日贷方发生额
     * 表字段:OCCUR_CR
     */
    private BigDecimal occurCr;

    /**
     * 当日借方余额
     * 表字段:CURR_BAL_DR
     */
    private BigDecimal currBalDr;

    /**
     * 当日贷方余额
     * 表字段:CURR_BAL_CR
     */
    private BigDecimal currBalCr;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

}
