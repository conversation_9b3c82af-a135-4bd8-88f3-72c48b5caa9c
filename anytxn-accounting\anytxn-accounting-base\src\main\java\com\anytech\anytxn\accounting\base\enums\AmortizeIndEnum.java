package com.anytech.anytxn.accounting.base.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 模块标识
 * @date 2020-02-16 14:06
 **/
public enum AmortizeIndEnum {
    /**
     */
    N("N", "非递延"),
    Y("Y", "递延"),
    ;
    private String code;
    private String desc;


    AmortizeIndEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String getValue(String code) {
        if (code == null) {
            return null;
        }
        AmortizeIndEnum[] var2 = values();
        int var3 = var2.length;
        for (int var4 = 0; var4 < var3; ++var4) {
            AmortizeIndEnum moduleFlagEnum = var2[var4];
            if (Objects.equals(code, moduleFlagEnum.getCode())) {
                return moduleFlagEnum.getDesc();
            }
        }
        return null;
    }
}
