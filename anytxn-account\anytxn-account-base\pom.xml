<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.anytech</groupId>
        <artifactId>anytxn-account</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>anytxn-account-base</artifactId>

    <dependencies>
        <!--        内部依赖start-->
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-rule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-business-core-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-business-core-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-parameter-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-transaction-base</artifactId>
        </dependency>
        <!--        内部依赖end-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
    </dependencies>
</project>
