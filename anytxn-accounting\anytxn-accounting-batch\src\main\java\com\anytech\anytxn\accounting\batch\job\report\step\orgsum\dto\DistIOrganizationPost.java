package com.anytech.anytxn.accounting.batch.job.report.step.orgsum.dto;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.anytech.anytxn.accounting.batch.job.report.step.orgsum.model.OrgAmountDetail;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 每月分期付款出账报表
 * <AUTHOR>
 * @Date 2020/9/11 11:43
 */
@Getter
@Setter
@ToString
public class DistIOrganizationPost {

    private List<DistIOrganizationPostSub> list;
    //总数
    private Integer total;

    public static DistIOrganizationPost resetReportData(List<OrgAmountDetail> data, TimeTypeEnum timeTypeEnum){
        DistIOrganizationPost head = new DistIOrganizationPost();
        head.setTotal(data.size());
        List<DistIOrganizationPostSub> subs = new ArrayList<>();
        head.setList(subs);

        Map<String, List<OrgAmountDetail>> group = data.stream().collect(Collectors.groupingBy(x -> x.getBranchId() + x.getCurrCode()));
        int i = 0;
        for (Map.Entry<String, List<OrgAmountDetail>> entry : group.entrySet()) {
            List<OrgAmountDetail> value = entry.getValue();
            OrgAmountDetail temple = value.get(0);
            DistIOrganizationPostSub sub = new DistIOrganizationPostSub();
            sub.setBankNumber(temple.getBranchId());
            sub.setCurrencyCode(temple.getCurrCode());
            sub.setEnd(++i == value.size());
            sub.setSubjectCount(value.size());
            List<DistIOrganizationPostDetail> details = new ArrayList<>();
            sub.setDetailList(details);
            subs.add(sub);

            for (OrgAmountDetail detail : value) {
                DistIOrganizationPostDetail postDetail = new DistIOrganizationPostDetail();
                postDetail.setBankNumber(detail.getBranchId());
                postDetail.setNetNumber(detail.getFundId());
                postDetail.setCurrencyCode(detail.getCurrCode());
                postDetail.setSubjectNumber(detail.getGlAcct());
                postDetail.setSubjectName(detail.getGlAcctName());
                postDetail.setSubjectCategory(detail.getGlAcctType());
                postDetail.setPostDate(TimeUtils.dateToStr(detail.getPostDate(), timeTypeEnum));
                postDetail.setDebitAmount(detail.getDMount());
                postDetail.setCrebitAmount(detail.getCMount());

                details.add(postDetail);
            }
        }

        return head;
    }
}
