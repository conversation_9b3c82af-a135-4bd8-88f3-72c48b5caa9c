package com.anytech.anytxn.accounting.batch.job.glamsfromupi.steps;

import com.anytech.anytxn.accounting.batch.job.glamsfromupi.dto.DetailC602DZ;
import com.anytech.anytxn.accounting.batch.job.glamsfromupi.dto.DetailC602DZ004;
import com.anytech.anytxn.accounting.batch.job.glamsfromupi.dto.ReportC602DZ;
import com.anytech.anytxn.accounting.batch.job.glamsfromupi.dto.UPIMessage;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsMapper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.Random;

/**
 * <AUTHOR>
 */
@Slf4j
@StepScope
@Component
public class C602DZToLogTable implements Tasklet {

    @Resource(name = "upiFilePathConfig")
    private AnytxnFilePathConfig upiInputFilePathConfig;

    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    AccountantGlamsMapper accountantGlamsMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;



    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Exception.class)
    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>UPIFiles C602DZToLogTable Start>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        LocalDate today = organizationInfo.getToday();
        String yyyyMMdd = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        File upiFile = new File(upiInputFilePathConfig.getCommonPath() + File.separator + yyyyMMdd);
        if (!upiFile.exists()) {
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}没有文件，不做处理。。。>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",upiFile.getAbsolutePath());
            return RepeatStatus.FINISHED;
        }
        File[] upiFileList = upiFile.listFiles();
        this.fileFormat(upiFileList,today);
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>UPIFiles C602DZToLogTable End>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        return RepeatStatus.FINISHED;
    }

    private void fileFormat(File[] upiFiles,LocalDate today) throws IOException {
        for (File upiTypeFile : upiFiles) {
            String upiTypeFileName = upiTypeFile.getName();
            //C602DZ报表文件
            if (upiTypeFileName.length() > 17 && "C602DZ".equals(upiTypeFileName.substring(11, 17))) {
                log.info("C602DZ file: {}", upiTypeFile.getAbsolutePath());
                try (FileReader fileReader = new FileReader(upiTypeFile);
                     BufferedReader bufferedReader = new BufferedReader(fileReader)) {
                    int i = 1;
                    ReportC602DZ reportC602DZ = new ReportC602DZ();
                    String line = bufferedReader.readLine();
                    log.info("line{}: {}", i++, line);
                    while (line != null) {
                        //去除横线
                        if(line.startsWith("==========") ||
                                line.startsWith("----------") ||
                                line.startsWith("Ins Role")){
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                        }
                        if(line == null){
                            continue;
                        }
                        //文件头处理
                        if(line.startsWith("IIN:")){
                            String iin = line.substring(31).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String institutionName = line.substring(31).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String transactionSettlementDate = line.substring(31).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String reportDate = line.substring(31).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String transactionCurrency = line.substring(31).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String settlementCurrency = line.substring(31).trim();
                            reportC602DZ.setIin(iin);
                            reportC602DZ.setInstitutionName(institutionName);
                            reportC602DZ.setTransactionSettlementDate(LocalDate.parse(transactionSettlementDate, DateTimeFormatter.ofPattern("yyyyMMdd")));
                            reportC602DZ.setReportDate(LocalDate.parse(reportDate,DateTimeFormatter.ofPattern("yyyyMMdd")));
                            reportC602DZ.setTransactionCurrency(transactionCurrency);
                            reportC602DZ.setSettlementCurrency(settlementCurrency);

                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                        }
                        //C602-DZ-001 机构清算汇总报表
                        if(line.endsWith("C602-DZ-001")){
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String reportName = line.substring(14).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String reportDate = line.substring(14).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            LinkedList<DetailC602DZ> detailC602DZ01 = new LinkedList<>();
                            do{
                                while (line != null && !line.startsWith("==========")){
                                    if(line.startsWith("----------") || line.startsWith("Ins Role")
                                            || line.startsWith("==========")){
                                        line = bufferedReader.readLine();
                                        log.info("line{}: {}", i++, line);
                                        continue;
                                    }
                                    DetailC602DZ detailC602DZ = UPIMessage.parse(line, DetailC602DZ.class);
                                    detailC602DZ01.add(detailC602DZ);
                                    line = bufferedReader.readLine();
                                    log.info("line{}: {}", i++, line);
                                }
                            }while (!line.startsWith("=========="));
                            reportC602DZ.setC602dz01(detailC602DZ01);
                        }

                        //C602-DZ-002 机构费用清算报表
                        if(line.endsWith("C602-DZ-002")){
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String reportName = line.substring(14).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String reportDate = line.substring(14).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            LinkedList<DetailC602DZ> detailC602DZ02 = new LinkedList<>();
                            do{
                                while (line != null && !line.startsWith("==========")){
                                    if(line.startsWith("----------") || line.startsWith("Ins Role")
                                            || line.startsWith("==========")){
                                        line = bufferedReader.readLine();
                                        log.info("line{}: {}", i++, line);
                                        continue;
                                    }
                                    DetailC602DZ detailC602DZ = UPIMessage.parse(line, DetailC602DZ.class);
                                    detailC602DZ02.add(detailC602DZ);
                                    line = bufferedReader.readLine();
                                    log.info("line{}: {}", i++, line);
                                }
                            }while (!line.startsWith("=========="));
                            reportC602DZ.setC602dz02(detailC602DZ02);
                        }

                        //C602-DZ-003 机构差错费用报表
                        if(line.endsWith("C602-DZ-003")){
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String reportName = line.substring(14).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            String reportDate = line.substring(14).trim();
                            line = bufferedReader.readLine();
                            log.info("line{}: {}", i++, line);
                            LinkedList<DetailC602DZ> detailC602DZ03 = new LinkedList<>();
                            do{
                                while (line != null && !line.startsWith("==========")){
                                    if(line.startsWith("----------") || line.startsWith("Ins Role")
                                            || line.startsWith("==========")){
                                        line = bufferedReader.readLine();
                                        log.info("line{}: {}", i++, line);
                                        continue;
                                    }
                                    DetailC602DZ detailC602DZ = UPIMessage.parse(line, DetailC602DZ.class);
                                    detailC602DZ03.add(detailC602DZ);
                                    line = bufferedReader.readLine();
                                    log.info("line{}: {}", i++, line);
                                }
                            }while (!line.startsWith("=========="));
                            reportC602DZ.setC602dz03(detailC602DZ03);
                        }

                        //C602-DZ-004 机构代授权费用报表
                        if(line != null){
                            if(line.startsWith("----------")
                                    || line.startsWith("==========")){
                                continue;
                            }
                            LinkedList<DetailC602DZ004> detailC602DZ04 = new LinkedList<>();
                            while (line != null){
                                if (line.length() == 89 && "Fees of Stand-in Authorization".equals(line.substring(0,36).trim())){
                                    DetailC602DZ004 detailC602DZ004 = UPIMessage.parse(line, DetailC602DZ004.class);
                                    detailC602DZ04.add(detailC602DZ004);
                                    line = bufferedReader.readLine();
                                    log.info("line{}: {}", i++, line);
                                }else {
                                    line = bufferedReader.readLine();
                                    log.info("line{}: {}", i++, line);
                                }
                            }
                            reportC602DZ.setC602dz04(detailC602DZ04);
                        }
                    }

                    //币种
                    String settlementCurrency = reportC602DZ.getSettlementCurrency();

                    if(!CollectionUtils.isEmpty(reportC602DZ.getC602dz01())){
                        for(DetailC602DZ detailC602DZ : reportC602DZ.getC602dz01()){
                            if("Issuer".equals(detailC602DZ.getInsRole())){
                                String serviceFee = detailC602DZ.getServiceFee();
                                String reimbursementFee = detailC602DZ.getReimbursementFee();
                                String netSettlementAmount = detailC602DZ.getNetSettlementAmount();

                                BigDecimal serviceFeeBig = new BigDecimal(serviceFee);
                                BigDecimal reimbursementFeeBig = new BigDecimal(reimbursementFee);
                                BigDecimal netSettlementAmountBig = new BigDecimal(netSettlementAmount);
                                BigDecimal sAddr = serviceFeeBig.add(reimbursementFeeBig);



                                //存表glams
                                if(serviceFeeBig.compareTo(BigDecimal.ZERO) == -1 || serviceFeeBig.compareTo(BigDecimal.ZERO) == 0){
                                    log.info("+++++++++++++C602-DZ-001 serviceFee 开始存glams表++++++++++++++++++++++");
                                    log.info("+++++++++++++C602-DZ-001 serviceFee 正在存glams表。。。。。。。。。。。。。。。。。。。。。。");
                                    AccountantGlams accountantGlams = new AccountantGlams();
                                    BigDecimal amt = serviceFeeBig.setScale(2, RoundingMode.HALF_UP).abs();
                                    accountantGlams.setCrdNumber("UPI602DZ001");
                                    accountantGlams.setTxnAmt(amt);
                                    accountantGlams.setTxnCurrency(settlementCurrency);
                                    accountantGlams.setSettleAmt(amt);
                                    accountantGlams.setSettleCurrency(settlementCurrency);
                                    accountantGlams.setTxnDate(today.atStartOfDay());
                                    accountantGlams.setTxnSource("C");
                                    accountantGlams.setTxnDescription("UPI 602-DZ-001 Service Fee");
                                    accountantGlams.setCurrencyRate("1");
                                    accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                                    accountantGlams.setBranchid("DINERS");
                                    accountantGlams.setAccountManagementId("UPI602DZ001");
                                    Random random=new Random();
                                    int rannum= (int)(random.nextDouble()*(99999-10000 + 1))+ 10000;
                                    accountantGlams.setGlobalFlowNo(today.atTime(LocalTime.now()).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+rannum);
                                    accountantGlams.setModuleFlag("0");
                                    accountantGlams.setTxnCode("UPSFD");
                                    accountantGlams.setPostingDate(today);
                                    accountantGlams.setPostingCurrencyCode(settlementCurrency);
                                    accountantGlams.setPostingAmt(amt);
                                    accountantGlams.setFinanceStatus("0");
                                    accountantGlams.setPriceTaxFlg("N");
                                    accountantGlams.setTxnCodeOrig("UPSFD");
                                    accountantGlams.setCreateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateBy("admin");
                                    accountantGlams.setVersionNumber(1L);
                                    accountantGlams.setPartitionKey(0L);
                                    accountantGlams.setSubchannelId("C");
                                    accountantGlams.setPlatformId("C");
                                    accountantGlams.setOrganizationNumber("101");
                                    accountantGlamsMapper.insertSelective(accountantGlams);
                                    log.info("+++++++++++++C602-DZ-001 serviceFee 存glams表完成++++++++++++++++++++++");
                                }

                                if(serviceFeeBig.compareTo(BigDecimal.ZERO) == 1){
                                    log.info("+++++++++++++C602-DZ-001 serviceFee 开始存glams表++++++++++++++++++++++");
                                    log.info("+++++++++++++C602-DZ-001 serviceFee 正在存glams表。。。。。。。。。。。。。。。。。。。。。。");
                                    AccountantGlams accountantGlams = new AccountantGlams();
                                    BigDecimal amt = serviceFeeBig.setScale(2, RoundingMode.HALF_UP).abs();
                                    accountantGlams.setCrdNumber("UPI602DZ001");
                                    accountantGlams.setTxnAmt(amt);
                                    accountantGlams.setTxnCurrency(settlementCurrency);
                                    accountantGlams.setSettleAmt(amt);
                                    accountantGlams.setSettleCurrency(settlementCurrency);
                                    accountantGlams.setTxnDate(today.atStartOfDay());
                                    accountantGlams.setTxnSource("C");
                                    accountantGlams.setTxnDescription("UPI 602-DZ-001 Service Fee");
                                    accountantGlams.setCurrencyRate("1");
                                    accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                                    accountantGlams.setBranchid("DINERS");
                                    accountantGlams.setAccountManagementId("UPI602DZ001");
                                    Random random=new Random();
                                    int rannum= (int)(random.nextDouble()*(99999-10000 + 1))+ 10000;
                                    accountantGlams.setGlobalFlowNo(today.atTime(LocalTime.now()).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+rannum);
                                    accountantGlams.setModuleFlag("0");
                                    accountantGlams.setTxnCode("UPSFC");
                                    accountantGlams.setPostingDate(today);
                                    accountantGlams.setPostingCurrencyCode(settlementCurrency);
                                    accountantGlams.setPostingAmt(amt);
                                    accountantGlams.setFinanceStatus("0");
                                    accountantGlams.setPriceTaxFlg("N");
                                    accountantGlams.setTxnCodeOrig("UPSFC");
                                    accountantGlams.setCreateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateBy("admin");
                                    accountantGlams.setVersionNumber(1L);
                                    accountantGlams.setPartitionKey(0L);
                                    accountantGlams.setSubchannelId("C");
                                    accountantGlams.setPlatformId("C");
                                    accountantGlams.setOrganizationNumber("101");
                                    accountantGlamsMapper.insertSelective(accountantGlams);
                                    log.info("+++++++++++++C602-DZ-001 serviceFee 存glams表完成++++++++++++++++++++++");
                                }

                                if(reimbursementFeeBig.compareTo(BigDecimal.ZERO) == -1 || reimbursementFeeBig.compareTo(BigDecimal.ZERO) == 0){
                                    log.info("+++++++++++++C602-DZ-001 reimbursementFee 开始存glams表++++++++++++++++++++++");
                                    log.info("+++++++++++++C602-DZ-001 reimbursementFee 正在存glams表。。。。。。。。。。。。。。。。。。。。。。");
                                    AccountantGlams accountantGlams = new AccountantGlams();
                                    BigDecimal amt = reimbursementFeeBig.setScale(2, RoundingMode.HALF_UP).abs();
                                    accountantGlams.setCrdNumber("UPI602DZ001");
                                    accountantGlams.setTxnAmt(amt);
                                    accountantGlams.setTxnCurrency(settlementCurrency);
                                    accountantGlams.setSettleAmt(amt);
                                    accountantGlams.setSettleCurrency(settlementCurrency);
                                    accountantGlams.setTxnDate(today.atStartOfDay());
                                    accountantGlams.setTxnSource("C");
                                    accountantGlams.setTxnDescription("UPI 602-DZ-001 Reimbursement Fee");
                                    accountantGlams.setCurrencyRate("1");
                                    accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                                    accountantGlams.setBranchid("DINERS");
                                    accountantGlams.setAccountManagementId("UPI602DZ001");
                                    Random random=new Random();
                                    int rannum= (int)(random.nextDouble()*(99999-10000 + 1))+ 10000;
                                    accountantGlams.setGlobalFlowNo(today.atTime(LocalTime.now()).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+rannum);
                                    accountantGlams.setModuleFlag("0");
                                    accountantGlams.setTxnCode("UPRFD");
                                    accountantGlams.setPostingDate(today);
                                    accountantGlams.setPostingCurrencyCode(settlementCurrency);
                                    accountantGlams.setPostingAmt(amt);
                                    accountantGlams.setFinanceStatus("0");
                                    accountantGlams.setPriceTaxFlg("N");
                                    accountantGlams.setTxnCodeOrig("UPRFD");
                                    accountantGlams.setCreateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateBy("admin");
                                    accountantGlams.setVersionNumber(1L);
                                    accountantGlams.setPartitionKey(0L);
                                    accountantGlams.setSubchannelId("C");
                                    accountantGlams.setPlatformId("C");
                                    accountantGlams.setOrganizationNumber("101");
                                    accountantGlamsMapper.insertSelective(accountantGlams);
                                    log.info("+++++++++++++C602-DZ-001 reimbursementFee 存glams表完成++++++++++++++++++++++");
                                }

                                if(reimbursementFeeBig.compareTo(BigDecimal.ZERO) == 1){
                                    log.info("+++++++++++++C602-DZ-001 reimbursementFee 开始存glams表++++++++++++++++++++++");
                                    log.info("+++++++++++++C602-DZ-001 reimbursementFee 正在存glams表。。。。。。。。。。。。。。。。。。。。。。");
                                    AccountantGlams accountantGlams = new AccountantGlams();
                                    BigDecimal amt = reimbursementFeeBig.setScale(2, RoundingMode.HALF_UP).abs();
                                    accountantGlams.setCrdNumber("UPI602DZ001");
                                    accountantGlams.setTxnAmt(amt);
                                    accountantGlams.setTxnCurrency(settlementCurrency);
                                    accountantGlams.setSettleAmt(amt);
                                    accountantGlams.setSettleCurrency(settlementCurrency);
                                    accountantGlams.setTxnDate(today.atStartOfDay());
                                    accountantGlams.setTxnSource("C");
                                    accountantGlams.setTxnDescription("UPI 602-DZ-001 Reimbursement Fee");
                                    accountantGlams.setCurrencyRate("1");
                                    accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                                    accountantGlams.setBranchid("DINERS");
                                    accountantGlams.setAccountManagementId("UPI602DZ001");
                                    Random random=new Random();
                                    int rannum= (int)(random.nextDouble()*(99999-10000 + 1))+ 10000;
                                    accountantGlams.setGlobalFlowNo(today.atTime(LocalTime.now()).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+rannum);
                                    accountantGlams.setModuleFlag("0");
                                    accountantGlams.setTxnCode("UPRFC");
                                    accountantGlams.setPostingDate(today);
                                    accountantGlams.setPostingCurrencyCode(settlementCurrency);
                                    accountantGlams.setPostingAmt(amt);
                                    accountantGlams.setFinanceStatus("0");
                                    accountantGlams.setPriceTaxFlg("N");
                                    accountantGlams.setTxnCodeOrig("UPRFC");
                                    accountantGlams.setCreateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateBy("admin");
                                    accountantGlams.setVersionNumber(1L);
                                    accountantGlams.setPartitionKey(0L);
                                    accountantGlams.setSubchannelId("C");
                                    accountantGlams.setPlatformId("C");
                                    accountantGlams.setOrganizationNumber("101");
                                    accountantGlamsMapper.insertSelective(accountantGlams);
                                    log.info("+++++++++++++C602-DZ-001 reimbursementFee 存glams表完成++++++++++++++++++++++");

                                }

                                if(sAddr.compareTo(BigDecimal.ZERO) == -1 || sAddr.compareTo(BigDecimal.ZERO) == 0){
                                    log.info("+++++++++++++C602-DZ-001 serviceFee+reimbursementFee 开始存glams表++++++++++++++++++++++");
                                    log.info("+++++++++++++C602-DZ-001 serviceFee+reimbursementFee 正在存glams表。。。。。。。。。。。。。。。。。。。。。。");
                                    AccountantGlams accountantGlams = new AccountantGlams();
                                    BigDecimal amt = sAddr.setScale(2, RoundingMode.HALF_UP).abs();
                                    accountantGlams.setCrdNumber("UPI602DZ001");
                                    accountantGlams.setTxnAmt(amt);
                                    accountantGlams.setTxnCurrency(settlementCurrency);
                                    accountantGlams.setSettleAmt(amt);
                                    accountantGlams.setSettleCurrency(settlementCurrency);
                                    accountantGlams.setTxnDate(today.atStartOfDay());
                                    accountantGlams.setTxnSource("C");
                                    accountantGlams.setTxnDescription("UPI 602-DZ-001 Service Fee + Reimbursement Fee");
                                    accountantGlams.setCurrencyRate("1");
                                    accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                                    accountantGlams.setBranchid("DINERS");
                                    accountantGlams.setAccountManagementId("UPI602DZ001");
                                    Random random=new Random();
                                    int rannum= (int)(random.nextDouble()*(99999-10000 + 1))+ 10000;
                                    accountantGlams.setGlobalFlowNo(today.atTime(LocalTime.now()).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+rannum);
                                    accountantGlams.setModuleFlag("0");
                                    accountantGlams.setTxnCode("UPSRD");
                                    accountantGlams.setPostingDate(today);
                                    accountantGlams.setPostingCurrencyCode(settlementCurrency);
                                    accountantGlams.setPostingAmt(amt);
                                    accountantGlams.setFinanceStatus("0");
                                    accountantGlams.setPriceTaxFlg("N");
                                    accountantGlams.setTxnCodeOrig("UPSRD");
                                    accountantGlams.setCreateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateBy("admin");
                                    accountantGlams.setVersionNumber(1L);
                                    accountantGlams.setPartitionKey(0L);
                                    accountantGlams.setSubchannelId("C");
                                    accountantGlams.setPlatformId("C");
                                    accountantGlams.setOrganizationNumber("101");
                                    accountantGlamsMapper.insertSelective(accountantGlams);
                                    log.info("+++++++++++++C602-DZ-001 serviceFee+reimbursementFee 存glams表完成++++++++++++++++++++++");

                                }

                                if(sAddr.compareTo(BigDecimal.ZERO) == 1){
                                    log.info("+++++++++++++C602-DZ-001 serviceFee+reimbursementFee 开始存glams表++++++++++++++++++++++");
                                    log.info("+++++++++++++C602-DZ-001 serviceFee+reimbursementFee 正在存glams表。。。。。。。。。。。。。。。。。。。。。。");
                                    AccountantGlams accountantGlams = new AccountantGlams();
                                    BigDecimal amt = sAddr.setScale(2, RoundingMode.HALF_UP).abs();
                                    accountantGlams.setCrdNumber("UPI602DZ001");
                                    accountantGlams.setTxnAmt(amt);
                                    accountantGlams.setTxnCurrency(settlementCurrency);
                                    accountantGlams.setSettleAmt(amt);
                                    accountantGlams.setSettleCurrency(settlementCurrency);
                                    accountantGlams.setTxnDate(today.atStartOfDay());
                                    accountantGlams.setTxnSource("C");
                                    accountantGlams.setTxnDescription("UPI 602-DZ-001 Service Fee + Reimbursement Fee");
                                    accountantGlams.setCurrencyRate("1");
                                    accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                                    accountantGlams.setBranchid("DINERS");
                                    accountantGlams.setAccountManagementId("UPI602DZ001");
                                    Random random=new Random();
                                    int rannum= (int)(random.nextDouble()*(99999-10000 + 1))+ 10000;
                                    accountantGlams.setGlobalFlowNo(today.atTime(LocalTime.now()).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+rannum);
                                    accountantGlams.setModuleFlag("0");
                                    accountantGlams.setTxnCode("UPSRC");
                                    accountantGlams.setPostingDate(today);
                                    accountantGlams.setPostingCurrencyCode(settlementCurrency);
                                    accountantGlams.setPostingAmt(amt);
                                    accountantGlams.setFinanceStatus("0");
                                    accountantGlams.setPriceTaxFlg("N");
                                    accountantGlams.setTxnCodeOrig("UPSRC");
                                    accountantGlams.setCreateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateBy("admin");
                                    accountantGlams.setVersionNumber(1L);
                                    accountantGlams.setPartitionKey(0L);
                                    accountantGlams.setSubchannelId("C");
                                    accountantGlams.setPlatformId("C");
                                    accountantGlams.setOrganizationNumber("101");
                                    accountantGlamsMapper.insertSelective(accountantGlams);
                                    log.info("+++++++++++++C602-DZ-001 serviceFee+reimbursementFee 存glams表完成++++++++++++++++++++++");

                                }

                                if(netSettlementAmountBig.compareTo(BigDecimal.ZERO) == -1 || netSettlementAmountBig.compareTo(BigDecimal.ZERO) == 0){
                                    log.info("+++++++++++++C602-DZ-001 netSettlementAmount 开始存glams表++++++++++++++++++++++");
                                    log.info("+++++++++++++C602-DZ-001 netSettlementAmount 正在存glams表。。。。。。。。。。。。。。。。。。。。。。");
                                    AccountantGlams accountantGlams = new AccountantGlams();
                                    BigDecimal amt = netSettlementAmountBig.setScale(2, RoundingMode.HALF_UP).abs();
                                    accountantGlams.setCrdNumber("UPI602DZ001");
                                    accountantGlams.setTxnAmt(amt);
                                    accountantGlams.setTxnCurrency(settlementCurrency);
                                    accountantGlams.setSettleAmt(amt);
                                    accountantGlams.setSettleCurrency(settlementCurrency);
                                    accountantGlams.setTxnDate(today.atStartOfDay());
                                    accountantGlams.setTxnSource("C");
                                    accountantGlams.setTxnDescription("UPI 602-DZ-001 Net Settlement Amount");
                                    accountantGlams.setCurrencyRate("1");
                                    accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                                    accountantGlams.setBranchid("DINERS");
                                    accountantGlams.setAccountManagementId("UPI602DZ001");
                                    Random random=new Random();
                                    int rannum= (int)(random.nextDouble()*(99999-10000 + 1))+ 10000;
                                    accountantGlams.setGlobalFlowNo(today.atTime(LocalTime.now()).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+rannum);
                                    accountantGlams.setModuleFlag("0");
                                    accountantGlams.setTxnCode("UPNSD");
                                    accountantGlams.setPostingDate(today);
                                    accountantGlams.setPostingCurrencyCode(settlementCurrency);
                                    accountantGlams.setPostingAmt(amt);
                                    accountantGlams.setFinanceStatus("0");
                                    accountantGlams.setPriceTaxFlg("N");
                                    accountantGlams.setTxnCodeOrig("UPNSD");
                                    accountantGlams.setCreateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateBy("admin");
                                    accountantGlams.setVersionNumber(1L);
                                    accountantGlams.setPartitionKey(0L);
                                    accountantGlams.setSubchannelId("C");
                                    accountantGlams.setPlatformId("C");
                                    accountantGlams.setOrganizationNumber("101");
                                    accountantGlamsMapper.insertSelective(accountantGlams);
                                    log.info("+++++++++++++C602-DZ-001 netSettlementAmount 存glams表完成++++++++++++++++++++++");

                                }

                                if(netSettlementAmountBig.compareTo(BigDecimal.ZERO) == 1){
                                    log.info("+++++++++++++C602-DZ-001 netSettlementAmount 开始存glams表++++++++++++++++++++++");
                                    log.info("+++++++++++++C602-DZ-001 netSettlementAmount 正在存glams表。。。。。。。。。。。。。。。。。。。。。。");
                                    AccountantGlams accountantGlams = new AccountantGlams();
                                    BigDecimal amt = netSettlementAmountBig.setScale(2, RoundingMode.HALF_UP).abs();
                                    accountantGlams.setCrdNumber("UPI602DZ001");
                                    accountantGlams.setTxnAmt(amt);
                                    accountantGlams.setTxnCurrency(settlementCurrency);
                                    accountantGlams.setSettleAmt(amt);
                                    accountantGlams.setSettleCurrency(settlementCurrency);
                                    accountantGlams.setTxnDate(today.atStartOfDay());
                                    accountantGlams.setTxnSource("C");
                                    accountantGlams.setTxnDescription("UPI 602-DZ-001 Net Settlement Amount");
                                    accountantGlams.setCurrencyRate("1");
                                    accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                                    accountantGlams.setBranchid("DINERS");
                                    accountantGlams.setAccountManagementId("UPI602DZ001");
                                    Random random=new Random();
                                    int rannum= (int)(random.nextDouble()*(99999-10000 + 1))+ 10000;
                                    accountantGlams.setGlobalFlowNo(today.atTime(LocalTime.now()).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+rannum);
                                    accountantGlams.setModuleFlag("0");
                                    accountantGlams.setTxnCode("UPNSC");
                                    accountantGlams.setPostingDate(today);
                                    accountantGlams.setPostingCurrencyCode(settlementCurrency);
                                    accountantGlams.setPostingAmt(amt);
                                    accountantGlams.setFinanceStatus("0");
                                    accountantGlams.setPriceTaxFlg("N");
                                    accountantGlams.setTxnCodeOrig("UPNSC");
                                    accountantGlams.setCreateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateTime(LocalDateTime.now());
                                    accountantGlams.setUpdateBy("admin");
                                    accountantGlams.setVersionNumber(1L);
                                    accountantGlams.setPartitionKey(0L);
                                    accountantGlams.setSubchannelId("C");
                                    accountantGlams.setPlatformId("C");
                                    accountantGlams.setOrganizationNumber("101");
                                    accountantGlamsMapper.insertSelective(accountantGlams);
                                    log.info("+++++++++++++C602-DZ-001 netSettlementAmount 存glams表完成++++++++++++++++++++++");

                                }
                            }
                        }
                    }
                }
            }
        }
    }
}