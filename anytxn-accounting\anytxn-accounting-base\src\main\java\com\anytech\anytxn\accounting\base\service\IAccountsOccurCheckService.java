package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkOccurDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;

import java.util.List;

/**
 * 分户总分核对
 * <AUTHOR>
public interface IAccountsOccurCheckService {

/**
 * TODO
  * @param tPmsGlacgnDTO
 * @return java.util.List<com.anytech.anytxn.accountant.dto.TAmsGlbalchkOccurDTO>
 * <AUTHOR> @date 2020-7-28
 */
    List<TAmsGlbalchkOccurDTO> caculateGlCheckOcur(TPmsGlacgnDTO tPmsGlacgnDTO);
}
