package com.anytech.anytxn.account.job.autopayment.step;

import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.account.base.service.IAccountManageInfoService;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2019-09-02
 **/
@Slf4j
public class AutoPayProcessor implements ItemProcessor<AutoEbitSignUpInforDTO, AutoPaymentBO> {
    @Autowired
    private IAccountManageInfoService accountManageInfoService;

    @Override
    public AutoPaymentBO process(AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        log.info("签约的数据{}", autoEbitSignUpInfor.getId());
        return accountManageInfoService.batchProcess4AutoPayment(autoEbitSignUpInfor);
    }
}
