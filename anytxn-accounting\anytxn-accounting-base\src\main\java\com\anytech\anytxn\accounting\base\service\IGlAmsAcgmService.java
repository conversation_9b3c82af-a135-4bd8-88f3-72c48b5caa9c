package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgmDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
/**
 * <AUTHOR>
public interface IGlAmsAcgmService {
/**
 * TODO
  * @param page
 * @param rows
 * @param postingDate
 * @return com.anytech.anytxn.biz.common.dto.PageResultDTO<com.anytech.anytxn.accountant.dto.TAmsGlacgmDTO>
 * <AUTHOR> @date 2020-7-28
 */
    PageResultDTO<TAmsGlacgmDTO> getGlAmsAcgmByPage(Integer page, Integer rows, String postingDate);


}
