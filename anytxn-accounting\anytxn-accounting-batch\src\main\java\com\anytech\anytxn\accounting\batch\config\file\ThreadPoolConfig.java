package com.anytech.anytxn.accounting.batch.config.file;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ThreadPoolConfig {
    @Bean
    public TaskExecutor accountantTaskExe(){
        ThreadPoolTaskExecutor poolTaskExecutor = new ThreadPoolTaskExecutor();
        poolTaskExecutor.setCorePoolSize(2);
        poolTaskExecutor.setThreadNamePrefix("accountant_data_");
        poolTaskExecutor.setQueueCapacity(5000);
        return poolTaskExecutor;
    }
}
