package com.anytech.anytxn.account.job.paymentfile.step.repay;

import com.anytech.anytxn.account.job.paymentfile.config.RepaymentFileConfig;
import com.anytech.anytxn.central.base.domain.dto.MappingObj;
import com.anytech.anytxn.central.base.enums.IdentityTypeEnum;
import com.anytech.anytxn.central.client.mapping.MappingFeign;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.google.common.collect.Maps;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.utils.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.io.*;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 还款文件拆分
 * <AUTHOR>
 * @date 2021/1/7
 */
@Slf4j
public class RepaymentFileSplitTasklet implements Tasklet {

    private final String shardFilePathPre;
    private final String inputFilePath;
    private final String fileName;
    private final int errorSli;

    public RepaymentFileSplitTasklet(String shardFilePathPre, String inputFilePath, String fileName, int errorSli) {
        this.shardFilePathPre = shardFilePathPre;
        this.inputFilePath = inputFilePath;
        this.fileName = fileName;
        this.errorSli = errorSli;
    }

    @Autowired
    private MappingFeign generalIdentityMappingService;

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        // 只处理当前机构文件
        String orgNumber = OrgNumberUtils.getOrg();
        StopWatch sw = new StopWatch();
        sw.start();

        // 输出流缓存，避免重复开启关闭(不同分片不同输出流)
        Map<Integer, BufferedWriter> cache = Maps.newHashMap();
        try {
            // 目标文件
            File file = FileUtil.getFileFile(inputFilePath);
            if (file != null) {
                try (BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(file),"GBK"))){
                    String str = null;
                    // 一行一行处理
                    while ((str = br.readLine()) != null) {
                        if (StringUtils.isNotEmpty(str)) {
                            Integer k = -1;
                            try {
                                // 解析
                                List<String> context = lineTokenizer(str);
                                // 检查数据
                                check(context);
                                //输出到对应到文件中
                                String card = context.get(7);
                                AnyTxnHttpResponse<MappingObj> mappingObj = generalIdentityMappingService.getShardValueByOrgNumAndRouteMap(orgNumber, IdentityTypeEnum.BANK_CARD, card);
                                if (mappingObj == null) {
                                    log.warn("管理账户号无法识别分区id, 跳过处理, 行数据: {}", str);
                                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.AM_NE);
                                }
                                // 输出到分片文件文件
//                                k = mappingObj.getData().getShardValue() + 1;
                                //专门针对运营单分片处理
                                k = mappingObj.getData().getShardValue();
                                log.info("k:{}",k);
                            }catch (Exception e){
                                k = errorSli;
                                log.error(e.getMessage(), e);
                                log.error("还款文键内容异常，分配到分片{}处理，行内容： {}", errorSli, str);
                            }

                            if (!cache.containsKey(k)) {
                                // 输出文件不需要区分机构，在业务处理层区分即可
                                String outFilePath = shardFilePathPre + k + File.separator + fileName;
                                File outFile = FileUtil.mkdirFile(outFilePath, true);

                                // 支持追加
                                BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile, true),"GBK"));
                                cache.put(k, bw);

                                // 输出
                                bw.write(str);
                            } else {
                                // 追加
                                cache.get(k).append("\n").append(str);
                            }
                        }
                    }
                } catch (Exception e){
                    log.error(e.getMessage(), e);
                }
                FileUtils.deleteQuietly(file);
            }
        }finally {
            if (!cache.isEmpty()) {
                for (Map.Entry<Integer, BufferedWriter> entry : cache.entrySet()) {
                    entry.getValue().close();
                }
            }
        }

        sw.stop();
        log.info("拆分扣款文件完成，处理文件：{}, 耗时：{}", inputFilePath, sw.getTotalTimeMillis());

        return RepeatStatus.FINISHED;
    }

    /**
     * 数据检查
     * @param context
     */
    public void check(List<String> context){
        if (context.size() < 12) {
            log.error("还款文件格式错误！ 内容: {}", context);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.FFE);
        }

        if (context.get(7) == null) {
            log.error("还款文件卡号不存在！ 内容: {}", context);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.FFE);
        }

        if (context.get(0) == null) {
            log.error("还款文件机构号不存在！ 内容: {}", context);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.FFE);
        }
    }

    /**
     * 行拆分
     */
    public List<String> lineTokenizer(String line){
        int[] condition = RepaymentFileConfig.condition;
        long count = Arrays.stream(condition).sum();
        // 数据格式错误直接返回远数据
        if (line.length() < count) {
            return Lists.newArrayList(line);
        }

        List<String> result = Lists.newArrayList();
        char[] chars = line.toCharArray();

        int ps = 0;
        for (int len : condition) {
            result.add(new String(chars, ps, len).trim());
            ps += len;
        }

        return result;
    }
}
