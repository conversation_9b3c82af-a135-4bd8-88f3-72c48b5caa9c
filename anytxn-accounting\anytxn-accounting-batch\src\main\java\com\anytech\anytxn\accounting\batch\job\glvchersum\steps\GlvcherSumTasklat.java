package com.anytech.anytxn.accounting.batch.job.glvchersum.steps;

import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.base.constants.AccountantConstants;
import com.anytech.anytxn.accounting.mapper.AccountantGlvcherSumSelfMapper;
import com.anytech.anytxn.accounting.base.domain.model.TAmsGlvcherSumC;
import com.anytech.anytxn.accounting.base.utils.AmountUtil;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2020/11/3
 */
@Slf4j
public class GlvcherSumTasklat implements Tasklet {

    @Value("#{stepExecutionContext['fromId']}")
    private String fromId;
    @Value("#{stepExecutionContext['endId']}")
    private String endId;

    @Autowired
    private AccountantGlvcherSelfMapper amsGlvcherSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private AccountantGlvcherSumSelfMapper accountantGlvcherSumSelfMapper;
    @Autowired
    private AccountantGlvcherSelfMapper accountantGlvcherSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        List<AccountantGlvcher> accountantGlvchers = amsGlvcherSelfMapper.selectByPartition(organizationInfo.getOrganizationNumber(), organizationInfo.getToday(), Integer.valueOf(fromId), Integer.valueOf(endId));

        if (CollectionUtils.isEmpty(accountantGlvchers)) {
            return RepeatStatus.FINISHED;
        }

        List<AccountantGlvcher> updates = Lists.newArrayList();
        Map<String, TAmsGlvcherSumC> cache = new HashMap<>(accountantGlvchers.size());
        AtomicInteger atomicInteger = new AtomicInteger(0);

        for (AccountantGlvcher accountantGlvcher : accountantGlvchers) {

            accountantGlvcher.setUpdateTime(LocalDateTime.now());
            accountantGlvcher.setUpdateBy("admin");
            accountantGlvcher.setVersionNumber(accountantGlvcher.getVersionNumber() + 1);
            // 标记已处理
            accountantGlvcher.setProcessType("2");
            // 更新
            updates.add(accountantGlvcher);

            String k = accountantGlvcher.getBranchid() + accountantGlvcher.getCurrCode() + accountantGlvcher.getGlAcct() + accountantGlvcher.getDrcr();

            TAmsGlvcherSumC sumDTO;
            if (cache.containsKey(k)) {
                sumDTO = cache.get(k);
            } else {
                //会计汇总传票复制
                sumDTO = copyTAmsGlvcherSum(accountantGlvcher);
                sumDTO.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                sumDTO.setDrcr(accountantGlvcher.getDrcr());
                sumDTO.setOccurDb(BigDecimal.ZERO);
                sumDTO.setOccurCr(BigDecimal.ZERO);
                sumDTO.setCntCr(BigDecimal.ZERO);
                sumDTO.setCntDr(BigDecimal.ZERO);
                sumDTO.setVersionNumber(1L);
                sumDTO.setProcessInd("0");
            }

            if ("D".equals(accountantGlvcher.getDrcr())) {
                sumDTO.setOccurDb(
                        AmountUtil.addAmount(sumDTO.getOccurDb(), accountantGlvcher.getGlAmount()));
                sumDTO.setCntDr(
                        AmountUtil.addAmount(sumDTO.getCntDr(), accountantGlvcher.getGlCnt())
                );
            }
            if ("C".equals(accountantGlvcher.getDrcr())) {
                sumDTO.setOccurCr(
                        AmountUtil.addAmount(sumDTO.getOccurCr(), accountantGlvcher.getGlAmount()));
                sumDTO.setCntCr(
                        AmountUtil.addAmount(sumDTO.getCntCr(), accountantGlvcher.getGlCnt())
                );
            }

            cache.put(k, sumDTO);

            int incrementAndGet = atomicInteger.incrementAndGet();

            if (incrementAndGet % 500 == 0){
                accountantGlvcherSelfMapper.updateBatch(updates);
                log.info("");
                updates.clear();
            }
        }

        accountantGlvcherSelfMapper.updateBatch(updates);
        updates.clear();





       /* // 更新会计传票
        if (updates.size() > 500) {
            List<List<AccountantGlvcher>> partition = Lists.partition(updates, 500);
            if (partition.size() >= 500) {
                log.info("有超过500条的传票需要更新：{}", partition.size());
                for (List<AccountantGlvcher> glvchers : partition) {
                    accountantGlvcherSelfMapper.updateBatch(glvchers);
                    // help gc
                    glvchers.clear();
                }
            } else {
                log.info("少于500条传票需要更新：par:{},upda:{}", partition.size(), updates.size());
                accountantGlvcherSelfMapper.updateBatch(updates);
            }

        } else {
            log.info("少于500条传票需要更新2：upda:{}", updates.size());
            accountantGlvcherSelfMapper.updateBatch(updates);
        }*/

        // 更新汇总
        Collection<TAmsGlvcherSumC> glvcherSums = cache.values();
        List<TAmsGlvcherSumC> tAmsGlvcherSumCS = Lists.newArrayList(glvcherSums);
        // help gc
        cache.clear();
        glvcherSums.clear();

        if (tAmsGlvcherSumCS.size() > 500) {
            List<List<TAmsGlvcherSumC>> partition = Lists.partition(tAmsGlvcherSumCS, 500);
            for (List<TAmsGlvcherSumC> glvcherSum : partition) {
                accountantGlvcherSumSelfMapper.insertBatch(glvcherSum);
                glvcherSum.clear();
            }
        } else {
            accountantGlvcherSumSelfMapper.insertBatch(tAmsGlvcherSumCS);
        }
        return RepeatStatus.FINISHED;
    }


    public TAmsGlvcherSumC copyTAmsGlvcherSum(AccountantGlvcher source) {
        TAmsGlvcherSumC tAmsGlvcherSum = new TAmsGlvcherSumC();
        tAmsGlvcherSum.setOrganizationNumber(source.getOrganizationNumber());
        tAmsGlvcherSum.setBranchid(source.getBranchid());
        tAmsGlvcherSum.setFundId(source.getFundId());
        tAmsGlvcherSum.setCurrCode(source.getCurrCode());
        tAmsGlvcherSum.setGlAcct(source.getGlAcct());
        tAmsGlvcherSum.setOccurDb(source.getGlAmount());
        tAmsGlvcherSum.setOccurCr(source.getGlAmount());
        tAmsGlvcherSum.setPostingDate(source.getPostingDate());
        tAmsGlvcherSum.setCreateTime(LocalDateTime.now());
        tAmsGlvcherSum.setUpdateTime(LocalDateTime.now());
        tAmsGlvcherSum.setUpdateBy(AccountantConstants.DEFAULT_USER);
        return tAmsGlvcherSum;
    }
}
