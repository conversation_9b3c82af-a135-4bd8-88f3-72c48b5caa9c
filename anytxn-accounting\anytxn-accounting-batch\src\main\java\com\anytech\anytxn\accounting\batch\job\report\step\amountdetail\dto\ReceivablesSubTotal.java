package com.anytech.anytxn.accounting.batch.job.report.step.amountdetail.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020/9/16 10:01
 */
@Getter
@Setter
@ToString
public class ReceivablesSubTotal {

    /**小计**/
    private String bankNumber;
    //币种
    private String currencyCode;
    //产品ID
    private String productId;
    //科目号
    private String subjectNumber;
    //借贷方向
    private String debitCreditIndcator;
    //发生额总计
    private BigDecimal createAmountTotal;
}
