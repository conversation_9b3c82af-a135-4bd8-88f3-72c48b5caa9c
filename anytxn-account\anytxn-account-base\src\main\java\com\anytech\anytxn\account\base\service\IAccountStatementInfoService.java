package com.anytech.anytxn.account.base.service;


import com.anytech.anytxn.business.base.transaction.domain.dto.CardStatementDTO2;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.ModifyPaymentDueDateDTO;

import com.anytech.anytxn.business.base.transaction.domain.dto.CardStatementDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 账单
 *
 * <AUTHOR>
 * @date 2018-09-30 11:31
 **/
public interface IAccountStatementInfoService {

    /**
     * 根据管理账户id查询24期账单
     * @param accountManageInfoId 管理账户id
     * @param cardNumber 卡号
     * @return accountStatementInfo
     */
    List<AccountStatementInfoDTO> findLastedStatementInfo(String accountManageInfoId, String cardNumber);


    PageResultDTO<CardStatementDTO> findCardStatementInfos(CardStatementDTO2 cardStatementBO);


    /**
     * 更新账单金额
     * @param accountStatementId 账单id
     * @param balance 金额
     * @return int
     */
    int modifyAccountStatementInfo(String accountStatementId, BigDecimal balance);

    /**
     * 更新延迟还款日
     * @param modifyPaymentDueDateDto
     */
    void modifyPaymentDueDate(ModifyPaymentDueDateDTO modifyPaymentDueDateDto);

    /**
     *新增统计账户
     * @param accountStatementInfoDTO
     */
    void  registAccountStatementInfo(AccountStatementInfoDTO accountStatementInfoDTO);

    /**
     * 修改统计账户
     * @param accountStatementInfoDTO
     */
    void  modifyAccountStatementInfo(AccountStatementInfoDTO accountStatementInfoDTO);

    /**
     * 根据账单id查询账单信息
     * @param statementId 账单ID
     * @return AccountStatementInfoDTO
     */
    AccountStatementInfoDTO findStatementInfo(String statementId);

    /**
     * 根据管理账户id和时间
     * @param accountManageInfoId
     * @param date
     * @return
     */
    AccountStatementInfoDTO findStatementInfoByIdAndDate(String accountManageInfoId, LocalDate date);

    /**
     * 获取当日需要处理的管理账户数
     *
     * @param partitionKey 分区区间
     * @param organizations 机构list
     * @return int
     */
    int getAccountManagementCount(String partitionKey, List<Map<String, Object>> organizations);

    /**
     * 获取管理账户id
     *
     * @param partitionKey 分区区间
     * @param organizations 机构list
     * @param rowNumbers rownumber list
     * @return List<String>
     */
    List<String> queryAccountManagementIds(String partitionKey, List<Map<String, Object>> organizations, List<Integer> rowNumbers);

    /**
     * 获取期末余额
     *
     * @param accountManagementId 管理账户id
     * @param lastStatementDate 上一账单日
     * @return BigDecimal
     */
    BigDecimal getCloseBalance(String accountManagementId, LocalDate lastStatementDate);

    /**
     * 批量查询，根据管理账户id和账单日
     * @param statementList
     * @return
     */
    List<AccountStatementInfoDTO> selectByManageIdAndDate(List<Map<String, Object>> statementList);
}
