package com.anytech.anytxn.account.base.service;

import com.anytech.anytxn.business.base.account.domain.dto.AccountOptionalReqDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountOptionalResDTO;

import java.util.List;

/**
 * 管理账户配置信息
 *
 * <AUTHOR>
 * @date 2022-09-19
 */
public interface IAccountOptinalInfoService {


    /**
     * 根据管理账户Id查询账户配置信息
     *
     * @param accountManagementId 管理账户Id
     * @return 管理账户
     */
    AccountOptionalResDTO findByAccountManagementId(String accountManagementId);

    /**
     * 添加账户配置信息
     *
     * @param accountOptionalReqDTO Dto
     */
    boolean insertAccountOptinalInfo(AccountOptionalReqDTO accountOptionalReqDTO);

    /**
     * 更新账户配置信息
     *
     * @param accountManagementInfoDTO 管理账户信息
     * @return int
     */
    boolean modifyAccountOptinalInfo(AccountOptionalReqDTO accountManagementInfoDTO);

    /**
     * 查询管理账户配置信息
     *
     * @param condition C 客户层
     *                  A 账户层
     * @return int
     */
    List<AccountOptionalResDTO> findByCondition(String condition , String id);

}