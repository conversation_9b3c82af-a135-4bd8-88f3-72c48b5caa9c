package com.anytech.anytxn.accounting.batch.config;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2020/9/14
 */
@Getter
@Setter
public abstract class BaseDMQYConfigurer extends BaseConfigurer{

    /**
     * 通用模版扩展目录
     */
    private String cmTempleFilePath;

    /**
     * 通用文件扩展目录
     */
    private String cmFilePath;

    /**
     * 日输出文件名称
     */
    private String dayFileName;
    /**
     * 日模版名称
     */
    private String dayTempleFileName;
    /**
     * 日模版模版文件扩展路径
     */
    private String dayTempleFilePath;
    /**
     * 日模版输出文件扩展路径
     */
    private String dayExportFilePath;


    /**
     * 月输出文件
     */
    private String monthFileName;
    /**
     * 月模版文件
     */
    private String monthTempleFileName;
    /**
     * 月模版模版文件扩展路径
     */
    private String monthTempleFilePath;
    /**
     * 月模版输出文件扩展路径
     */
    private String monthExportFilePath;


    /**
     * 季度输出文件
     */
    private String quarterFileName;
    /**
     * 季度模版文件
     */
    private String quarterTempleFileName;
    /**
     * 季度模版模版文件扩展路径
     */
    private String quarterTempleFilePath;
    /**
     * 季度模版输出文件扩展路径
     */
    private String quarterExportFilePath;


    /**
     * 年输出文件
     */
    private String yearFileName;
    /**
     * 年模版文件
     */
    private String yearTempleFileName;
    /**
     * 年模版模版文件扩展路径
     */
    private String yearTempleFilePath;
    /**
     * 年模版输出文件扩展路径
     */
    private String yearExportFilePath;

    /**
     * 输出文件名称
     */
    public String outFileName(TimeTypeEnum timeTypeEnum){
        switch (timeTypeEnum){
            case D:
                return dayFileName + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case M:
                return monthFileName + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case Q:
                return quarterFileName + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case Y:
                return yearFileName + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            default: return "";
        }
    }

    /**
     * 输出文件
     */
    public String outFile(TimeTypeEnum timeTypeEnum){
        switch (timeTypeEnum){
            case D:
                // 优先使用指定文件目录，然后使用通用指定目录
                String dex = StringUtils.isEmpty(dayExportFilePath) ? cmFilePath : dayExportFilePath;
                return StringUtils.isEmpty(dex) ? commonConfigurer.exportFilePath : commonConfigurer.exportFilePath + dex;
            case M:
                // 优先使用指定文件目录，然后使用通用指定目录
                String mex = StringUtils.isEmpty(monthExportFilePath) ? cmFilePath : monthExportFilePath;
                return StringUtils.isEmpty(mex) ? commonConfigurer.exportFilePath : commonConfigurer.exportFilePath + mex;
            case Q:
                // 优先使用指定文件目录，然后使用通用指定目录
                String qex = StringUtils.isEmpty(quarterExportFilePath) ? cmFilePath : quarterExportFilePath;
                return StringUtils.isEmpty(qex) ? commonConfigurer.exportFilePath : commonConfigurer.exportFilePath + qex;
            case Y:
                // 优先使用指定文件目录，然后使用通用指定目录
                String yex = StringUtils.isEmpty(yearExportFilePath) ? cmFilePath : yearExportFilePath;
                return StringUtils.isEmpty(yex) ? commonConfigurer.exportFilePath : commonConfigurer.exportFilePath + yex;
            default: return "";
        }
    }

    /**
     * 模版文件
     */
    public String templeFile(TimeTypeEnum timeTypeEnum){

        switch (timeTypeEnum){
            case D:
                // 优先使用指定文件目录，然后使用通用指定目录
                String dex = StringUtils.isEmpty(dayTempleFilePath) ? cmTempleFilePath : dayTempleFilePath;
                String dex2 = StringUtils.isEmpty(dex) ? commonConfigurer.templateFilePath : commonConfigurer.templateFilePath + dex;
                // 优先使用指定文件名称，然后使用通用文件名称
                String dnx = StringUtils.isEmpty(dayTempleFileName) ? templateFileName : dayTempleFileName;
                return dex2 + File.separator + dnx;
            case M:
                // 优先使用指定文件目录，然后使用通用指定目录
                String mex = StringUtils.isEmpty(monthTempleFilePath) ? cmTempleFilePath : monthTempleFilePath;
                String mex2 = StringUtils.isEmpty(mex) ? commonConfigurer.templateFilePath : commonConfigurer.templateFilePath + mex;
                // 优先使用指定文件名称，然后使用通用文件名称
                String mnx = StringUtils.isEmpty(monthTempleFileName) ? templateFileName : monthTempleFileName;
                return mex2 + File.separator + mnx;
            case Q:
                // 优先使用指定文件目录，然后使用通用指定目录
                String qex = StringUtils.isEmpty(quarterTempleFilePath) ? cmTempleFilePath : quarterTempleFilePath;
                String qex2 = StringUtils.isEmpty(qex) ? commonConfigurer.templateFilePath : commonConfigurer.templateFilePath + qex;
                // 优先使用指定文件名称，然后使用通用文件名称
                String qnx = StringUtils.isEmpty(quarterTempleFileName) ? templateFileName : quarterTempleFileName;
                return qex2 + File.separator + qnx;
            case Y:
                // 优先使用指定文件目录，然后使用通用指定目录
                String yex = StringUtils.isEmpty(yearTempleFilePath) ? cmTempleFilePath : yearTempleFilePath;
                String yex2 = StringUtils.isEmpty(yex) ? commonConfigurer.templateFilePath : commonConfigurer.templateFilePath + yex;
                // 优先使用指定文件名称，然后使用通用文件名称
                String ynx = StringUtils.isEmpty(yearTempleFileName) ? templateFileName : yearTempleFileName;
                return yex2 + File.separator + ynx;
            default: return "";
        }
    }
}
