package com.anytech.anytxn.accounting.batch.job.report.step.cardtrans.dto2;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2020/10/15 18:15
 */
@Getter
@Setter
@ToString
public class TransTotalTotal {

    private String debitSettleAmountDesc;//贷记卡 借方结算金额合计

    private String credSettleAmountDesc; //贷记卡 贷方结算金额合计

    private String debitTransCount;//贷记卡 借方交易笔数

    private String creditTransCount;//贷记卡 贷方交易笔数

    private String quaDebitSettleAmountDesc;//准贷记卡 借方结算金额合计

    private String quaCredSettleAmountDesc; //准贷记卡 贷方结算金额合计

    private String quaDebitTransCount;//准贷记卡 借方交易笔数

    private String quaCreditTransCount;//准贷记卡 贷方交易笔数

    private String totalDebitSettleAmountDesc;//合计 借方结算金额合计

    private String totalCredSettleAmountDesc; //合计 贷方结算金额合计

    private String totalSettleAmountDesc; //合计 结算金额合计

    private String totalDebitTransCount;//合计 借方交易笔数

    private String totalCreditTransCount;//合计 贷方交易笔数

    private String totalTransCount;//合计 交易笔数
}
