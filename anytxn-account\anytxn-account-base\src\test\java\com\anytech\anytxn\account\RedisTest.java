/*
package com.anytech.anytxn.accounting;

import com.anytech.anytxn.parameter.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

*/
/**
 * <AUTHOR>
 * @date 2019-07-29 10:08
 **//*

@RunWith(SpringRunner.class)
@SpringBootTest
public class RedisTest {

    @Autowired
    private ParameterClient parameterClient;

    @Test
    public void testOne() {
        parameterClient.getOrganizationInfo("0001");
    }

    @Test
    public void testTwo() {
        parameterClient.getProductInfo("0001","P00001","156");
    }
}
*/
