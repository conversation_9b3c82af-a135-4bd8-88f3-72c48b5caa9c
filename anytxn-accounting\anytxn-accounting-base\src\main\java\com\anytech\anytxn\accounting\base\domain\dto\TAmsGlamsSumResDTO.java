package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.accounting.base.enums.AbsStatusEnum;
import com.anytech.anytxn.accounting.base.enums.AmortizeIndEnum;
import com.anytech.anytxn.accounting.base.enums.BalTypeEnum;
import com.anytech.anytxn.accounting.base.enums.DrcrEnum;
import com.anytech.anytxn.accounting.base.enums.FinanceStatusEnum;
import com.anytech.anytxn.accounting.base.enums.InterestIndEnum;
import com.anytech.anytxn.accounting.base.enums.ModuleFlagEnum;
import com.anytech.anytxn.accounting.base.enums.OrigTxnAbsIndEnum;
import com.anytech.anytxn.accounting.base.enums.PriceTaxFlgEnum;
import com.anytech.anytxn.accounting.base.enums.TransactionAttributeEnum;
import com.anytech.anytxn.accounting.base.enums.TxnIndEnum;
import com.anytech.anytxn.common.core.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2019-12-04 17:51
 **/
@Getter
@Setter
public class TAmsGlamsSumResDTO extends BaseEntity {

    /**
     * 技术主键
     * 表字段:ID
     */
    @Schema(description =  "技术主键")
    private String id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    @Schema(description =  "分行号")
    private String branchid;

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    @Schema(description =  "账户管理信息id")
    private String accountManagementId;

    /**
     * 产品代码
     * 表字段:ACCT_LOGO
     */
    @Schema(description =  "产品代码")
    private String acctLogo;

    private String acctLogoDesc;

    /**
     * 全局业务流水号
     * 表字段:GLOBAL_FLOW_NO
     */
    @Schema(description =  "全局业务流水号")
    private String globalFlowNo;

    /**
     * 模块标识
     * 表字段:MODULE_FLAG
     */
    @Schema(description =  "模块标识")
    private String moduleFlag;

    /**
     * 交易码
     * 表字段:TXN_CODE
     */
    @Schema(description =  "交易码")
    private String txnCode;

    private String txnCodeDesc;

    /**
     * 入账日期
     * 表字段:POSTING_DATE
     */
    @Schema(description =  "入账日期")
    private LocalDate postingDate;

    /**
     * 入账币种
     * 表字段:POSTING_CURRENCY_CODE
     */
    @Schema(description =  "入账币种")
    private String postingCurrencyCode;

    /**
     * 入账金额
     * 表字段:POSTING_AMT
     */
    @Schema(description =  "入账金额")
    private BigDecimal postingAmt;

    /**
     * 入账金额
     * 表字段:FINANCE_STATUS
     */
    @Schema(description =  "入账金额")
    private String financeStatus;

    /**
     * 表内表外标识
     * 表字段:INTEREST_IND
     */
    @Schema(description =  "表内表外标识")
    private String interestInd;

    /**
     * 价税分离标识
     * 表字段:PRICE_TAX_FLG
     */
    @Schema(description =  "价税分离标识")
    private String priceTaxFlg;

    /**
     * 原始交易码
     * 表字段:TXN_CODE_ORIG
     */
    @Schema(description =  "原始交易码")
    private String txnCodeOrig;

    private String txnCodeOrigDesc;

    /**
     * 余额类型
     * 表字段:BAL_TYPE
     */
    @Schema(description =  "余额类型")
    private String balType;

    /**
     * 交易标志
     * 表字段:TXN_IND
     */
    @Schema(description =  "交易标志")
    private String txnInd;

    /**
     * 余额结转标志
     * 表字段:BAL_PROCESS_IND
     */
    @Schema(description =  "余额结转标志")
    private String balProcessInd;

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    @Schema(description =  "资产出表状态")
    private String absStatus;

    /**
     * 资产包编号
     * 表字段:ASSET_NO
     */
    @Schema(description =  "资产包编号")
    private String assetNo;

    /**
     * 原始交易ABS借贷记标志
     * 表字段:ORIG_TXN_ABS_IND
     */
    @Schema(description =  "原始交易ABS借贷记标志")
    private String origTxnAbsInd;

    /**
     * 分期订单号
     * 表字段:ORDER_ID
     */
    @Schema(description =  "分期订单号")
    private String orderId;

    /**
     * 处理标识
     * 表字段:PROCESS_IND
     */
    @Schema(description =  "处理标识")
    private String processInd;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    @Schema(description =  "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    @Schema(description =  "更新日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    @Schema(description =  "更新用户")
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    @Schema(description =  "版本号")
    private Long versionNumber;
    /**
     * 渠道ID
     * 表字段:CHANNEL_ID
     */
    private String channelId;

    /**
     * 子渠道ID
     * 表字段:SUBCHANNEL_ID
     */
    private String subchannelId;

    /**
     * 资金源ID
     * 表字段:FUND_ID
     */
    private String fundId;

    /**
     * 平台ID
     * 表字段:PLATFORM_ID
     */
    private String platformId;

    /**
     * 商户ID
     * 表字段:MERCHANT_NUMBER
     */
    private String merchantNumber;
    /**
     * 表内表外标识
     * 表字段:INTEREST_IND
     */
    @Schema(description =  "表内表外标识")
    private String interestIndDesc;

    public String getInterestIndDesc() {
        return InterestIndEnum.getValue(interestInd);
    }

    public void setInterestIndDesc(String interestIndDesc) {
        this.interestIndDesc = interestIndDesc;
    }

    /**
     * 价税分离标识
     * 表字段:PRICE_TAX_FLG
     */
    @Schema(description =  "价税分离标识")
    private String priceTaxFlgDesc;

    public String getPriceTaxFlgDesc() {
        return PriceTaxFlgEnum.getValue(priceTaxFlg);
    }

    public void setPriceTaxFlgDesc(String priceTaxFlgDesc) {
        this.priceTaxFlgDesc = priceTaxFlgDesc;
    }

    /**
     * 余额类型
     * 表字段:BAL_TYPE
     */
    @Schema(description =  "余额类型")
    private String balTypeDesc;

    public String getBalTypeDesc() {
        return BalTypeEnum.getValue(balType);
    }

    public void setBalTypeDesc(String balTypeDesc) {
        this.balTypeDesc = balTypeDesc;
    }

    /**
     * 模块标识描述
     * 表字段:MODULE_FLAG
     */
    @Schema(description =  "模块标识描述")
    private String moduleFlagDesc;

    public String getModuleFlagDesc() {
        return ModuleFlagEnum.getValue(moduleFlag);
    }

    public void setModuleFlagDesc(String moduleFlagDesc) {
        this.moduleFlagDesc = moduleFlagDesc;
    }

    /**
     * 入账金额
     * 表字段:FINANCE_STATUS
     */
    @Schema(description =  "入账金额")
    private String financeStatusDesc;

    public String getFinanceStatusDesc() {
        return FinanceStatusEnum.getValue(financeStatus);
    }

    public void setFinanceStatusDesc(String financeStatusDesc) {
        this.financeStatusDesc = financeStatusDesc;
    }

    /**
     * 交易标志
     * 表字段:TXN_IND
     */
    @Schema(description =  "交易标志")
    private String txnIndDesc;

    public String getTxnIndDesc() {
        return TxnIndEnum.getValue(txnInd);
    }

    public void setTxnIndDesc(String txnIndDesc) {
        this.txnIndDesc = txnIndDesc;
    }

    /**
     * 资产出表状态
     * 表字段:ABS_STATUS
     */
    @Schema(description =  "资产出表状态")
    private String absStatusDesc;

    public String getAbsStatusDesc() {
        return AbsStatusEnum.getValue(absStatus);
    }

    public void setAbsStatusDesc(String absStatusDesc) {
        this.absStatusDesc = absStatusDesc;
    }

    /**
     * 原始交易ABS借贷记标志
     * 表字段:ORIG_TXN_ABS_IND
     */
    @Schema(description =  "原始交易ABS借贷记标志")
    private String origTxnAbsIndDesc;

    public String getOrigTxnAbsIndDesc() {
        return OrigTxnAbsIndEnum.getValue(origTxnAbsInd);
    }

    public void setOrigTxnAbsIndDesc(String origTxnAbsIndDesc) {
        this.origTxnAbsIndDesc = origTxnAbsIndDesc;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getSubchannelId() {
        return subchannelId;
    }

    public void setSubchannelId(String subchannelId) {
        this.subchannelId = subchannelId;
    }

    public String getFundId() {
        return fundId;
    }

    public void setFundId(String fundId) {
        this.fundId = fundId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getMerchantNumber() {
        return merchantNumber;
    }

    public void setMerchantNumber(String merchantNumber) {
        this.merchantNumber = merchantNumber;
    }
    /**
     * 交易属性
     */
    private String transactionAttribute;
    private String transactionAttributeDesc;
    /**
     * 借贷记属性
     */
    private String debitCreditIndicator;
    private String debitCreditIndicatorDesc;
    /**
     * 递延标识
     */
    private String amortizeInd;
    private String amortizeIndDesc;
    /**
     * 交易类型
     */
    private String transactionTypeCode;
    private String transactionTypeCodeDesc;



    /**
     * 规则ID
     */
    private String ruleId;

    private String ruleIdDesc;

    /**
     * 五级分类标识
     */
    private String fiveTypeIndicator;

    /**
     * abs类型
     */
    private String absType;

    public String getRuleIdDesc() {
        return ruleIdDesc;
    }

    public void setRuleIdDesc(String ruleIdDesc) {
        this.ruleIdDesc = ruleIdDesc;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getTransactionAttribute() {
        return transactionAttribute;
    }

    public String getTransactionAttributeDesc() {
        return TransactionAttributeEnum.getValue(transactionAttribute);
    }

    public String getDebitCreditIndicator() {
        return debitCreditIndicator;
    }

    public String getDebitCreditIndicatorDesc() {
        return DrcrEnum.getValue(debitCreditIndicator);
    }

    public String getAmortizeInd() {
        return amortizeInd;
    }

    public String getAmortizeIndDesc() {
        return AmortizeIndEnum.getValue(amortizeInd);
    }

    public String getTransactionTypeCode() {
        return transactionTypeCode;
    }

    public String getTransactionTypeCodeDesc() {
        return transactionTypeCodeDesc;
    }


    public void setTransactionAttribute(String transactionAttribute) {
        this.transactionAttribute = transactionAttribute;
    }

    public void setTransactionAttributeDesc(String transactionAttributeDesc) {
        this.transactionAttributeDesc = transactionAttributeDesc;
    }

    public void setDebitCreditIndicator(String debitCreditIndicator) {
        this.debitCreditIndicator = debitCreditIndicator;
    }

    public void setDebitCreditIndicatorDesc(String debitCreditIndicatorDesc) {
        this.debitCreditIndicatorDesc = debitCreditIndicatorDesc;
    }

    public void setAmortizeInd(String amortizeInd) {
        this.amortizeInd = amortizeInd;
    }

    public void setAmortizeIndDesc(String amortizeIndDesc) {
        this.amortizeIndDesc = amortizeIndDesc;
    }

    public void setTransactionTypeCode(String transactionTypeCode) {
        this.transactionTypeCode = transactionTypeCode;
    }

    public void setTransactionTypeCodeDesc(String transactionTypeCodeDesc) {
        this.transactionTypeCodeDesc = transactionTypeCodeDesc;
    }
    /**
     * 获取分行号
     *
     * @return branchid String
     */
    public String getBranchid() {
        return branchid;
    }

    /**
     * 设置分行号
     *
     * @param branchid 分行号
     */
    public void setBranchid(String branchid) {
        this.branchid = branchid == null ? null : branchid.trim();
    }

    /**
     * 获取账户管理信息id
     *
     * @return accountManagementId String
     */
    public String getAccountManagementId() {
        return accountManagementId;
    }

    /**
     * 设置账户管理信息id
     *
     * @param accountManagementId 账户管理信息id
     */
    public void setAccountManagementId(String accountManagementId) {
        this.accountManagementId = accountManagementId == null ? null : accountManagementId.trim();
    }

    /**
     * 获取产品代码
     *
     * @return acctLogo String
     */
    public String getAcctLogo() {
        return acctLogo;
    }

    /**
     * 设置产品代码
     *
     * @param acctLogo 产品代码
     */
    public void setAcctLogo(String acctLogo) {
        this.acctLogo = acctLogo == null ? null : acctLogo.trim();
    }

    /**
     * 获取全局业务流水号
     *
     * @return globalFlowNo String
     */
    public String getGlobalFlowNo() {
        return globalFlowNo;
    }

    /**
     * 设置全局业务流水号
     *
     * @param globalFlowNo 全局业务流水号
     */
    public void setGlobalFlowNo(String globalFlowNo) {
        this.globalFlowNo = globalFlowNo == null ? null : globalFlowNo.trim();
    }

    /**
     * 获取模块标识
     *
     * @return moduleFlag String
     */
    public String getModuleFlag() {
        return moduleFlag;
    }

    /**
     * 设置模块标识
     *
     * @param moduleFlag 模块标识
     */
    public void setModuleFlag(String moduleFlag) {
        this.moduleFlag = moduleFlag == null ? null : moduleFlag.trim();
    }

    /**
     * 获取交易码
     *
     * @return txnCode String
     */
    public String getTxnCode() {
        return txnCode;
    }

    /**
     * 设置交易码
     *
     * @param txnCode 交易码
     */
    public void setTxnCode(String txnCode) {
        this.txnCode = txnCode == null ? null : txnCode.trim();
    }

    /**
     * 获取入账日期
     *
     * @return postingDate LocalDate
     */
    public LocalDate getPostingDate() {
        return postingDate;
    }

    /**
     * 设置入账日期
     *
     * @param postingDate 入账日期
     */
    public void setPostingDate(LocalDate postingDate) {
        this.postingDate = postingDate;
    }

    /**
     * 获取入账币种
     *
     * @return postingCurrencyCode String
     */
    public String getPostingCurrencyCode() {
        return postingCurrencyCode;
    }

    /**
     * 设置入账币种
     *
     * @param postingCurrencyCode 入账币种
     */
    public void setPostingCurrencyCode(String postingCurrencyCode) {
        this.postingCurrencyCode = postingCurrencyCode == null ? null : postingCurrencyCode.trim();
    }

    /**
     * 获取入账金额
     *
     * @return postingAmt BigDecimal
     */
    public BigDecimal getPostingAmt() {
        return postingAmt;
    }

    /**
     * 设置入账金额
     *
     * @param postingAmt 入账金额
     */
    public void setPostingAmt(BigDecimal postingAmt) {
        this.postingAmt = postingAmt;
    }

    /**
     * 获取入账金额
     *
     * @return financeStatus String
     */
    public String getFinanceStatus() {
        return financeStatus;
    }

    /**
     * 设置入账金额
     *
     * @param financeStatus 入账金额
     */
    public void setFinanceStatus(String financeStatus) {
        this.financeStatus = financeStatus == null ? null : financeStatus.trim();
    }

    /**
     * 获取表内表外标识
     *
     * @return interestInd String
     */
    public String getInterestInd() {
        return interestInd;
    }

    /**
     * 设置表内表外标识
     *
     * @param interestInd 表内表外标识
     */
    public void setInterestInd(String interestInd) {
        this.interestInd = interestInd == null ? null : interestInd.trim();
    }

    /**
     * 获取价税分离标识
     *
     * @return priceTaxFlg String
     */
    public String getPriceTaxFlg() {
        return priceTaxFlg;
    }

    /**
     * 设置价税分离标识
     *
     * @param priceTaxFlg 价税分离标识
     */
    public void setPriceTaxFlg(String priceTaxFlg) {
        this.priceTaxFlg = priceTaxFlg == null ? null : priceTaxFlg.trim();
    }

    /**
     * 获取原始交易码
     *
     * @return txnCodeOrig String
     */
    public String getTxnCodeOrig() {
        return txnCodeOrig;
    }

    /**
     * 设置原始交易码
     *
     * @param txnCodeOrig 原始交易码
     */
    public void setTxnCodeOrig(String txnCodeOrig) {
        this.txnCodeOrig = txnCodeOrig == null ? null : txnCodeOrig.trim();
    }

    /**
     * 获取余额类型
     *
     * @return balType String
     */
    public String getBalType() {
        return balType;
    }

    /**
     * 设置余额类型
     *
     * @param balType 余额类型
     */
    public void setBalType(String balType) {
        this.balType = balType == null ? null : balType.trim();
    }

    /**
     * 获取交易标志
     *
     * @return txnInd String
     */
    public String getTxnInd() {
        return txnInd;
    }

    /**
     * 设置交易标志
     *
     * @param txnInd 交易标志
     */
    public void setTxnInd(String txnInd) {
        this.txnInd = txnInd == null ? null : txnInd.trim();
    }

    /**
     * 获取余额结转标志
     *
     * @return balProcessInd String
     */
    public String getBalProcessInd() {
        return balProcessInd;
    }

    /**
     * 设置余额结转标志
     *
     * @param balProcessInd 余额结转标志
     */
    public void setBalProcessInd(String balProcessInd) {
        this.balProcessInd = balProcessInd == null ? null : balProcessInd.trim();
    }

    /**
     * 获取资产出表状态
     *
     * @return absStatus String
     */
    public String getAbsStatus() {
        return absStatus;
    }

    /**
     * 设置资产出表状态
     *
     * @param absStatus 资产出表状态
     */
    public void setAbsStatus(String absStatus) {
        this.absStatus = absStatus == null ? null : absStatus.trim();
    }

    /**
     * 获取资产包编号
     *
     * @return assetNo String
     */
    public String getAssetNo() {
        return assetNo;
    }

    /**
     * 设置资产包编号
     *
     * @param assetNo 资产包编号
     */
    public void setAssetNo(String assetNo) {
        this.assetNo = assetNo == null ? null : assetNo.trim();
    }

    /**
     * 获取原始交易ABS借贷记标志
     *
     * @return origTxnAbsInd String
     */
    public String getOrigTxnAbsInd() {
        return origTxnAbsInd;
    }

    /**
     * 设置原始交易ABS借贷记标志
     *
     * @param origTxnAbsInd 原始交易ABS借贷记标志
     */
    public void setOrigTxnAbsInd(String origTxnAbsInd) {
        this.origTxnAbsInd = origTxnAbsInd == null ? null : origTxnAbsInd.trim();
    }

    /**
     * 获取分期订单号
     *
     * @return orderId String
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置分期订单号
     *
     * @param orderId 分期订单号
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * 获取处理标识
     *
     * @return processInd String
     */
    public String getProcessInd() {
        return processInd;
    }

    /**
     * 设置处理标识
     *
     * @param processInd 处理标识
     */
    public void setProcessInd(String processInd) {
        this.processInd = processInd == null ? null : processInd.trim();
    }

    /**
     * 获取创建日期
     *
     * @return createTime LocalDateTime
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建日期
     *
     * @param createTime 创建日期
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新日期
     *
     * @return updateTime LocalDateTime
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新日期
     *
     * @param updateTime 更新日期
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新用户
     *
     * @return updateBy String
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新用户
     *
     * @param updateBy 更新用户
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    /**
     * 获取版本号
     *
     * @return versionNumber Long
     */
    public Long getVersionNumber() {
        return versionNumber;
    }

    /**
     * 设置版本号
     *
     * @param versionNumber 版本号
     */
    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getFiveTypeIndicator() {
        return fiveTypeIndicator;
    }

    public void setFiveTypeIndicator(String fiveTypeIndicator) {
        this.fiveTypeIndicator = fiveTypeIndicator;
    }

    public String getAbsType() {
        return absType;
    }

    public void setAbsType(String absType) {
        this.absType = absType;
    }
}
