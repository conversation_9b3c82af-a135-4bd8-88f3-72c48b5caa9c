package com.anytech.anytxn.accounting.batch.config.file;

import com.anytech.anytxn.accounting.base.domain.model.AccountantGlTxnSum;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlTxnSumHeader;
import com.anytech.anytxn.accounting.service.GlvcherFileServiceImpl;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherSpecialInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.common.service.system.OrganizationInfoServiceImpl;
import com.anytech.batch.job.file.common.FileHeader;
import com.anytech.batch.job.file.handler.TextLineHandler;
import com.anytech.batch.job.file.write.FileBody;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJob;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJobConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
@Slf4j
@Configuration
public class TxnRecordSumConfig extends SimpleWriteFileBatchJobConfig<AccountantGlvcherSpecialInfo, AccountantGlTxnSum, Long> {
    @Resource
    private GlvcherFileServiceImpl glSumService;
    @Resource
    private AnytxnFilePathConfig txnSumFileFormatPathConfig;
    @Resource
    private OrganizationInfoServiceImpl organizationInfoService;
    @Resource
    private AccountantGlvcherSelfMapper accountantGlvcherSelfMapper;

    public TxnRecordSumConfig(JobBuilderFactory jobs,
                                 StepBuilderFactory steps,
                                 SqlSessionFactory sqlSessionFactory,
                                 GlvcherFileServiceImpl glSumService) {
        super(jobs, steps, sqlSessionFactory);
        this.glSumService = glSumService;
    }
    @Override
    public SimpleWriteFileBatchJob<AccountantGlvcherSpecialInfo, AccountantGlTxnSum, Long> getBatchJob() {
        String txnFilePath = txnSumFileFormatPathConfig.getCommonPath();
        log.info("txnSumPath: {}",txnFilePath);

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        LocalDate today = organizationInfo.getToday();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyMMdd");
        String format = dateTimeFormatter.format(today);

        SimpleWriteFileBatchJob<AccountantGlvcherSpecialInfo, AccountantGlTxnSum, Long> batchJob = SimpleWriteFileBatchJob.of();

        batchJob.name("txnSumFileProcess").path(txnFilePath).resolveClass(AccountantGlTxnSum.class)
                .fileName("TXN.RECORD.SUM.OUT." + format)
                .header(new FileHeader(AccountantGlTxnSumHeader.class, (TextLineHandler) () -> {
                    AccountantGlTxnSumHeader headerDto = new AccountantGlTxnSumHeader();
                    headerDto.setPostDate("Post_date");
                    headerDto.setAccountCode("Account");
                    headerDto.setAccountDesc("Account_name");
                    headerDto.setSource("Source");
                    headerDto.setTranCode("Tran_code");
                    headerDto.setTranCodeDesc("Tran_code_desc");
                    headerDto.setDrOrCr("Dr_or_Cr");
                    headerDto.setPostAmt("Post_amt");
                    headerDto.setPostCurr("Post_curr");
                    return headerDto;
                }))
                .body(FileBody.<AccountantGlvcherSpecialInfo, AccountantGlTxnSum, Long>of().queryId("com.anytech.anytxn.core.accountant.mapper.AccountantGlvcherSelfMapper.selectSpecificInfo")
                        .pageSize(1000)
                        .processorHandler(accountantGlvcherSpecialInfo ->
                                glSumService.buildGlTxnSum(accountantGlvcherSpecialInfo,today)));

        return batchJob;
    }
}
