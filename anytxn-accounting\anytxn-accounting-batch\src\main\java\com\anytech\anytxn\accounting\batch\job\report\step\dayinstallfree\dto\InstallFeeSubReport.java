package com.anytech.anytxn.accounting.batch.job.report.step.dayinstallfree.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/9/16 16:56
 */
@Getter
@Setter
@ToString
public class InstallFeeSubReport {
    private List<InstallFeeDetailReport> detailList;
    /**小计**/
    //分期产品id
    private String productId;
    //币种
    private String currencyCode;
    //条数
    private Integer subCount;
    //合计总额
    private BigDecimal amountTotal;

    private boolean end = false;
}
