package com.anytech.anytxn.accounting.batch.config.file.IrrgulardataFiles;

import com.anytech.anytxn.accounting.base.domain.model.AccountantGlamsSumOut;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlamsSumOutHeader;
import com.anytech.anytxn.accounting.service.IllegalDataFileServiceImpl;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlamsSum;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.common.service.system.OrganizationInfoServiceImpl;
import com.anytech.batch.job.file.common.FileHeader;
import com.anytech.batch.job.file.handler.TextLineHandler;
import com.anytech.batch.job.file.write.FileBody;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJob;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJobConfig;
import jakarta.annotation.Resource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;

@Configuration
public class IllegalGlamsSumFileConfig extends SimpleWriteFileBatchJobConfig<AccountantGlamsSum, AccountantGlamsSumOut, Long> {
    @Resource
    private IllegalDataFileServiceImpl ilegalDataFileServiceImpl;
    @Resource
    private AnytxnFilePathConfig illGlamsSumFileFormatPathConfig;
    @Resource
    private OrganizationInfoServiceImpl organizationInfoService;

    public IllegalGlamsSumFileConfig(JobBuilderFactory jobs, StepBuilderFactory steps, SqlSessionFactory sqlSessionFactory,IllegalDataFileServiceImpl ilegalDataFileServiceImpl) {
        super(jobs, steps, sqlSessionFactory);
        this.ilegalDataFileServiceImpl = ilegalDataFileServiceImpl;

    }

    @Override
    public SimpleWriteFileBatchJob<AccountantGlamsSum, AccountantGlamsSumOut, Long> getBatchJob() {
        String illGlamsSumFilePath = illGlamsSumFileFormatPathConfig.getCommonPath();

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        LocalDate today = organizationInfo.getToday();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyMMdd");
        String format = dateTimeFormatter.format(today);
        HashMap<String, Object> map = new HashMap<>();
        map.put("postingDate", organizationInfo.getToday());
        map.put("processInd","2");
        SimpleWriteFileBatchJob<AccountantGlamsSum, AccountantGlamsSumOut, Long> batchJob = SimpleWriteFileBatchJob.of();

        batchJob.name("illGlamsSumFileProcess").path(illGlamsSumFilePath).resolveClass(AccountantGlamsSumOut.class)
                .fileName("EXCEPTIONAL.GLAMS.SUM.OUT." + format)
                .header(new FileHeader(AccountantGlamsSumOutHeader.class, (TextLineHandler) () -> {
                    AccountantGlamsSumOutHeader headerDto = new AccountantGlamsSumOutHeader();
                    return headerDto;
                }))
                .body(FileBody.<AccountantGlamsSum, AccountantGlamsSumOut, Long>of().queryId("com.anytech.anytxn.core.accountant.mapper.AccountantGlamsSumSelfMapper.selectGlamsSumByDateAndInd")
                        .pageSize(1000)
                        .parameterValues(map)
                        .processorHandler(detailInfo ->
                                ilegalDataFileServiceImpl.buildGlamsSum(detailInfo)));

        return batchJob;
    }
}
