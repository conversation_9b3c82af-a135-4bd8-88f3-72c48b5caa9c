/*
package com.anytech.anytxn.accounting.mapper;

import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.parameter.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;

*/
/**
 * <AUTHOR>
 * @date 2019-08-05 11:50
 **//*

@SpringBootTest
@RunWith(SpringRunner.class)
public class AccountStatementInfoSelfMapperTest {

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;

    @Test
    public void selectLastedStatementInfoByAccountManagementIdAndDate() {
        List<AccountStatementInfo> result = accountStatementInfoSelfMapper.selectLastedStatementInfoByAccountManagementIdAndDate("20190729043809306001005000000055");

        for (AccountStatementInfo a : result) {
            System.out.println(a.toString());

        }

    }

    @Test
    public void getAccountManagementCount() {
    }

    @Test
    public void queryAccountManagementIds() {
    }

    @Test
    public void selectByAccountManagementIdAndDateAndCurrency() {
    }

    @Test
    public void selectPaymentDueDate() {
    }

    @Test
    public void getCloseBalance() {
    }

    @Test
    public void selectByManageIdAndDate() {
    }
}*/
