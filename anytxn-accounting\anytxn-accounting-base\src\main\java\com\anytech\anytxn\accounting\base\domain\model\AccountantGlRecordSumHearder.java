package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TextLineObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TextLineObject(encoding = "UTF-8")
public class AccountantGlRecordSumHearder implements Serializable {
    /**
     * CP_FINEXT.CB_EXT_REF_NO 8 汇总日期
     */
    @FileField(order = 1, length = 9, rightPad = true)
    private String sumDate;

    @FileField(order = 2, length = 1, rightPad = true)
    private String dot=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 科目号
     */
    @FileField(order = 3, length = 7, rightPad = true)
    private String subjectCodeId;

    @FileField(order = 4, length = 1, rightPad = true)
    private String dot2=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 120 科目描述
     */
    @FileField(order = 5, length = 12, rightPad = true)
    private String subjectDesc;

    @FileField(order = 6, length = 1, rightPad = true)
    private String do3=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 2 借贷标志
     */
    @FileField(order = 7, length = 8, rightPad = true)
    private String debitCreditSide;

    @FileField(order = 8, length = 1, rightPad = true)
    private String dot4=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 总金额
     */
    @FileField(order = 9, length = 8, padChar = "0")
    private String totalAmt;

    @FileField(order = 10, length = 1, rightPad = true)
    private String dot5=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 3 币种
     */
    @FileField(order = 11, length = 9, rightPad = true)
    private String currency;

    @FileField(order = 12, length = 1, rightPad = true)
    private String dot6=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 6 账产品
     */
    @FileField(order = 13, length = 15, rightPad = true)
    private String accountProduct;

    @FileField(order = 14, length = 1, rightPad = true)
    private String dot7=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 模块标志
     */
    @FileField(order = 15, length = 8, rightPad = true)
    private String moduleFlag;

    @FileField(order = 16, length = 1, rightPad = true)
    private String dot8=",";

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 卡组织
     */
    @FileField(order = 17, length = 11, rightPad = true)
    private String crdOrganization;

}
