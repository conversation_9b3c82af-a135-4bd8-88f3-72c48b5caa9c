package com.anytech.anytxn.accounting.base.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 模块标识
 * @date 2020-02-16 14:06
 **/
public enum FinanceStatusEnum {
    /**
     * 0：应计
     * 1：非应计
     * 2：核销
     * 3：逾期
     * 4：ABS
     * 5：正常转逾期
     * 6：逾期转正常
     * 7：非应计转核销
     * 8：逾期转非应计
     * 9：非应计转逾期
     * A:应计转ABS
     * B:非应计转ABS
     * C:ABS转应计
     * D:ABS转非应计
     * E:逾期转ABS
     * F:非应计转正常
     */
    CORE("0", "应计"),
    CHARGE_OFF("1", "非应计"),
    TAX("2", "核销"),
    OVERDUE("3", "逾期"),
    INST("4", "ABS"),
    CO_OV("5", "正常转逾期"),
    OV_CO("6", "逾期转正常"),
    CH_TA("7", "非应计转核销"),
    OV_CH("8", "逾期转非应计"),
    CH_OV("9", "非应计转逾期"),
    A("A", "应计转ABS"),
    B("B", "非应计转ABS"),
    C("C", "ABS转应计"),
    D("D", "ABS转非应计"),
    E("E","逾期转ABS"),
    F("F","非应计转正常"),
    ;
    private String code;
    private String desc;


    FinanceStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String getValue(String code) {
        if (code == null) {
            return null;
        }
        FinanceStatusEnum[] var2 = values();
        int var3 = var2.length;
        for (int var4 = 0; var4 < var3; ++var4) {
            FinanceStatusEnum moduleFlagEnum = var2[var4];
            if (Objects.equals(code, moduleFlagEnum.getCode())) {
                return moduleFlagEnum.getDesc();
            }
        }
        return null;
    }
}
