package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsSumResDTO;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSum;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSumOracleLog;
import com.anytech.anytxn.common.core.base.PageResultDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * 交易会计汇总流水表
 * @date 2019-12-04 16:18
 **/
public interface IAccountSummaryService {

    /**
     * 根据id查询  交易会计汇总流水表
     *
     * @param id
     * @date 2020-7-28
     */
    TAmsGlamsSumResDTO selectByPrimaryKey(String id);


    /**
     * 分页查询交易会计汇总流水表
     *
     * @param page
     * @param rows
     * @param postingDate
     * @param accountManagementId
     * @param globalFlowNo
     * @param processInd
     * @date 2020-7-28
     */
    PageResultDTO<TAmsGlamsSumResDTO> findPage(Integer page, Integer rows, String postingDate, String accountManagementId, String globalFlowNo, String processInd);



    SubjectRecordSumOracleLog insertSumOracleRecord(SubjectRecordSum subjectRecordSum);



    Integer updateSuccessLogStatus(String id);



}
