package com.anytech.anytxn.accounting.batch.config.file;

import com.anytech.anytxn.accounting.base.domain.model.AccountantGlOriRecordDetail;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlOriRecordDetailHeader;
import com.anytech.anytxn.accounting.service.GlvcherFileServiceImpl;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherOriDetailInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.common.service.system.OrganizationInfoServiceImpl;
import com.anytech.batch.job.file.common.FileHeader;
import com.anytech.batch.job.file.handler.TextLineHandler;
import com.anytech.batch.job.file.write.FileBody;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJob;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJobConfig;
import jakarta.annotation.Resource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;

@Configuration
public class OriRecordDetailConfig extends SimpleWriteFileBatchJobConfig<AccountantGlvcherOriDetailInfo, AccountantGlOriRecordDetail, Long> {
    @Resource
    private GlvcherFileServiceImpl glDetailService;
    @Resource
    private AnytxnFilePathConfig ordFileFormatPathConfig;
    @Resource
    private OrganizationInfoServiceImpl organizationInfoService;

    public OriRecordDetailConfig(JobBuilderFactory jobs,
                     StepBuilderFactory steps,
                     SqlSessionFactory sqlSessionFactory,
                     GlvcherFileServiceImpl glDetailService) {
        super(jobs, steps, sqlSessionFactory);
        this.glDetailService = glDetailService;
    }
    @Override
    public SimpleWriteFileBatchJob<AccountantGlvcherOriDetailInfo, AccountantGlOriRecordDetail, Long> getBatchJob() {
        String ordFilePath = ordFileFormatPathConfig.getCommonPath();

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        LocalDate today = organizationInfo.getToday();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyMMdd");
        String format = dateTimeFormatter.format(today);
        HashMap<String, Object> map = new HashMap<>();
        map.put("postingDate", organizationInfo.getToday());
        SimpleWriteFileBatchJob<AccountantGlvcherOriDetailInfo, AccountantGlOriRecordDetail, Long> batchJob = SimpleWriteFileBatchJob.of();

        batchJob.name("ordFileProcess2").path(ordFilePath).resolveClass(AccountantGlOriRecordDetail.class)
                .fileName("ORI.RECORD.DETAIL.OUT." + format)
                .header(new FileHeader(AccountantGlOriRecordDetailHeader.class, (TextLineHandler) () -> {
                    AccountantGlOriRecordDetailHeader headerDto = new AccountantGlOriRecordDetailHeader();
                    headerDto.setSumDate("post_date");
                    headerDto.setSubjectCodeId("account");
                    headerDto.setSubjectDesc("account_name");
                    headerDto.setDebitCreditSide("Dr_or_Cr");
                    headerDto.setTotalAmt("post_amt");
                    headerDto.setSumCurrency("post_curr");
                    headerDto.setTxnCode("tran_code");
                    headerDto.setTxnDesc("tran_code_desc");
                    headerDto.setAccountProduct("Account_product");
                    headerDto.setCrdNumber("card_number");
                    headerDto.setMerchantId("MID");
                    headerDto.setTxnDate("tran_date");
                    headerDto.setSource("tran_source");
                    headerDto.setTxnCurrency("tran_curr");
                    headerDto.setTxnAmt("tran_amt");
                    headerDto.setCurrencyRate("exchange_rate");
                    headerDto.setRrn("RRN");
                    headerDto.setCardProduct("card_product");
                    headerDto.setCardOrganization("card_scheme");
                    headerDto.setModuleFlag("business");
                    headerDto.setAcqId("acq_id");

                    return headerDto;
                }))
                .body(FileBody.<AccountantGlvcherOriDetailInfo, AccountantGlOriRecordDetail, Long>of().queryId("com.anytech.anytxn.core.accountant.mapper.AccountantGlvcherSelfMapper.queryGlvchersForToday")
                        .pageSize(1000)
                        .parameterValues(map)
                        .processorHandler(detailInfo ->
                                glDetailService.buildOriRecordDetail(detailInfo,organizationInfo)));

        return batchJob;
    }
}
