package com.anytech.anytxn.account.base.service;

import com.anytech.anytxn.account.base.domain.dto.AccountLoyaltyInfoDTO;

/**
 * <AUTHOR>
 * @Date 2024/3/22  17:04
 * @Version 1.0
 */


public interface IAccountLoyaltyInfoService {

    /**
     * 更新账户权益信息
     *
     * @param accountLoyaltyInfo
     * @return
     */
    AccountLoyaltyInfoDTO updateAccountLoyaltyInfo(AccountLoyaltyInfoDTO accountLoyaltyInfo);

    /**
     * 根据管理账户号查询账户权益信息
     *
     * @param accountManagementId
     * @return
     */
    AccountLoyaltyInfoDTO queryAccountLoyaltyInfo(String accountManagementId);
}
