package com.anytech.anytxn.accounting.batch.job.journal.task;

import com.anytech.anytxn.accounting.batch.job.journal.Oauth1;
import com.anytech.anytxn.accounting.batch.job.journal.OracleSuiteApi;
import com.anytech.anytxn.accounting.batch.job.journal.dto.JournalEntry;
import com.anytech.anytxn.accounting.batch.job.journal.dto.JournalEntryLineItems;
import com.anytech.anytxn.accounting.batch.job.journal.dto.Subsidiary;
import com.anytech.anytxn.common.core.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.common.core.utils.DateHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSum;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.accounting.mapper.ParmAccountantDictMappingMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.ParamAccountantDictMapping;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: sukang
 * @Date: 2022/12/1 16:12
 */
@Slf4j
public abstract class AbstractJournalTasklet implements Tasklet {

    private ParmOrganizationInfo parmOrganizationInfo;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    abstract RepeatStatus doProcess(StepContribution contribution, ChunkContext chunkContext) throws Exception;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        return doProcess(contribution, chunkContext);
    }

    protected String getToDay(ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper,
                              String accruedThruDay) {

        if (parmOrganizationInfo != null) {
            return DateHelper.formatYmd(parmOrganizationInfo.getToday());
        }

        if (StringUtils.isNotBlank(accruedThruDay)) {
            parmOrganizationInfo = new ParmOrganizationInfo();
            parmOrganizationInfo.setAccruedThruDay(LocalDate.of(
                    Integer.parseInt(accruedThruDay.substring(0, 4)),
                    Integer.parseInt(accruedThruDay.substring(4, 6)),
                    Integer.parseInt(accruedThruDay.substring(6, 8))
            ));
            parmOrganizationInfo.setToday(parmOrganizationInfo.getAccruedThruDay());
            return accruedThruDay;
        }

        parmOrganizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(OrgNumberUtils.getOrg());

        return DateHelper.formatYmd(parmOrganizationInfo.getToday());

    }

    protected JournalEntry buildJournalEntry(List<SubjectRecordSum> subjectRecordSums,
                                             ParmAccountantDictMappingMapper parmAccountantDictMappingMapper) {
        //组装JournalEntry
        JournalEntry.JournalEntryLine journalEntryLine = new JournalEntry.JournalEntryLine();


        List<JournalEntryLineItems> journalEntryLineItems = subjectRecordSums.stream()
                .map(e -> getJournalEntryLine(e, parmAccountantDictMappingMapper))
                .collect(Collectors.toList());
        journalEntryLine.setItems(journalEntryLineItems);
        return getJournalEntry(journalEntryLine, parmAccountantDictMappingMapper);
    }

    protected JournalEntry getJournalEntry(JournalEntry.JournalEntryLine journalEntryLine,
                                           ParmAccountantDictMappingMapper parmAccountantDictMappingMapper) {

        JournalEntry journalEntry = new JournalEntry();
        journalEntry.setApproved(true);

        journalEntry.setExternalId(sequenceIdGen.generateId(TenantUtils.getTenantId()));


        ParamAccountantDictMapping subsidiaryMapping = getSubsidiary("2", parmAccountantDictMappingMapper);

        Subsidiary subsidiary = new Subsidiary();
        subsidiary.setId(subsidiaryMapping.getMappingValue());

        Subsidiary.Currency currency = new Subsidiary.Currency();

        ParamAccountantDictMapping dictMapping = getCurrency("702", parmAccountantDictMappingMapper);

        currency.setId(dictMapping.getMappingValue());
        currency.setExchangeRate("1");
        currency.setName(dictMapping.getMappingValueDesc());
        subsidiary.setCurrency(currency);

        subsidiary.setCurrency(currency);

        journalEntry.setSubsidiary(subsidiary);
        journalEntry.setTranDate(DateHelper.format2Ymd(this.parmOrganizationInfo.getToday()));
        journalEntry.setMemo(journalEntry.getTranDate());

        journalEntry.setLine(journalEntryLine);
        return journalEntry;
    }

    private ParamAccountantDictMapping getSubsidiary(String subsidiary,
                                                     ParmAccountantDictMappingMapper parmAccountantDictMappingMapper) {

        return parmAccountantDictMappingMapper
                .selectByMappingTypeAndCodeValue("SUBSIDIARY", subsidiary);
    }

    private ParamAccountantDictMapping getCurrency(String currency,
                                                   ParmAccountantDictMappingMapper parmAccountantDictMappingMapper) {

        return parmAccountantDictMappingMapper
                .selectByMappingTypeAndCodeValue("CURRENCY", currency);
    }

    protected JournalEntryLineItems getJournalEntryLine(SubjectRecordSum e,
                                                        ParmAccountantDictMappingMapper parmAccountantDictMappingMapper) {

        JournalEntryLineItems entryLineItems = new JournalEntryLineItems();


        entryLineItems.setAccount(getAccountId(e.getSubjectCodeId(), parmAccountantDictMappingMapper));


        entryLineItems.setMemo(e.getSubjectDesc());

        if (Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), e.getDebitCreditSide())) {
            entryLineItems.setCredit(e.getTotalAmount());
        } else {
            entryLineItems.setDebit(e.getTotalAmount());
        }

        if (StringUtils.isNotBlank(e.getModuleFlag())) {
            entryLineItems.setCsegBusiness(getBusiness(e.getModuleFlag(), parmAccountantDictMappingMapper));
        }

        if (StringUtils.isNotBlank(e.getAccountProduct())) {
            entryLineItems.setCsegProductType(getProductType(e.getAccountProduct(), parmAccountantDictMappingMapper));
        }

        if (StringUtils.isNotBlank(e.getCrdOrganization())) {
            entryLineItems.setCsegScheme(getScheme(e.getCrdOrganization(), parmAccountantDictMappingMapper));
        }

        return entryLineItems;
    }


    private Integer getAccountId(String subjectCodeId, ParmAccountantDictMappingMapper parmAccountantDictMappingMapper) {
        try {
            ParamAccountantDictMapping paramAccountantDictMapping = parmAccountantDictMappingMapper
                    .selectByMappingTypeAndCodeValue("ACCOUNT", subjectCodeId);

            return Integer.parseInt(paramAccountantDictMapping.getMappingValue());
        } catch (Exception e) {
            log.error("subjectCodeId is {}", subjectCodeId);
            throw e;
        }
    }

    private Integer getProductType(String productType,
                                   ParmAccountantDictMappingMapper parmAccountantDictMappingMapper) {

        try {
            ParamAccountantDictMapping paramAccountantDictMapping = parmAccountantDictMappingMapper
                    .selectByMappingTypeAndMappingValue("ACCOUT_PRODUCT_TYPE", productType);

            return Integer.parseInt(paramAccountantDictMapping.getMappingValueDesc());

        } catch (Exception e) {
            log.error("ACCOUT_PRODUCT_TYPE {} ", productType);
            throw e;
        }
    }

    private Integer getBusiness(String business,
                                ParmAccountantDictMappingMapper parmAccountantDictMappingMapper) {

        ParamAccountantDictMapping paramAccountantDictMapping = parmAccountantDictMappingMapper
                .selectByMappingTypeAndCodeValue("BUSINESS", business);

        return Integer.parseInt(paramAccountantDictMapping.getMappingValue());
    }

    private Integer getScheme(String scheme,
                              ParmAccountantDictMappingMapper parmAccountantDictMappingMapper) {

        ParamAccountantDictMapping paramAccountantDictMapping = parmAccountantDictMappingMapper
                .selectByMappingTypeAndCodeValue("SCHEME", scheme);

        return Integer.parseInt(paramAccountantDictMapping.getMappingValue());
    }

    protected Boolean doPostToOralceApi(OracleSuiteApi oracleSuiteApi,
                                        JournalEntry journalEntry) throws Exception {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        boolean restPost = oracleSuiteApi.doRestPost("record/v1/journalEntry", journalEntry, new Oauth1());
        stopWatch.stop();

        log.info("JournalEntry consume total time is {}s", stopWatch.getTotalTimeMillis() / 1000);

        return restPost;
    }
}