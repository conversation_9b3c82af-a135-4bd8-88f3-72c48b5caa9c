package com.anytech.anytxn.accounting.batch.job.report.step.cardsettlement.dto.rate;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 根据分期产品ID+币种维度
 * <AUTHOR>
 * @Date 2020/9/11 11:46
 */
@Getter
@Setter
@ToString
public class RateCardSettlementSub {
    private List<RateCardSettlementDetail> detailList;
    /**小计**/
    //渠道
    private String channel;
    //清算币种
    private String settlementCurrencyCode;
    //合计笔数
    private Integer subCount;
    //合计总额
    private BigDecimal amountTotal;
    private boolean end = false;
}
