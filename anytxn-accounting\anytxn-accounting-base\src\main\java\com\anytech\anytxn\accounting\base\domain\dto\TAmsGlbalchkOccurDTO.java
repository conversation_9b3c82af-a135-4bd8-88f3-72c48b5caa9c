package com.anytech.anytxn.accounting.base.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.anytech.anytxn.common.core.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * null
 * 表:ACCOUNTANT_GLBALCHK_OCCUR
 * <AUTHOR>
 * @date 2020-03-04
 */
public class TAmsGlbalchkOccurDTO extends BaseEntity {
    /**
     * 技术主键
     * 表字段:ID
     */
    private String id;
    /**
     * 分行号
     * 表字段:BRANCHID
     */
    private String branchid;

    /**
     * 账户管理信息id
     * 表字段:ACCOUNT_MANAGEMENT_ID
     */
    private String accountManagementId;

    /**
     * 币种
     * 表字段:CURR_CODE
     */
    private String currCode;

    /**
     * 科目号
     * 表字段:GL_ACCT
     */
    private String glAcct;

    /**
     * 总账余额
     * 表字段:GL_BALANCE
     */
    private BigDecimal glBalance;

    /**
     * 分户余额
     * 表字段:AC_BALANCE
     */
    private BigDecimal acBalance;

    /**
     * 总分差额
     * 表字段:GL_GAP_BAL
     */
    private BigDecimal glGapBal;

    /**
     * 创建日期
     * 表字段:CREATE_TIME
     */
    private LocalDateTime createTime;

    /**
     * 更新日期
     * 表字段:UPDATE_TIME
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户
     * 表字段:UPDATE_BY
     */
    private String updateBy;

    /**
     * 版本号
     * 表字段:VERSION_NUMBER
     */
    private Long versionNumber;

    /**
     * 处理日期
     * 表字段:PROC_DATE
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate procDate;

    private String glAcctName;

    public String getGlAcctName() {
        return glAcctName;
    }

    public void setGlAcctName(String glAcctName) {
        this.glAcctName = glAcctName;
    }

    /**
     * 获取技术主键
     * @return id Long
     */
    public String getId() {
        return id;
    }

    /**
     * 设置技术主键
     * @param id 技术主键
     */
    public void setId(String id) {
        this.id = id;
    }
    /**
     * 获取分行号
     * @return branchid String
     */
    public String getBranchid() {
        return branchid;
    }

    /**
     * 设置分行号
     * @param branchid 分行号
     */
    public void setBranchid(String branchid) {
        this.branchid = branchid == null ? null : branchid.trim();
    }

    /**
     * 获取账户管理信息id
     * @return accountManagementId String
     */
    public String getAccountManagementId() {
        return accountManagementId;
    }

    /**
     * 设置账户管理信息id
     * @param accountManagementId 账户管理信息id
     */
    public void setAccountManagementId(String accountManagementId) {
        this.accountManagementId = accountManagementId == null ? null : accountManagementId.trim();
    }

    /**
     * 获取币种
     * @return currCode String
     */
    public String getCurrCode() {
        return currCode;
    }

    /**
     * 设置币种
     * @param currCode 币种
     */
    public void setCurrCode(String currCode) {
        this.currCode = currCode == null ? null : currCode.trim();
    }

    /**
     * 获取科目号
     * @return glAcct String
     */
    public String getGlAcct() {
        return glAcct;
    }

    /**
     * 设置科目号
     * @param glAcct 科目号
     */
    public void setGlAcct(String glAcct) {
        this.glAcct = glAcct == null ? null : glAcct.trim();
    }

    /**
     * 获取总账余额
     * @return glBalance BigDecimal
     */
    public BigDecimal getGlBalance() {
        return glBalance;
    }

    /**
     * 设置总账余额
     * @param glBalance 总账余额
     */
    public void setGlBalance(BigDecimal glBalance) {
        this.glBalance = glBalance;
    }

    /**
     * 获取分户余额
     * @return acBalance BigDecimal
     */
    public BigDecimal getAcBalance() {
        return acBalance;
    }

    /**
     * 设置分户余额
     * @param acBalance 分户余额
     */
    public void setAcBalance(BigDecimal acBalance) {
        this.acBalance = acBalance;
    }

    /**
     * 获取总分差额
     * @return glGapBal BigDecimal
     */
    public BigDecimal getGlGapBal() {
        return glGapBal;
    }

    /**
     * 设置总分差额
     * @param glGapBal 总分差额
     */
    public void setGlGapBal(BigDecimal glGapBal) {
        this.glGapBal = glGapBal;
    }

    /**
     * 获取创建日期
     * @return createTime LocalDateTime
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建日期
     * @param createTime 创建日期
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新日期
     * @return updateTime LocalDateTime
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新日期
     * @param updateTime 更新日期
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新用户
     * @return updateBy String
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新用户
     * @param updateBy 更新用户
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    /**
     * 获取版本号
     * @return versionNumber Long
     */
    public Long getVersionNumber() {
        return versionNumber;
    }

    /**
     * 设置版本号
     * @param versionNumber 版本号
     */
    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }

    /**
     * 获取处理日期
     * @return procDate LocalDate
     */
    public LocalDate getProcDate() {
        return procDate;
    }

    /**
     * 设置处理日期
     * @param procDate 处理日期
     */
    public void setProcDate(LocalDate procDate) {
        this.procDate = procDate;
    }
}
