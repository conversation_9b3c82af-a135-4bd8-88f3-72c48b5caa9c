package com.anytech.anytxn.accounting.batch.job.report.step.installdetail.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2020/9/14
 */
@Getter
@Setter
public class InstallDetail {

    /**
     * 分期产品id
     */
    private String installType;

    /**
     * 分期产品号
     */
    private String installProductId;

    /**
     * 分期产品面熟
     */
    private String description;

    /**
     * 币种
     */
    private String currCode;

    /**
     * 交易码
     */
    private String txnCodeOrig;

    /**
     * 入账日期
     */
    private LocalDate postDate;

    /**
     * 科目号
     */
    private String glAcct;

    /**
     * 科目名称
     */
    private String glAcctName;

    /**
     * 借贷方向
     */
    private String drcr;

    /**
     * 发生额
     */
    private BigDecimal glAmount;
}
