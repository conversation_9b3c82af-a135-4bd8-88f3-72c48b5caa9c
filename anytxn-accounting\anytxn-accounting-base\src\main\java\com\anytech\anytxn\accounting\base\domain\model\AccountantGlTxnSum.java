package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.anytxn.common.core.base.BaseEntity;
import com.anytech.batch.file.SeparatedType;
import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TransferFileObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@TransferFileObject(separatedType = SeparatedType.SEPARATOR, lineTailSeparator = false,
        encoding = "UTF-8", description = "记账交易汇总文件",postfix = ".csv",separator = ",")
public class AccountantGlTxnSum extends BaseEntity implements Comparable<AccountantGlTxnSum>, Serializable {
    /**
     * CP_FINEXT.CB_EXT_REF_NO 16 日期
     */
    @FileField(order = 1, length = 50)
    private String dateRange;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 科目号
     */
    @FileField(order = 2, length = 50)
    private String subjectCodeId;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 120 科目描述
     */
    @FileField(order = 3, length = 50)
    private String subjectDesc;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 交易来源
     */
    @FileField(order = 4, length = 50)
    private String source;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 10 交易码
     */
    @FileField(order = 5, length = 50)
    private String txnCode;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 50 交易描述
     */
    @FileField(order = 6, length = 50)
    private String txnDescription;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 2 借贷记标志
     */
    @FileField(order = 7, length = 50)
    private String debitCreditSide;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 20 汇总金额
     */
    @FileField(order = 8, length = 50)
    private BigDecimal totalAmt;

    /**
     * CP_FINEXT.CB_EXT_REF_NO 3 币种
     */
    @FileField(order = 9, length = 50)
    private String currency;

    @Override
    public int compareTo(AccountantGlTxnSum o) {
        return 0;
    }
}
