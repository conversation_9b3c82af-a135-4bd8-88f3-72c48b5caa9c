package com.anytech.anytxn.account.base.domain.bo;



import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.common.core.base.BaseEntity;
import com.anytech.anytxn.business.dao.transaction.model.Posted;

import java.util.List;

/**
 * 账单批处理需要更新的数据
 *
 * <AUTHOR>
 * @date 2018-09-22
 */
public class StatementBatchBO extends BaseEntity {
    private final AccountStatementInfo accountStatementInfo;
    private final List<AccountStatisticsInfo> statisticsAccounts;
    private final AccountManagementInfoDTO managementAccount;
    private final List<AccountBalanceInfo> balanceAccounts;
    private final List<Posted> posteds;


    private StatementBatchBO(AccountStatementInfo accountStatementInfo,
                             List<AccountStatisticsInfo> statisticsAccounts,
                             AccountManagementInfoDTO managementAccount, List<Posted> posteds,
                             List<AccountBalanceInfo> balanceAccounts) {
        this.accountStatementInfo = accountStatementInfo;
        this.statisticsAccounts = statisticsAccounts;
        this.managementAccount = managementAccount;
        this.posteds = posteds;
        this.balanceAccounts =balanceAccounts;
    }

    public static StatementBatchBO of(AccountStatementInfo accountStatementInfo, List<AccountStatisticsInfo> statisticsAccounts,
                                      AccountManagementInfoDTO managementAccount, List<Posted> posteds,
                                      List<AccountBalanceInfo> balanceAccounts) {
        return new StatementBatchBO(accountStatementInfo, statisticsAccounts,
                managementAccount, posteds,balanceAccounts);
    }

    public AccountStatementInfo getAccountStatementInfo() {
        return accountStatementInfo;
    }

    public List<AccountStatisticsInfo> getStatisticsAccounts() {
        return statisticsAccounts;
    }

    public AccountManagementInfoDTO getManagementAccount() {
        return managementAccount;
    }

    public List<AccountBalanceInfo> getBalanceAccounts() {
        return balanceAccounts;
    }

    public List<Posted> getPosteds(){
        return posteds;
    }

    @Override
    public String toString() {
        return "StatementBatchBO{posteds=" + posteds + "}";
    }
}
