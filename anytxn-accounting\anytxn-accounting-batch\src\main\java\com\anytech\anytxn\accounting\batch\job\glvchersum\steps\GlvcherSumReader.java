package com.anytech.anytxn.accounting.batch.job.glvchersum.steps;

import com.anytech.anytxn.accounting.base.enums.AccountRepDetailEnum;
import com.anytech.anytxn.accounting.base.exception.AnyTxnAccountantException;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public class GlvcherSumReader extends JdbcPagingItemReader<AccountantGlvcher> {

    public GlvcherSumReader(DataSource dataSource, OrganizationInfoResDTO organizationInfo, String fromId, String endId) {
        super();
        this.setRowMapper(new BeanPropertyRowMapper<>(AccountantGlvcher.class));
        this.setQueryProvider(oraclePagingQueryProvider(dataSource,organizationInfo,fromId,endId));
    }

    private PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource,OrganizationInfoResDTO organizationInfo, String fromId, String endId) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();
        providerFactoryBean.setSelectClause(
                "ID, ORGANIZATION_NUMBER, BRANCHID, ACCOUNT_MANAGEMENT_ID, ACCT_LOGO, GLOBAL_FLOW_NO, "+
                "MODULE_FLAG, VP_TXN_CODE, CURR_CODE, GL_ACCT,GL_CNT, DRCR, GL_AMOUNT, POSTING_DATE, "+
                "PROCESS_TYPE, ORDER_ID, ASSET_NO, ABS_STATUS, TXN_CODE_ORIG, CREATE_TIME, UPDATE_TIME, "+
                "UPDATE_BY, VERSION_NUMBER, CHANNEL_ID, SUBCHANNEL_ID, FUND_ID, PLATFORM_ID, "+
                "MERCHANT_NUMBER, FIVE_TYPE_INDICATOR, ABS_TYPE"
        );
        //providerFactoryBean.setGroupClause("ORGANIZATION_NUMBER,BRANCHID,CURR_CODE,GL_ACCT,DRCR,FUND_ID,POSTING_DATE");
        providerFactoryBean.setFromClause("ACCOUNTANT_GLVCHER");
        String orgConditionStr = " ORGANIZATION_NUMBER = '"+ OrgNumberUtils.getOrg()+"'";

        String whereSql = orgConditionStr +
                " and POSTING_DATE = '"+ organizationInfo.getToday()+"'" +
                " and PROCESS_TYPE = '0'" ;

        if (fromId != null && endId != null) {
            whereSql = whereSql + " and PARTITION_KEY >= :fromId and PARTITION_KEY <= :endId ";

        }
        providerFactoryBean.setWhereClause(whereSql);
        providerFactoryBean.setDataSource(dataSource);
        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("ID", Order.ASCENDING);
        providerFactoryBean.setSortKeys(sortKey);
        try {
            return  providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("PagingQueryProvider exception",e);
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.D_DATABASE_FAULT, AccountRepDetailEnum.BATCH_PAGE_QUERY_ERROR);
        }
    }
}
