package com.anytech.anytxn.accounting.batch.job.report.step.installdetail;

import com.anytech.anytxn.accounting.base.enums.TimeTypeEnum;
import com.anytech.anytxn.accounting.base.utils.TimeUtils;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallProductInfo;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoSelfMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.anytech.anytxn.accounting.batch.config.BaseConfigurer;
import com.anytech.anytxn.accounting.batch.config.InstallDetailReportConfigurer;
import com.anytech.anytxn.accounting.batch.job.report.step.AbstractReportListTasklet;
import com.anytech.anytxn.accounting.batch.job.report.step.installdetail.dto.InstallDetailReport;
import com.anytech.anytxn.accounting.batch.job.report.step.installdetail.model.InstallDetail;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlacgn;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分期收入明细（日/月/季/年）
 * <AUTHOR>
 * @date 2020/9/14
 */
public class InstallDetailReportTasklet extends AbstractReportListTasklet {

    private TimeTypeEnum timeTypeEnum;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private TPmsGlacgnSelfMapper tPmsGlacgnSelfMapper;
    @Autowired
    private AccountantGlvcherSelfMapper accountantGlvcherSelfMapper;
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private InstallProductInfoSelfMapper installProductInfoSelfMapper;

    public InstallDetailReportTasklet(BaseConfigurer baseConfigurer, TimeTypeEnum timeTypeEnum) {
        super(baseConfigurer);
        this.timeTypeEnum = timeTypeEnum;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        if (reportConfigurer.isAbleReport()) {
            List<AccountantGlvcher> glvcherList = loadGlvcherModels();

            List<InstallDetail> data = modelToReport(glvcherList);
            out(data);
        }

        return RepeatStatus.FINISHED;
    }

    /**
     * 当前日期资产/负债传票（已处理）
     * @return
     */
    private List<AccountantGlvcher> loadGlvcherModels(){
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        List<AccountantGlvcher> result = null;
        switch (timeTypeEnum) {
            case D:
//                organizationInfo.setToday(LocalDate.of(2020,6,3));
                result = accountantGlvcherSelfMapper.selectByPostDateAndOrderAndR2(organizationInfo.getOrganizationNumber(), organizationInfo.getToday());
                break;
            case M:
//                organizationInfo.setToday(LocalDate.of(2020,6,30));
                if (TimeUtils.isEndMonth(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.mothRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndOrderAndR2(organizationInfo.getOrganizationNumber(),mothRand.getKey(), mothRand.getValue());
                }
                break;
            case Q:
//                organizationInfo.setToday(LocalDate.of(2020,6,30));
                if (TimeUtils.isEndQuarter(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> mothRand = TimeUtils.quarterRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndOrderAndR2(organizationInfo.getOrganizationNumber(),mothRand.getKey(), mothRand.getValue());
                }
                break;
            case Y:
//                organizationInfo.setToday(LocalDate.of(2020,12,31));
                if (TimeUtils.isEndYear(organizationInfo.getToday())) {
                    Pair<LocalDate, LocalDate> yearRand = TimeUtils.yearRand(organizationInfo.getToday());
                    result = accountantGlvcherSelfMapper.selectByPostDateRandAndOrderAndR2(organizationInfo.getOrganizationNumber(),yearRand.getKey(), yearRand.getValue());
                }
                break;
            default:
                break;
        }

        if (CollectionUtils.isNotEmpty(result)) {
            // 损益类
            List<TPmsGlacgn> pmsGlacgns = tPmsGlacgnSelfMapper.findByGlClass(Lists.newArrayList("PROF"),OrgNumberUtils.getOrg());
            Set<String> glAcct = pmsGlacgns.stream().map(TPmsGlacgn::getGlAcct).collect(Collectors.toSet());

            result = result.stream().filter(x-> glAcct.contains(x.getGlAcct())).collect(Collectors.toList());
        }

        return result;
    }

    /**
     * 业务数据转报表数据
     * @return
     */
    private List<InstallDetail> modelToReport(List<AccountantGlvcher> glvcherList){
        List<InstallDetail> result = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(glvcherList)) {
            Map<String, InstallOrder> orderMap = Maps.newHashMap();
            // 先直接汇总
            for (AccountantGlvcher tAmsGlvcher : glvcherList) {
                InstallDetail detail = new InstallDetail();

                // 分期类型
                InstallOrder installOrder = orderMap.containsKey(tAmsGlvcher.getOrderId()) ? orderMap.get(tAmsGlvcher.getOrderId()) : installOrderMapper.selectByPrimaryKey(tAmsGlvcher.getOrderId());
                orderMap.put(installOrder.getOrderId(), installOrder);
                detail.setInstallType(installOrder.getType());
                // 分期产品
                detail.setInstallProductId(installOrder.getProductCode());
                // 分期产品描述
                InstallProductInfo productInfo = installProductInfoSelfMapper.selectByIndex(installOrder.getOrganizationNumber(), installOrder.getProductCode());
                detail.setDescription(productInfo.getProductDesc());
                // 币种
                detail.setCurrCode(tAmsGlvcher.getCurrCode());
                // 交易码
                detail.setTxnCodeOrig(tAmsGlvcher.getTxnCodeOrig());
                // 入账日期
                detail.setPostDate(tAmsGlvcher.getPostingDate());
                // 科目号
                detail.setGlAcct(tAmsGlvcher.getGlAcct());
                // 科目名称
                TPmsGlacgn pmsGlacgn = tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(tAmsGlvcher.getOrganizationNumber(), tAmsGlvcher.getBranchid(), tAmsGlvcher.getGlAcct(), tAmsGlvcher.getCurrCode());
                detail.setGlAcctName(pmsGlacgn.getGlAcctName());
                // 借贷方向
                detail.setDrcr(tAmsGlvcher.getDrcr());
                // 发生额
                detail.setGlAmount(tAmsGlvcher.getGlAmount());

                result.add(detail);
            }
            // 在基于卡产品 + 币种 + 交易码 + 科目号 + 借贷方向汇总
            if (CollectionUtils.isNotEmpty(result)) {
                List<InstallDetail> realResult = Lists.newArrayList();
                Map<String, List<InstallDetail>> group = result.stream().collect(Collectors.groupingBy(x -> x.getInstallProductId() + x.getCurrCode() + x.getTxnCodeOrig() + x.getGlAcct() + x.getDrcr()));
                for (Map.Entry<String, List<InstallDetail>> entry : group.entrySet()) {
                    List<InstallDetail> value = entry.getValue();
                    InstallDetail temple = value.get(0);

                    temple.setGlAmount(value.stream().map(InstallDetail::getGlAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                    realResult.add(temple);
                }

                result = realResult;
            }
        }

        return result;
    }

    /**
     * 输出报表
     * @param data
     */
    private void out(List<InstallDetail> data) throws FileNotFoundException {
        if (CollectionUtils.isNotEmpty(data)) {
            InstallDetailReportConfigurer reportConfigurer0 = (InstallDetailReportConfigurer)reportConfigurer;
            Map<String, Object> params = new HashMap<>();
            reportConfigurer0.putParam(params, timeTypeEnum);

            outReport(Lists.newArrayList(InstallDetailReport.resetReportData(data, timeTypeEnum)), reportConfigurer0.outFile(timeTypeEnum), reportConfigurer0.outFileName(timeTypeEnum), reportConfigurer0.templeFile(timeTypeEnum), params);
        }
    }
}
