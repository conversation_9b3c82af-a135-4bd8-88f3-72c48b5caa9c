package com.anytech.anytxn.accounting.batch.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 分期余额
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.report.install-balance")
public class InstallBalanceReportConfigurer extends BaseConfigurer{
    /**
     * 设置参数
     * @param param
     */
    @Override
    public void putParam(Map<String, Object> param){
        param.put("reportNumber", "R039");
        param.put("reportName", "每日分期余额统计报表");
    }
}
