package com.anytech.anytxn.accounting.batch.job.glamsfromupi.config;

import com.anytech.anytxn.accounting.batch.job.glamsfromupi.steps.C602DZToLogTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class UpiGlamsConfig {

    @Autowired
    private JobBuilderFactory jobBuilderFactory;

    @Autowired
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private C602DZToLogTable c602DZToLogTable;

    @Bean
    public Job c602DZToLogTableJob(){
        return jobBuilderFactory.get("c602DZToLogTableJob")
                .start(c602DZToLogTableStep())
                .build();
    }

    @Bean
    public Step c602DZToLogTableStep(){
        return stepBuilderFactory.get("c602DZToLogTableStep")
                .tasklet(c602DZToLogTable)
                .build();
    }


}
