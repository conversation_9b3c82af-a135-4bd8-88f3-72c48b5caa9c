package com.anytech.anytxn.account.job.newautopay.step;

import com.anytech.anytxn.account.base.domain.bo.GiroAutoPayBO;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogService;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 自扣写autoPaymentLog和autoPaymentHistory
 *
 * <AUTHOR>
 * @date 2021/11/10
 */
public class AutoPaymentWriter implements ItemWriter<GiroAutoPayBO> {

    @Resource
    private IAutoPaymentLogService autoPaymentLogService;

    @Override
    public void write(Chunk<? extends GiroAutoPayBO> chunk) throws Exception {
        List<? extends GiroAutoPayBO> items = chunk.getItems();
        autoPaymentLogService.newAutoPayBatchSave((List<GiroAutoPayBO>) items);
    }
}
