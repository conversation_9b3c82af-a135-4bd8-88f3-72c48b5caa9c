package com.anytech.anytxn.account.job.newautopay.config;

import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.service.AutoPaymentLogServiceImpl;
import com.anytech.anytxn.business.dao.account.mapper.AutoPaymentLogMapper;
import com.anytech.anytxn.business.dao.account.mapper.AutoPaymentLogSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AutoPaymentLog;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.domain.dto.dbs.DbsInterBankGiroFileDTO;
import com.anytech.anytxn.transaction.base.domain.dto.dbs.DbsInterBankGiroFileHeaderDTO;
import com.anytech.anytxn.transaction.base.domain.dto.dbs.DbsInterBankGiroFileTailDTO;
import com.anytech.batch.job.file.common.FileHeader;
import com.anytech.batch.job.file.common.FileTail;
import com.anytech.batch.job.file.handler.DataLineHandler;
import com.anytech.batch.job.file.handler.TextLineHandler;
import com.anytech.batch.job.file.write.FileBody;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJob;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJobConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自扣还款文件写出处理
 *
 * <AUTHOR>
 * @date 2021-11-10
 */
@Configuration
@Slf4j
public class NewAutoPaymentFileConfig extends SimpleWriteFileBatchJobConfig<AutoPaymentLog, DbsInterBankGiroFileDTO, Long> {
    public NewAutoPaymentFileConfig(JobBuilderFactory jobs, StepBuilderFactory steps, SqlSessionFactory sqlSessionFactory) {
        super(jobs, steps, sqlSessionFactory);
    }

    @Resource(name = "autoPaymentOutFilePathConfig")
    private AnytxnFilePathConfig pathConfigInput;
    @Resource
    private IOrganizationInfoService organizationInfoService;
    @Resource
    private AutoPaymentLogServiceImpl autoPaymentLogService;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public SimpleWriteFileBatchJob<AutoPaymentLog, DbsInterBankGiroFileDTO, Long> getBatchJob() {
        OrganizationInfoResDTO organizationInfo = getOrganizationInfo();
        LocalDate today = organizationInfo.getToday();
        String autoPayFileOutPath = pathConfigInput.getCommonPath();
        log.info("自扣还款输出文件路径:{}", autoPayFileOutPath);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyMMdd");
        String format = dateTimeFormatter.format(today);
        Map<String, Object> map = new HashMap<>(2);
        map.put("organizationNumber", organizationInfo.getOrganizationNumber());
        map.put("trnDate", today);
        SimpleWriteFileBatchJob<AutoPaymentLog, DbsInterBankGiroFileDTO, Long> batchJob = SimpleWriteFileBatchJob.of();
        batchJob.name("newAutoPayFileOut").path(autoPayFileOutPath)
                .resolveClass(DbsInterBankGiroFileDTO.class)
                .fileName("AUTOPY." + format)
                .header(new FileHeader(DbsInterBankGiroFileHeaderDTO.class, (TextLineHandler) () -> {
                    return buildDbsInterBankGiroFileHeaderDTO(organizationInfo);
                }))
                .body(FileBody
                        .<AutoPaymentLog, DbsInterBankGiroFileDTO, Long>of()
                        .queryId("com.anytech.anytxn.core.accounting.mapper.AutoPaymentLogSelfMapper.selectByMap")
                        .pageSize(100)
                        .parameterValues(map)
                        .processorHandler(autoPaymentLog ->
                                autoPaymentLogService.newAutoPayFileOutWriter(autoPaymentLog)))
                .tail(new FileTail(DbsInterBankGiroFileTailDTO.class, (DataLineHandler) data -> {
                    return buildDbsInterBankGiroFileTailDTO(data, map);
                }));

        return batchJob;
    }

    /**
     * 构建GIRO自扣登记返回结果文件的头信息
     *
     * @param organizationInfo OrganizationInfoResDTO
     * @return DbsInterBankGiroFileHeaderDTO
     */
    private DbsInterBankGiroFileHeaderDTO buildDbsInterBankGiroFileHeaderDTO(OrganizationInfoResDTO organizationInfo) {
        DbsInterBankGiroFileHeaderDTO dbsInterBankGiroFileHeaderDto = new DbsInterBankGiroFileHeaderDTO();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("ddMMyyyy");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HHMMSS");
        dbsInterBankGiroFileHeaderDto.setCreationDate(dateTimeFormatter.format(organizationInfo.getToday()));
        dbsInterBankGiroFileHeaderDto.setCreationTime(timeFormatter.format(organizationInfo.getToday().atTime(LocalTime.now())));
        dbsInterBankGiroFileHeaderDto.setSendersCompanyId("DICLSI01");
        dbsInterBankGiroFileHeaderDto.setValueDate(dateTimeFormatter.format(organizationInfo.getToday()));
        dbsInterBankGiroFileHeaderDto.setOriginatingAccountNumber("**********");
        dbsInterBankGiroFileHeaderDto.setOriginatorsName("DINERS CLUB (S) P L");
        dbsInterBankGiroFileHeaderDto.setFiller(" ");
        dbsInterBankGiroFileHeaderDto.setBatchId("00001");
        dbsInterBankGiroFileHeaderDto.setBatchReference(" ");
        dbsInterBankGiroFileHeaderDto.setIndicator("C");
        dbsInterBankGiroFileHeaderDto.setFiler2(" ");
        dbsInterBankGiroFileHeaderDto.setRecordType("01");
        return dbsInterBankGiroFileHeaderDto;
    }

    private DbsInterBankGiroFileTailDTO buildDbsInterBankGiroFileTailDTO(Map<String, Object> data, Map<String, Object> map) {
        DbsInterBankGiroFileTailDTO dbsInterBankGiroFileTailDto = new DbsInterBankGiroFileTailDTO();
        dbsInterBankGiroFileTailDto.setTotalNumberOfCreditTransactionInBatch(0L);
        dbsInterBankGiroFileTailDto.setTotalCreditAmountInCentsInBatch(0L);
        if (data.get("totalNumber") == null || StringUtils.isEmpty(String.valueOf(data.get("totalNumber")))) {
            dbsInterBankGiroFileTailDto.setTotalNumberOfDebitTransactionInBatch(0L);
        } else {
            dbsInterBankGiroFileTailDto.setTotalNumberOfDebitTransactionInBatch(Long.valueOf(String.valueOf(data.get("totalNumber"))));
        }
        if (data.get("totalAmount") == null || StringUtils.isEmpty(String.valueOf(data.get("totalAmount")))) {
            dbsInterBankGiroFileTailDto.setTotalDebitAmountInCentsInBatch(0L);
        } else {
            dbsInterBankGiroFileTailDto.setTotalDebitAmountInCentsInBatch(Long.valueOf(String.valueOf(data.get("totalAmount"))));
        }
        if (data.get("accountNumberHashTotal") == null || StringUtils.isEmpty(String.valueOf(data.get("accountNumberHashTotal")))) {
            dbsInterBankGiroFileTailDto.setAmountNumberHashTotal(0L);
        } else {
            if (String.valueOf(data.get("accountNumberHashTotal")).length() > 11){
                dbsInterBankGiroFileTailDto.setAmountNumberHashTotal(Long.valueOf(String.valueOf(data.get("accountNumberHashTotal")).substring(0,11)));
            }else {
                dbsInterBankGiroFileTailDto.setAmountNumberHashTotal(Long.valueOf(String.valueOf(data.get("accountNumberHashTotal"))));
            }
        }
        dbsInterBankGiroFileTailDto.setFiller(" ");
        dbsInterBankGiroFileTailDto.setFiller2(" ");
        dbsInterBankGiroFileTailDto.setRecordType(20);
        //更新自扣表的outFile为1-已写出
        updateAutoPaymentLogOutFile(map);
        return dbsInterBankGiroFileTailDto;
    }

    private OrganizationInfoResDTO getOrganizationInfo() {
        OrganizationInfoResDTO organizationInfo =
                organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        if (organizationInfo == null) {
            log.error("根据机构号:{}查询机构参数 数据不存在", OrgNumberUtils.getOrg());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
        }
        return organizationInfo;
    }

    /**
     * 更新自扣表的outFile为1-已写出
     *
     * @param map Map<String, Object>
     */
    private void updateAutoPaymentLogOutFile(Map<String, Object> map) {
        SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH);
        // outFile更新为1-已写出
        AutoPaymentLogMapper mapper = session.getMapper(AutoPaymentLogMapper.class);
        AutoPaymentLogSelfMapper selfMapper = session.getMapper(AutoPaymentLogSelfMapper.class);
        List<AutoPaymentLog> autoPaymentLogs = selfMapper.selectByMapNotPage(map);
        if (!CollectionUtils.isEmpty(autoPaymentLogs)) {
            for (AutoPaymentLog autoPaymentLog : autoPaymentLogs) {
                autoPaymentLog.setOutFile("1");
                mapper.updateByPrimaryKeySelective(autoPaymentLog);
                session.commit();
            }
        }
    }

}
