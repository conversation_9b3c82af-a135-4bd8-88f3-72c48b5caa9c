package com.anytech.anytxn.account.base.domain.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BtiBlockCodeUpdateReportDTO {
    /**
     * 用户身份id
     */
    private String customerId;
    /**
     * 卡号
     */
    private String cardNumber;
    /**
     * 重开标志
     */
    private String renewalFlag;
    /**
     * bti索引
     */
    private String btiIndex;
    /**
     * bti封锁码
     */
    private String btiBlockCode;
    /**
     * 产品号
     */
    private String productCode;
    /**
     * 卡到期日
     */
    private String cardExpiryDate;
    /**
     * 主副卡标志
     */
    private String relationshipIndicator;
    /**
     * 主副卡标志
     */
    private String btiAgeCode;

    @Override
    public String toString() {
        return "Customer：" + customerId + ", Bti Index：" + btiIndex + ", Bti Age Code：" + btiAgeCode + ", Bti Block Code：" + btiBlockCode + ", Card No：" + cardNumber + ", Renewal Indicator：" + renewalFlag + ", Product Code：" + productCode + ", Card Expiry Date：" + cardExpiryDate + ", Basic/Supp.Indicator：" + relationshipIndicator;
    }
}
