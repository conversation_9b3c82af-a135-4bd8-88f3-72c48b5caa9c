package com.anytech.anytxn.accounting.batch.config;

import jrx.anytxn.report.enums.ExportTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/11
 */
@Getter
@Setter
public abstract class BaseConfigurer {

    @Autowired
    protected CommonConfigurer commonConfigurer;

    /**
     * 是否开启
     */
    private boolean ableReport = true;

    /**
     * 报表模板文件路径
     */
    protected String templateFilePath;
    /**
     * 报表导出路径
     */
    protected String exportFilePath;
    /**
     * 报表文件名称
     */
    protected String exportFileName;
    /**
     * 报表文件类型
     */
    protected ExportTypeEnum exportFileType = ExportTypeEnum.PDF;
    /**
     * 模版名称
     */
    protected String templateFileName;

    /**
     * 完整模版路径
     * @return
     */
    public String getTemplateFilePath(){
        // 是否含扩展路径
        String expand = StringUtils.isEmpty(templateFilePath) ? "" : templateFilePath;
        // 根路径 + 扩展路径
        return commonConfigurer.templateFilePath + expand + File.separator + templateFileName;
    }

    /**
     * 完整输出路径
     * @return
     */
    public String getExportFilePath(){
        String expand = StringUtils.isEmpty(exportFilePath) ? "" : exportFilePath;
        // 根路径 + 扩展路径
        return commonConfigurer.exportFilePath + expand;
    }

    /**
     * 输出文件名称
     */
    public String getOutFileName(){
        return exportFileName + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 设置参数
     * @param param
     */
    public void putParam(Map<String, Object> param){

    }
}
