package com.anytech.anytxn.accounting.base.service;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgdDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;

/**
 * <AUTHOR>
public interface IGlAmsAcgdService {
/**
 * TODO
  * @param page
 * @param rows
 * @param postingDate
 * @return com.anytech.anytxn.biz.common.dto.PageResultDTO<com.anytech.anytxn.accountant.dto.TAmsGlacgdDTO>
 * <AUTHOR> @date 2020-7-28
 */
    PageResultDTO<TAmsGlacgdDTO> getGlAmsAcgdByPage(Integer page, Integer rows, String postingDate);

}
