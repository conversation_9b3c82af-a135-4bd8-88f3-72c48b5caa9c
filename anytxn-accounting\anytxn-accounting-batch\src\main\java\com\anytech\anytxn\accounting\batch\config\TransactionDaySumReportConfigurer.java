package com.anytech.anytxn.accounting.batch.config;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 每日入账汇总/会计配置
 * <AUTHOR>
 * @date 2020/9/13
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "anytxn.report.transaction-sum")
public class TransactionDaySumReportConfigurer extends BaseConfigurer{

    /**
     * 通用扩展模版文件目录
     */
    private String cmtTempleFile;

    /**
     * 通用文件输出目录
     */
    private String cmtFilePath;

    /**
     * 通用交易汇总模版文件名称
     */
    private String cmtTempleFileName;

    /**
     * 通用交易汇总传票扩展模版文件目录
     */
    private String cmgTempleFile;

    /**
     * 通用交易汇总传票文件输出目录
     */
    private String cmgFilePath;

    /**
     * 通用交易汇总传票模版文件名称
     */
    private String cmgTempleFileName;

    /**
     * 交易汇总模版文件名
     */
    private String transactionTempleFileName;
    /**
     * 交易汇总输出文件名
     */
    private String transactionFileName;
    /**
     * 交易汇总输出文件扩展路径
     */
    private String transactionFilePath;
    /**
     * 交易汇总模版文件扩展路径
     */
    private String transactionTempleFilePath;


    /**
     * 交易汇总传票模版文件名
     */
    private String glvcherTempleFileName;
    /**
     * 交易汇总传票输出文件名
     */
    private String glvcherFileName;
    /**
     * 交易汇总传票输出文件扩展路径
     */
    private String glvcherFilePath;
    /**
     * 交易汇总传票模版文件扩展路径
     */
    private String glvcherTempleFilePath;



    /**
     * 差异交易汇总模版文件名
     */
    private String transactionDiffTempleFileName;
    /**
     * 差异交易汇总输出文件名
     */
    private String transactionDiffFileName;
    /**
     * 交易汇总输出文件扩展路径
     */
    private String transactionDiffFilePath;
    /**
     * 交易汇总输出文件扩展路径
     */
    private String transactionDiffTempleFilePath;



    /**
     * 差异交易汇总传票输出文件名
     */
    private String glvcherDiffFileName;
    /**
     * 交易汇总传票输出文件扩展路径
     */
    private String glvcherDiffFilePath;
    /**
     * 差异交易汇总传票模版文件名
     */
    private String glvcherDiffTempleFileName;
    /**
     * 差异交易汇总传票模版文件扩展路径
     */
    private String glvcherDiffTempleFilePath;


    public enum Type{
        T("交易汇总"),
        G("交易汇总传票"),
        TD("时间差交易汇总"),
        GD("时间差交易汇总传票");

        private String des;

        Type(String des) {
            this.des = des;
        }
    }

    /**
     * 输出文件名称
     */
    public String outFileName(Type type){
        switch (type){
            case T: return transactionFileName  + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case G: return glvcherFileName  + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case TD: return transactionDiffFileName  + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case GD: return glvcherDiffFileName  + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            default:return "";
        }
    }

    /**
     * 输出文件
     */
    public String outFile(Type type){
        switch (type){
            case T: return getOutFile(transactionFilePath, cmtFilePath);
            case G: return getOutFile(glvcherFilePath, cmgFilePath);
            case TD: return getOutFile(transactionDiffFilePath, cmtFilePath);
            case GD: return getOutFile(glvcherDiffFilePath, cmgFilePath);
            default:return "";
        }
    }

    private String getOutFile(String filePath, String cmFilePath){
        String ex = StringUtils.isEmpty(filePath) ? cmFilePath : filePath;
        return StringUtils.isEmpty(ex) ? commonConfigurer.exportFilePath : commonConfigurer.exportFilePath + ex;
    }

    /**
     * 模版文件
     */
    public String templeFile(Type type){
        switch (type){
            case T: return getTempleFile(transactionTempleFilePath, transactionTempleFileName, cmtTempleFile, cmtTempleFileName);
            case G: return getTempleFile(glvcherTempleFilePath, glvcherTempleFileName, cmgTempleFile, cmgTempleFileName);
            case TD: return getTempleFile(transactionDiffTempleFilePath, transactionDiffTempleFileName, cmtTempleFile, cmtTempleFileName);
            case GD: return getTempleFile(glvcherDiffTempleFilePath, glvcherDiffTempleFileName, cmgTempleFile, cmgTempleFileName);
            default: return "";
        }
    }

    private String getTempleFile(String templeFile, String templeName, String cmFile, String cmFileName){
        String ex = StringUtils.isEmpty(templeFile) ? cmFile : templeFile;
        String ex2 = StringUtils.isEmpty(ex) ? commonConfigurer.templateFilePath : commonConfigurer.templateFilePath + ex;
        String nx = StringUtils.isEmpty(templeName) ? cmFileName : templeName;
        return ex2 + File.separator + nx;
    }
}
