package com.anytech.anytxn.accounting.batch.config.file.subjectSumFile;

import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.batch.core.partition.support.Partitioner;
import org.springframework.batch.item.ExecutionContext;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RecordSumInDBPartitioner implements Partitioner {

    private String partitionKey;


    @Override
    public Map<String, ExecutionContext> partition(int gridSize) {
        Map<String, ExecutionContext> result = new HashMap<>(2);
        int num = 1;
        Integer partitionKey0 = null;
        Integer partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = Integer.parseInt(partitionKey.split("-")[0]);
            partitionKey1 = Integer.parseInt(partitionKey.split("-")[1]);
        }

        if (partitionKey0 == null || partitionKey1 == null) {
            partitionKey0 = 0;
            partitionKey1 = 9999;
        }
        List<Pair<Integer, Integer>> partitionGroups = PartitionKeyUtils.partitionGroup(partitionKey0,partitionKey1,gridSize);

        for (Pair<Integer, Integer> group : partitionGroups) {
            ExecutionContext context = new ExecutionContext();
            context.put("fromId", group.getKey());
            context.put("endId", group.getValue());
            context.put("threadName", "Thread" + num);
            result.put("partition" + num, context);
            num++;
        }
        return result;
    }
}
