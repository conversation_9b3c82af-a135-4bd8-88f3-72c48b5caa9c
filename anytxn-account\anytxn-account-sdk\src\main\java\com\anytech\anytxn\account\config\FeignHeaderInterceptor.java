package com.anytech.anytxn.account.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;

import java.util.Map;

/**
 * feign请求拦截器
 * <AUTHOR>
 * @Date 2024/4/28  9:23
 * @Version 1.0
 */
public class FeignHeaderInterceptor  implements RequestInterceptor  {
  private Map<String,String> headers;

  public FeignHeaderInterceptor(Map<String, String> headers) {
    this.headers = headers;
  }

  @Override
  public void apply(RequestTemplate requestTemplate) {
    headers.forEach(requestTemplate :: header);
  }
}
