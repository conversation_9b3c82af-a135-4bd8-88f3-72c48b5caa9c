package com.anytech.anytxn.account.base.constants;

/**
 * 费用收取标识
 * @author: jarod
 * 
 * 各个工程自己定义自己的常量/枚举,如果多个工程都需要再考虑是否整合到biz-common
 *
 */
public enum FeeTakeFlag {


    TAKE("1","收取"),

    NO_TAKE("0","不收取");


    private String code;

    private String desc;


    FeeTakeFlag(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
