package com.anytech.anytxn.accounting.batch.job.accountcheckoccur.config;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkOccurDTO;
import com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step.AccountOccurCheckReader;
import com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step.AccountOccurCheckWriter;
import com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step.AccountOccurCheckProcessor;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/27
 * 分户总分核对
 */
@Configuration
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class AccountCheckOccurJobConfig {

    @Autowired
    private JobBuilderFactory jobBuilderFactory;

    @Autowired
    private StepBuilderFactory stepBuilderFactory;

    @Value("${anytxn.batch.accountCheckJob.accountBatchStep.chunk-limit:5000}")
    private Integer chunkLimit;

    @Value("${anytxn.batch.accountCheckJob.accountBatchStep.page-size:500}")
    private Integer pageSize;

    @Bean
    public Job accountCheckOccurJob(@Qualifier("accountOccurCheckStep") Step accountOccurCheckStep) {
            return jobBuilderFactory.get("accountCheckOccurJob")
                .start(accountOccurCheckStep)
                .build();
    }

    @Bean
    public Step accountOccurCheckStep(@Qualifier("accountOccurCheckReader") AccountOccurCheckReader accountOccurCheckReader,
                                      @Qualifier("accountOccurCheckWriter") AccountOccurCheckWriter accountOccurCheckWriter,
                                      @Qualifier("accountOccurCheckProcessor") AccountOccurCheckProcessor accountOccurCheckProcessor) {
        return stepBuilderFactory.get("accountOccurCheckStep").<TPmsGlacgnDTO, List<TAmsGlbalchkOccurDTO>>chunk(chunkLimit)
                .reader(accountOccurCheckReader)
                .processor(accountOccurCheckProcessor)
                .writer(accountOccurCheckWriter)
                .build();
    }

    @Bean
    @StepScope
    public AccountOccurCheckReader accountOccurCheckReader(@Qualifier("commonDataSource") DataSource dataSource) {
        AccountOccurCheckReader reader = new AccountOccurCheckReader(dataSource);
        //记录状态信息和断点
        reader.setSaveState(true);
        reader.setDataSource(dataSource);
        reader.setPageSize(pageSize);
        return reader;
    }

    @Bean
    @StepScope
    public AccountOccurCheckProcessor accountOccurCheckProcessor() {
        return new AccountOccurCheckProcessor();
    }

    @Bean
    @StepScope
    public AccountOccurCheckWriter accountOccurCheckWriter() {
        return new AccountOccurCheckWriter();
    }
}
