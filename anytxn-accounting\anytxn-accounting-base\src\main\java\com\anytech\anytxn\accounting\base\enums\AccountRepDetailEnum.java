package com.anytech.anytxn.accounting.base.enums;

import com.anytech.anytxn.common.core.constants.MessageHandle;

/**
 * <AUTHOR>
 * @date 2021/3/2
 */
public enum AccountRepDetailEnum implements MessageHandle {
    BATCH_PAGE_QUERY_ERROR("批次分页处理异常！","batch PagingQueryProvider error!"),
    DATE_FORMAT_ERROR("日期格式不正确{%s}", "date format error {%s}"),
    GLACCT_NULL("科目号不存在{%s}", "glAcct is null {%s}"),
    CURRENCY_CREDIT("含有借贷不平币种{%s}", "Currency with credit {%s}")
    ;
    private String CnMsg;
    private String EnMsg;

    AccountRepDetailEnum(String cnMsg, String enMsg) {
        CnMsg = cnMsg;
        EnMsg = enMsg;
    }

    @Override
    public String getCnMsg() {
        return CnMsg;
    }

    @Override
    public String getEnMsg() {
        return EnMsg;
    }
}
