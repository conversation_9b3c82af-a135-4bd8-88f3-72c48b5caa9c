package com.anytech.anytxn.accounting.batch.job.glamsfromupi.dto;

import lombok.Getter;
import lombok.Setter;
import org.springframework.core.annotation.Order;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class DetailC602DZ implements UPIMessage {

    private static final long serialVersionUID = -6685581197200662489L;
    /**
     * 机构角色
     */
    @Order(0)
    @Size(max=13)
    private String insRole;

    /**
     * 交易类型
     */
    @Order(1)
    @Size(max=36)
    private String transactionType;

    /**
     * 清算笔数
     */
    @Order(2)
    @Size(max=17)
    private String settlementCount;

    /**
     * 交易金额
     */
    @Order(3)
    @Size(max=24)
    private String transactionAmount;

    /**
     * 清算金额
     */
    @Order(4)
    @Size(max=21)
    private String settlementAmount;

    /**
     * 交换费
     */
    @Order(5)
    @Size(max=21)
    private String reimbursementFee;

    /**
     * 服务费
     */
    @Order(6)
    @Size(max=21)
    private String serviceFee;

    /**
     * 附加费
     */
    @Order(7)
    @Size(max=21)
    private String additionalFee;

    /**
     * 净清算金额
     */
    @Order(8)
    @Size(max=23)
    private String netSettlementAmount;

}
