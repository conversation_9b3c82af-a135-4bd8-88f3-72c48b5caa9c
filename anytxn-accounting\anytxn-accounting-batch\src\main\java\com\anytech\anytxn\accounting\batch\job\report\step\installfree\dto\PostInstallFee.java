package com.anytech.anytxn.accounting.batch.job.report.step.installfree.dto;

import com.anytech.anytxn.accounting.batch.job.report.step.installfree.model.InstallFreeReport;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 入账分期手续费报表
 * <AUTHOR>
 * @Date 2020/9/11 11:43
 */
@Getter
@Setter
public class PostInstallFee {

    private List<PostInstallFeeSub> list;
    //总数
    private Integer total;

    /**
     * 报表数据转换
     * @return
     */
    public static PostInstallFee resetReportData(List<InstallFreeReport> data){
        // 总数
        PostInstallFee installFee = new PostInstallFee();
        installFee.setTotal(data.size());

        // 分组
        List<PostInstallFeeSub> installFeeSubs = Lists.newArrayList();
        installFee.setList(installFeeSubs);

        Map<String, List<InstallFreeReport>> group = data.stream().collect(Collectors.groupingBy(x -> x.getProductCode() + x.getCurrCode()));
        int i = 0;
        for (Map.Entry<String, List<InstallFreeReport>> entry : group.entrySet()) {
            List<InstallFreeReport> value = entry.getValue();
            InstallFreeReport temple = value.get(0);

            PostInstallFeeSub feeSub = new PostInstallFeeSub();
            feeSub.setProductId(temple.getProductCode());
            feeSub.setCurrencyCode(temple.getCurrCode());
            feeSub.setSubCount(value.size());
            feeSub.setAmountTotal(value.stream().map(InstallFreeReport::getGlAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            feeSub.setEnd(++i == group.size());
            installFeeSubs.add(feeSub);

            List<PostInstallFeeDetail> details = Lists.newArrayList();
            feeSub.setDetailList(details);

            for (InstallFreeReport reportDTO : value) {
                PostInstallFeeDetail detail = new PostInstallFeeDetail();
                detail.setOrderNo(reportDTO.getOrderId());
                detail.setCardNumber(reportDTO.getCardNumber());
                detail.setProductId(reportDTO.getProductCode());
                detail.setProductDesc(reportDTO.getProductDescription());
                detail.setPostDate(reportDTO.getPostingDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                detail.setCurrencyCode(reportDTO.getCurrCode());
                detail.setTransactionCode(reportDTO.getTransactionCode());
                detail.setDesc(reportDTO.getDiscription());
                detail.setAmount(reportDTO.getGlAmount());
                detail.setCrebitSubject(reportDTO.getCAcct());
                detail.setDebitSubject(reportDTO.getDAcct());
                details.add(detail);
            }
        }

        return installFee;
    }
}
