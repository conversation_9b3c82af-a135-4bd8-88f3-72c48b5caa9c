package com.anytech.anytxn.account.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 交易工程按照partitionKey分組用
 * @author: zhangnan
 * @create: 2020-05-04
 **/
public class AccountBatchPartitionHelper {

    /**
     * batch按照partition分组用。
     * 如job.partitionKey=0-1023，gridSize=8.则：0-127，128-255，...，896-1023
     * 如job.partitionKey=2-2，gridSize=8则只生成一个2-2的结果
     * @param n：需要分的个数
     */
    public static List<AccountBatchPartitionGroup> averagePartitionAssign(int from, int to, int n) {
        int size = to - from + 1;
        List<AccountBatchPartitionGroup> partitionGroupList = new ArrayList<>();
        //(先计算出余数)
        int remainder = size % n;
        //然后是商
        int number = size / n;
        int i = from;
        while (i <= to) {
            AccountBatchPartitionGroup partitionGroup = new AccountBatchPartitionGroup();
            partitionGroup.setFrom(i);
            if (remainder > 0) {
                //多放一個
                i = i + 1;
                partitionGroup.setTo(i + number - 1);
                remainder--;
            } else {
                partitionGroup.setTo(i + number - 1);
            }
            i = i + number;
            partitionGroupList.add(partitionGroup);
        }
        return partitionGroupList;
    }
}
