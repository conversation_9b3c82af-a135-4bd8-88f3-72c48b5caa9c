package com.anytech.anytxn.accounting.base.domain.model;

import com.anytech.batch.file.annotation.FileField;
import com.anytech.batch.file.annotation.TextLineObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TextLineObject(encoding = "UTF-8")
public class AccountantGlTxnSumHeader implements Serializable {
    /**
     * 记录类型 1 H
     */
    @FileField(order = 1, length = 9, rightPad = true)
    private String postDate;

    @FileField(order = 2, length = 1, rightPad = true)
    private String dot=",";

    /**
     * 记录类型 1 H
     */
    @FileField(order = 3, length = 7, rightPad = true)
    private String accountCode;

    @FileField(order = 4, length = 1, rightPad = true)
    private String dot2=",";

    /**
     * 记录类型 1 H
     */
    @FileField(order = 5, length = 12, rightPad = true)
    private String accountDesc;

    @FileField(order = 6, length = 1, rightPad = true)
    private String dot3=",";

    /**
     * 记录类型 1 H
     */
    @FileField(order = 7, length = 6, rightPad = true)
    private String source;

    @FileField(order = 8, length = 1, rightPad = true)
    private String dot8=",";

    /**
     * 记录类型 1 H
     */
    @FileField(order = 9, length = 9, rightPad = true)
    private String tranCode;

    @FileField(order = 10, length = 1, rightPad = true)
    private String dot4=",";

    /**
     * 记录类型 1 H
     */
    @FileField(order = 11, length = 14, rightPad = true)
    private String tranCodeDesc;

    @FileField(order = 12, length = 1, rightPad = true)
    private String dot5=",";

    /**
     * 记录类型 1 H
     */
    @FileField(order = 13, length = 8, rightPad = true)
    private String DrOrCr;

    @FileField(order = 14, length = 1, rightPad = true)
    private String dot6=",";

    /**
     * 记录类型 1 H
     */
    @FileField(order = 15, length = 8, rightPad = true)
    private String postAmt;

    @FileField(order = 16, length = 1, rightPad = true)
    private String dot7=",";

    /**
     * 记录类型 1 H
     */
    @FileField(order = 17, length = 9, rightPad = true)
    private String postCurr;

}
