package com.anytech.anytxn.accounting.batch.job.report.step.installbalance.dto;

import com.anytech.anytxn.accounting.batch.job.report.step.installbalance.model.InstallBalance;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/16
 */
@Getter
@Setter
public class InstallBalanceReport {

    private List<InstallBalanceReportSub> list;
    //总数
    private Integer total;

    public static InstallBalanceReport resetReportData(List<InstallBalance> data){
        InstallBalanceReport report = new InstallBalanceReport();
        report.setTotal(data.size());

        List<InstallBalanceReportSub> subs = new ArrayList<>();
        report.setList(subs);

        Map<String, List<InstallBalance>> group = data.stream().collect(Collectors.groupingBy(x -> x.getBranchId() + x.getCurrCode() + x.getProductNumber()));
        int i = 0;
        for (Map.Entry<String, List<InstallBalance>> entry : group.entrySet()) {
            List<InstallBalance> value = entry.getValue();
            InstallBalance temple = value.get(0);

            InstallBalanceReportSub sub = new InstallBalanceReportSub();
            subs.add(sub);
            sub.setEnd(++i == group.size());
            sub.setBranchNumber(temple.getBranchId());
            sub.setProductID(temple.getProductNumber());
            sub.setCurrencyCode(temple.getCurrCode());
            sub.setSubCount(value.size());
            sub.setAmountTotal(value.stream().map(InstallBalance::getBalance).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

            List<InstallBalanceReportDetail> details = new ArrayList<>();
            sub.setDetailList(details);

            for (InstallBalance balance : value) {
                InstallBalanceReportDetail detail = new InstallBalanceReportDetail();
                details.add(detail);

                detail.setBalance(balance.getBalance());
                detail.setBranchNumber(balance.getBranchId());
                detail.setCurrencyCode(balance.getCurrCode());
                detail.setProductDetailed(balance.getDescription());
                detail.setProductID(balance.getProductNumber());
            }
        }

        return report;
    }
}
