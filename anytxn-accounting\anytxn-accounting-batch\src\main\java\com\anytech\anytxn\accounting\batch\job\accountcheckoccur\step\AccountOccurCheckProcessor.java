package com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkOccurDTO;
import com.anytech.anytxn.accounting.base.service.IAccountsOccurCheckService;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/26
 */
public class AccountOccurCheckProcessor implements ItemProcessor<TPmsGlacgnDTO, List<TAmsGlbalchkOccurDTO>> {

    @Autowired
    private IAccountsOccurCheckService accountsOccurCheckService;

    @Override
    public List<TAmsGlbalchkOccurDTO> process(TPmsGlacgnDTO item) throws Exception {
        return accountsOccurCheckService.caculateGlCheckOcur(item);
    }
}
