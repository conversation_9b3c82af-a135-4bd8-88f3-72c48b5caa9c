package com.anytech.anytxn.account.base.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccStaDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatisticsInfoDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 统计账户
 *
 * <AUTHOR>
 * @date 2018-09-29 17:28
 **/
public interface IAccountStatisticsInfoService {

    /**
     * 根据管理账户id分页查询统计账户
     *
     * @param page 页码
     * @param rows 页面容量
     * @param accountManagementId  管理账户id
     * @return 统计账户
     */
    PageResultDTO<AccountStatisticsInfoDTO> findListAccStaInfo(Integer page, Integer rows, String accountManagementId);

    /**
     * 根据管理账户id查询统计账户明细
     * @param statisticsId 统计账户id
     * @return AccountStatisticsInfoDTO
     */
    AccountStatisticsInfoDTO findStaById(String statisticsId);

    /**
     * 更新统计账户余额和交易码
     *
     * @param statisticsId 管理账户id
     * @param balance 余额
     * @param transactionTypeCode 交易码
     */
    void modifyStaBaAndCode(String statisticsId, BigDecimal balance, String transactionTypeCode);


    /**
     * 查询账户统计信息表
     *
     * @param accountManagementId 账户管理信息id
     * @param transactionTypeCode 交易类型码
     * @return AccountStatisticsInfo
     */
    AccountStatisticsInfoDTO getAccountStatisticsInfo(String accountManagementId, String transactionTypeCode);

    /**
     * 查询除了溢缴款统计账户的所有相同管理账户id的统计账户
     *
     * @param accountManagementId 管理账户id
     * @return 统计账户列表
     */
    List<AccountStatisticsInfoDTO> getNotOverRepaymentAsiByMid(String accountManagementId);


    /**
     * 查询溢缴款统计账户
     *
     * @param accountManagementId 账户管理信息id
     * @return 溢缴款统计账户
     */
    AccountStatisticsInfoDTO getOverPaymentAsi(String accountManagementId);

    /**
     * 根据传入数据创建汇总(00000)统计账户
     * @param accStaDto  传入数据
     */
    void addAccStaInfo(AccStaDTO accStaDto);

    /**
     *新增统计账户
     * @param accountStatisticsInfoDTO
     */
    void  registAccountStatisticsInfo(AccountStatisticsInfoDTO accountStatisticsInfoDTO);

    /**
     * 修改统计账户
     * @param accountStatisticsInfoDTO
     */
    void  modifyAccountStatisticsInfo(AccountStatisticsInfoDTO accountStatisticsInfoDTO);


}
