package com.anytech.anytxn.account.controller;

import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.account.base.utils.AnyTxnHttpResponseHelper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogService;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * @program:: anytxn-accounting-parent
 * @description: 微信公众号还款
 * @author: lxl
 * @create: 2021-01-05 17:15
 *
 *
 */
@RestController
@Tag(name = "微信公众号还款接口")
public class WeChatController extends BizBaseController {

	@Resource
	private IAutoPaymentLogService autoPaymentLogService;

	@Operation(summary = "产生还款记录")
	@PostMapping(path = "/account/insertAutoPaymentLog")
	public AnyTxnHttpResponse insertAutoPaymentLog(@RequestBody JSONObject jsonParam) {
		autoPaymentLogService.insertAutoPaymentLog(jsonParam.getString("accountManagementId"),jsonParam.getString("cardNumber"),
				jsonParam.getBigDecimal("originalPaymentAmount"),jsonParam.getString("trnDate"));
		return AnyTxnHttpResponseHelper.success(AccountingRepDetailEnum.WE_OF.getCnMsg());
	}
}
