package com.anytech.anytxn.accounting.batch.config.file.subjectSumFile;

import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherOriDetailInfo;
import com.anytech.anytxn.common.core.constants.PropsConstant;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.google.common.collect.ImmutableMap;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.mybatis.spring.batch.builder.MyBatisPagingItemReaderBuilder;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.partition.PartitionHandler;
import org.springframework.batch.core.partition.support.Partitioner;
import org.springframework.batch.core.partition.support.TaskExecutorPartitionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;

import java.lang.reflect.Field;

@Configuration
@Slf4j
public class RecordSumInDBConfig {

    @Resource
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;


    private Integer griSize= 1;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.recordSumInDBJob.chunkLimit:1000}")
    private Integer chunkLimit;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.recordSumInDBJob.pageSize:3000}")
    private Integer pageSize;

    @Bean
    public Job recordSumInDBJob(@Qualifier("recordSumInDBStepManager") Step recordSumInDBStepManager) {
        return jobs.get("recordSumInDBJob")
                .start(recordSumInDBStepManager).build();
    }

    @Bean
    public Step recordSumInDBStepManager(@Qualifier("recordSumPartitioner") Partitioner recordSumPartitioner,
                                               @Qualifier("recordSumPartHandler") PartitionHandler recordSumPartHandler){
        return steps.get("recordSumInDBStepManager")
                .partitioner("acctManageStep", recordSumPartitioner)
                .partitionHandler(recordSumPartHandler)
                .build();
    }

    @Bean
    public Partitioner recordSumPartitioner(){
        return new RecordSumInDBPartitioner();
    }

    @Bean
    public PartitionHandler recordSumPartHandler(@Qualifier("accountantTaskExe") TaskExecutor accountantTaskExe,
                                                  @Qualifier("recordSumInDBStep") Step recordSumInDBStep) {
        TaskExecutorPartitionHandler retVal = new TaskExecutorPartitionHandler();
        retVal.setTaskExecutor(accountantTaskExe);
        retVal.setStep(recordSumInDBStep);
        retVal.setGridSize(griSize);
        return retVal;
    }

    @Bean
    public Step recordSumInDBStep(
            @Qualifier("recordSumInDBProcessor") RecordSumInDBProcessor recordSumInDBProcessor,
            @Qualifier("recordSumInDBWriter") RecordSumInDBWriter recordSumInDBWriter,
            @Qualifier("sqlSessionFactoryBusiness") SqlSessionFactory sqlSessionFactoryBusiness) throws Exception {

        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        MyBatisPagingItemReader<AccountantGlvcherOriDetailInfo> myBatisPagingItemReader =
                new MyBatisPagingItemReaderBuilder<AccountantGlvcherOriDetailInfo>()
                .pageSize(pageSize)
                .queryId(AccountantGlvcherSelfMapper.class.getName() + ".selectSumRecordToday")
                .parameterValues(ImmutableMap.of("today", organizationInfo.getToday()))
                .sqlSessionFactory(sqlSessionFactoryBusiness)
                .build();

        Field field = myBatisPagingItemReader.getClass().getDeclaredField("sqlSessionTemplate");
        field.setAccessible(true);
        field.set(myBatisPagingItemReader,new SqlSessionTemplate(sqlSessionFactoryBusiness, ExecutorType.SIMPLE));
        myBatisPagingItemReader.setSaveState(false);

        return steps.get("acctManageMigrationStep").<AccountantGlvcherOriDetailInfo, SubjectRecordSum>chunk(chunkLimit)
                .reader(myBatisPagingItemReader)
                .processor(recordSumInDBProcessor)
                .writer(recordSumInDBWriter).build();

    }

    /*@Bean
    @StepScope
    public RecordSumInDBReader recordSumInDBReader(
            @Qualifier("businessDataSource") DataSource dataSource,
            @Value("#{stepExecutionContext['fromId']}") String fromId,
            @Value("#{stepExecutionContext['endId']}") String endId) {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        RecordSumInDBReader reader = new RecordSumInDBReader(dataSource, fromId, endId,organizationInfo);
        reader.setDataSource(dataSource);
        // 设置页码大小
        reader.setPageSize(pageSize);
        // 记录断点
        reader.setSaveState(true);
        Map<String, Object> parameters = new HashMap<>(4);
        parameters.put("fromId", fromId);
        parameters.put("endId", endId);
        reader.setParameterValues(parameters);
        return reader;
    }*/

    @Bean
    @StepScope
    public RecordSumInDBProcessor recordSumInDBProcessor() {
        return new RecordSumInDBProcessor();
    }

    @Bean
    @StepScope
    public RecordSumInDBWriter recordSumInDBWriter() {
        return new RecordSumInDBWriter();
    }
}
