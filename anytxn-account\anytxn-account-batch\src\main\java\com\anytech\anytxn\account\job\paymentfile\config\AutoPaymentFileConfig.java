package com.anytech.anytxn.account.job.paymentfile.config;

import com.anytech.anytxn.account.config.AutoPaymentFileConfigurer;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.job.paymentfile.model.AutoPaymentOut;
import com.anytech.anytxn.account.job.paymentfile.step.auto.AutoPaymentFileWriter;
import com.anytech.anytxn.account.job.paymentfile.step.auto.AutoPaymentLogReader;
import com.anytech.anytxn.business.dao.account.model.AutoPaymentLog;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.file.FlatFileItemWriter;
import org.springframework.batch.item.file.transform.ExtractorLineAggregator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.FileSystemResource;

import jakarta.annotation.Resource;
import javax.sql.DataSource;
import java.time.format.DateTimeFormatter;

/**
 * 扣款文件生成分区处理
 * <AUTHOR>
 * @date 2021/1/5
 */
@Configuration
@Slf4j
public class AutoPaymentFileConfig {

    @Autowired
    private JobBuilderFactory jobs;

    @Autowired
    private StepBuilderFactory steps;

    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Autowired
    private AutoPaymentFileConfigurer autoPaymentFileConfigurer;

    @Value("${anytxn.batch.auto-payment-file.page-size:1000}")
    private Integer pageSize;
    @Value("${anytxn.batch.auto-payment-file.chunk-limit:1000}")
    private Integer chunkLimit;
    @Value("${data-id:-1}")
    private String dataId;

    @Resource(name = "autoPaymentFilePathConfig")
    private AnytxnFilePathConfig pathConfigInput;

    public final static int[] condition = new int[]{4,4,8,20,12,3,8,19,2,18,1,4,4,2,62,20,20,480};

    /**
     * 扣款文件输出处理
     * 1. 初始化删除重名文件
     * 2. 有数据输出则构建文件、没有则不构建
     */
    @Bean
    public Job autoPaymentFileJob(@Qualifier("autoPaymentFileStep") Step autoPaymentFileStep) {
        return jobs.get("autoPaymentFileJob")
                .start(autoPaymentFileStep)
                .build();
    }

    @Bean
    public Step autoPaymentFileStep(
            @Qualifier("autoPaymentLogReader") AutoPaymentLogReader autoPaymentLogReader,
            @Qualifier("autoPaymentFileWriter") AutoPaymentFileWriter autoPaymentFileWriter) {
        return steps.get("autoPaymentFileStep").<AutoPaymentLog, AutoPaymentLog>chunk(chunkLimit)
                .reader(autoPaymentLogReader)
                .writer(autoPaymentFileWriter)
                .build();
    }


    @Bean
    @StepScope
    public AutoPaymentLogReader autoPaymentLogReader(@Qualifier("businessDataSource") DataSource dataSource) {
        AutoPaymentLogReader reader = new AutoPaymentLogReader(dataSource);
        reader.setDataSource(dataSource);
        reader.setPageSize(pageSize);
        //记录状态信息和断点
        reader.setSaveState(true);
        return reader;
    }

    public FlatFileItemWriter<AutoPaymentOut> fileWriter(){
        FlatFileItemWriter<AutoPaymentOut> fileItemWriter = new FlatFileItemWriter<>();
        // 初始化时重置文件（删除并创建新文件）
        fileItemWriter.setAppendAllowed(false);
        fileItemWriter.setSaveState(true);
        fileItemWriter.setTransactional(true);
        fileItemWriter.setEncoding("GBK");
        // 文件输出路径
        log.info("文件输出路径为: {}",joinAutoPaymentFilePath());
        String filePath = joinAutoPaymentFilePath();
        fileItemWriter.setResource(new FileSystemResource(filePath));

        fileItemWriter.setLineAggregator(new FixedLenLineAggregator<AutoPaymentOut>(condition){{
            setFieldExtractor((log)-> new Object[]{log.getBank(),log.getBranch(),log.getInpDate(),
            log.getAcct(),log.getAmount(),log.getCurr(),log.getTrnDate(),log.getCardNbr(),log.getIdType(),
            log.getId(),log.getAcctType(),log.getOrder(),log.getObank(),log.getReservd(),log.getName(),
            log.getAddAcct(),log.getBusiness(),log.getRevs2()});
        }});

        return fileItemWriter;
    }


    @Bean
    @StepScope
    public AutoPaymentFileWriter autoPaymentFileWriter() {
        return new AutoPaymentFileWriter(fileWriter());
    }

    private String getDusId(){
        if (dataId.contains(".")) {
            dataId = dataId.split("\\.")[0];
        }

        if (dataId.contains("-")){
            dataId = dataId.split("-")[1];
        }

        return dataId;
    }

    private String joinAutoPaymentFileName(ParmOrganizationInfo org){
        // 文件名称由【机构号-文件名-日期】组成
        String today = org.getToday().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return org.getOrganizationNumber() + "-" + autoPaymentFileConfigurer.getFileName() + "-" + today;
    }

    private String joinAutoPaymentFilePath(){
        String orgNum = OrgNumberUtils.getOrg();
        ParmOrganizationInfo org = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(orgNum);
        // 拼接输出文件目录：前缀/分片号/机构号/文件名
        log.info("joinAutoPaymentFilePath : {}",pathConfigInput.getShardingPath(Integer.parseInt(getDusId())) + joinAutoPaymentFileName(org));
        return pathConfigInput.getShardingPath(Integer.parseInt(getDusId())) + joinAutoPaymentFileName(org);
    }

    public class FixedLenLineAggregator<T> extends ExtractorLineAggregator<T>{

        private int[] length;

        public FixedLenLineAggregator(int[] length) {
            this.length = length;
        }

        @Override
        protected String doAggregate(Object[] fields) {
            // 格式数组与字段数组数量不同抛出异常
            if (length.length != fields.length) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.OUT_FILE);
            }

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fields.length; i++) {
                String s = String.valueOf(fields[i]);
                int i1 = length[i];
                if (s.length() > i1) {
                    sb.append(StringUtils.substring(s, 0, i1));
                } else {
                    sb.append(StringUtils.rightPad(s, i1, " "));
                }

            }

            return sb.toString();
        }
    }
}
