package com.anytech.anytxn.account.job.stamptaxsumlimit.shard.step;

import com.anytech.anytxn.account.base.service.IStampTaxSumLimitService;
import com.anytech.anytxn.account.utils.FileUtil;
import com.anytech.anytxn.business.dao.account.mapper.StampTaxSumLimitMapper;
import com.anytech.anytxn.business.dao.account.model.StampTaxSumLimit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021-06-25
 */
@Slf4j
public class ShardTasklet implements Tasklet {

    @Autowired
    private IStampTaxSumLimitService stampTaxSumLimitService;
    @Autowired
    private StampTaxSumLimitMapper stampTaxSumLimitMapper;

    private String filePath;

    public ShardTasklet(String filePath) {
        this.filePath = filePath;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {
        StampTaxSumLimit stampTaxSumLimit = stampTaxSumLimitService.shardSumProcess();
        //单分片数据汇总入库
        stampTaxSumLimitMapper.insertSelective(stampTaxSumLimit);
        //单分片数据汇总写出到文件
        log.info("单分片文件输出路径:{}", filePath);
        FileUtil.outFile(filePath, stampTaxSumLimit.toString());
        return RepeatStatus.FINISHED;
    }
}
