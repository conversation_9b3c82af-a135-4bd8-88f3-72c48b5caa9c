package com.anytech.anytxn.account.job.brandservicefee.config;

import com.anytech.anytxn.account.job.brandservicefee.step2.WriteGlamsTasklet;
import com.anytech.anytxn.common.core.config.batch.CommonDbConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 品牌服务费文件处理Job
 *
 * <AUTHOR>
 * @date 2021/8/4
 */
@Configuration
@Slf4j
public class BrandServiceFeeCommonConfig {
    @Autowired
    private JobBuilderFactory jobBuilderFactory;
    @Autowired
    private StepBuilderFactory stepBuilderFactory;

    @Bean
    public Job brandServiceFeeShardJob(@Qualifier("writeGlamsStep") Step writeGlamsStep) {
        return jobBuilderFactory.get("brandServiceFeeShardJob")
                .start(writeGlamsStep)
                .build();
    }

    @Bean
    @StepScope
    public WriteGlamsTasklet writeGlamsTasklet(@Qualifier(CommonDbConfiguration.SQLSESSIONTEMPLATE) SqlSessionTemplate sqlSessionTemplateCommon) {
        return new WriteGlamsTasklet(sqlSessionTemplateCommon);
    }

    @Bean
    public Step writeGlamsStep(@Qualifier("writeGlamsTasklet")WriteGlamsTasklet writeGlamsTasklet) {
        return stepBuilderFactory.get("writeGlamsStep")
                .tasklet(writeGlamsTasklet)
                .build();
    }
}
