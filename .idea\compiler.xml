<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for anytxn-transaction" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../maven/apache-maven-3.6.3/repository/org/springframework/boot/spring-boot-configuration-processor/3.4.2/spring-boot-configuration-processor-3.4.2.jar" />
          <entry name="$PROJECT_DIR$/../../maven/apache-maven-3.6.3/repository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
          <entry name="$PROJECT_DIR$/../../maven/apache-maven-3.6.3/repository/org/mapstruct/mapstruct-processor/1.6.3/mapstruct-processor-1.6.3.jar" />
          <entry name="$PROJECT_DIR$/../../maven/apache-maven-3.6.3/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar" />
        </processorPath>
        <module name="anytxn-notification-sdk" />
        <module name="anytxn-installment-batch" />
        <module name="anytxn-limit-sdk" />
        <module name="anytxn-account-client" />
        <module name="anytxn-central-processing-server" />
        <module name="anytxn-card-client" />
        <module name="anytxn-file-manager-server" />
        <module name="anytxn-authorization-sdk" />
        <module name="anytxn-card-base" />
        <module name="anytxn-file-manager-sdk" />
        <module name="anytxn-transaction-base" />
        <module name="anytxn-settlement-sdk" />
        <module name="anytxn-transaction-batch" />
        <module name="anytxn-account-server" />
        <module name="anytxn-card-server" />
        <module name="anytxn-customer-server" />
        <module name="anytxn-installment-base" />
        <module name="anytxn-monetary-processing-server" />
        <module name="anytxn-parameter-batch" />
        <module name="anytxn-customer-client" />
        <module name="anytxn-settlement-batch" />
        <module name="anytxn-settlement-base" />
        <module name="anytxn-mapping-server" />
        <module name="anytxn-common-sequence" />
        <module name="anytxn-monetary-processing-sdk" />
        <module name="anytxn-transaction-sdk" />
        <module name="anytxn-installment-sdk" />
        <module name="anytxn-limit-batch" />
        <module name="anytxn-accounting-server" />
        <module name="anytxn-monetary-processing-batch" />
        <module name="anytxn-central-processing-base" />
        <module name="anytxn-installment-server" />
        <module name="anytxn-accounting-base" />
        <module name="anytxn-central-processing-client" />
        <module name="anytxn-mapping-base" />
        <module name="anytxn-mapping-sdk" />
        <module name="anytxn-parameter-client" />
        <module name="anytxn-authorization-base" />
        <module name="anytxn-central-processing-sdk" />
        <module name="anytxn-business-core-client" />
        <module name="anytxn-accounting-sdk" />
        <module name="anytxn-monetary-processing-base" />
        <module name="anytxn-authorization-batch" />
        <module name="anytxn-common-security" />
        <module name="anytxn-rule-sdk" />
        <module name="anytxn-settlement-server" />
        <module name="anytxn-hsm-sdk" />
        <module name="anytxn-business-core-dao" />
        <module name="anytxn-authorization-server" />
        <module name="anytxn-customer-base" />
        <module name="anytxn-customer-sdk" />
        <module name="anytxn-mapping-batch" />
        <module name="anytxn-api-gateway-client" />
        <module name="anytxn-common-sharding" />
        <module name="anytxn-notification-server" />
        <module name="anytxn-transaction-server" />
        <module name="anytxn-parameter-base" />
        <module name="anytxn-authorization-client" />
        <module name="anytxn-limit-server" />
        <module name="anytxn-business-core-base" />
        <module name="anytxn-account-sdk" />
        <module name="anytxn-common-loadbalancer" />
        <module name="anytxn-common-rule" />
        <module name="anytxn-api-gateway-server" />
        <module name="anytxn-common-redis" />
        <module name="anytxn-parameter-sdk" />
        <module name="anytxn-common-core" />
        <module name="anytxn-central-processing-batch" />
        <module name="anytxn-account-base" />
        <module name="anytxn-card-batch" />
        <module name="anytxn-account-batch" />
        <module name="anytxn-limit-base" />
        <module name="anytxn-card-sdk" />
        <module name="anytxn-transaction-client" />
        <module name="anytxn-hsm-server" />
        <module name="anytxn-accounting-batch" />
        <module name="anytxn-business-core-sdk" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="anytxn-account-base" options="-parameters" />
      <module name="anytxn-account-batch" options="-parameters" />
      <module name="anytxn-account-client" options="-parameters" />
      <module name="anytxn-account-sdk" options="-parameters" />
      <module name="anytxn-account-server" options="-parameters" />
      <module name="anytxn-accounting-base" options="-parameters" />
      <module name="anytxn-accounting-batch" options="-parameters" />
      <module name="anytxn-accounting-sdk" options="-parameters" />
      <module name="anytxn-accounting-server" options="-parameters" />
      <module name="anytxn-api-gateway-client" options="-parameters" />
      <module name="anytxn-api-gateway-server" options="-parameters" />
      <module name="anytxn-authorization-base" options="-parameters" />
      <module name="anytxn-authorization-batch" options="-parameters" />
      <module name="anytxn-authorization-client" options="-parameters" />
      <module name="anytxn-authorization-sdk" options="-parameters" />
      <module name="anytxn-authorization-server" options="-parameters" />
      <module name="anytxn-business-core-base" options="-parameters" />
      <module name="anytxn-business-core-client" options="-parameters" />
      <module name="anytxn-business-core-dao" options="-parameters" />
      <module name="anytxn-business-core-sdk" options="-parameters" />
      <module name="anytxn-card-base" options="-parameters" />
      <module name="anytxn-card-batch" options="-parameters" />
      <module name="anytxn-card-client" options="-parameters" />
      <module name="anytxn-card-sdk" options="-parameters" />
      <module name="anytxn-card-server" options="-parameters" />
      <module name="anytxn-central-processing-base" options="-parameters" />
      <module name="anytxn-central-processing-batch" options="-parameters" />
      <module name="anytxn-central-processing-client" options="-parameters" />
      <module name="anytxn-central-processing-sdk" options="-parameters" />
      <module name="anytxn-central-processing-server" options="-parameters" />
      <module name="anytxn-common-core" options="-parameters" />
      <module name="anytxn-common-loadbalancer" options="-parameters" />
      <module name="anytxn-common-redis" options="-parameters" />
      <module name="anytxn-common-rule" options="-parameters" />
      <module name="anytxn-common-security" options="-parameters" />
      <module name="anytxn-common-sequence" options="-parameters" />
      <module name="anytxn-common-sharding" options="-parameters" />
      <module name="anytxn-customer-base" options="-parameters" />
      <module name="anytxn-customer-client" options="-parameters" />
      <module name="anytxn-customer-sdk" options="-parameters" />
      <module name="anytxn-customer-server" options="-parameters" />
      <module name="anytxn-file-manager-sdk" options="-parameters" />
      <module name="anytxn-file-manager-server" options="-parameters" />
      <module name="anytxn-hsm-sdk" options="-parameters" />
      <module name="anytxn-hsm-server" options="-parameters" />
      <module name="anytxn-installment-base" options="-parameters" />
      <module name="anytxn-installment-batch" options="-parameters" />
      <module name="anytxn-installment-sdk" options="-parameters" />
      <module name="anytxn-installment-server" options="-parameters" />
      <module name="anytxn-limit-base" options="-parameters" />
      <module name="anytxn-limit-batch" options="-parameters" />
      <module name="anytxn-limit-sdk" options="-parameters" />
      <module name="anytxn-limit-server" options="-parameters" />
      <module name="anytxn-mapping-base" options="-parameters" />
      <module name="anytxn-mapping-batch" options="-parameters" />
      <module name="anytxn-mapping-sdk" options="-parameters" />
      <module name="anytxn-mapping-server" options="-parameters" />
      <module name="anytxn-monetary-processing" options="-parameters" />
      <module name="anytxn-monetary-processing-base" options="-parameters" />
      <module name="anytxn-monetary-processing-batch" options="-parameters" />
      <module name="anytxn-monetary-processing-sdk" options="-parameters" />
      <module name="anytxn-monetary-processing-server" options="-parameters" />
      <module name="anytxn-notification-sdk" options="-parameters" />
      <module name="anytxn-notification-server" options="-parameters" />
      <module name="anytxn-parameter-base" options="-parameters" />
      <module name="anytxn-parameter-batch" options="-parameters" />
      <module name="anytxn-parameter-client" options="-parameters" />
      <module name="anytxn-parameter-sdk" options="-parameters" />
      <module name="anytxn-rule-sdk" options="-parameters" />
      <module name="anytxn-settlement-base" options="-parameters" />
      <module name="anytxn-settlement-batch" options="-parameters" />
      <module name="anytxn-settlement-sdk" options="-parameters" />
      <module name="anytxn-settlement-server" options="-parameters" />
      <module name="anytxn-transaction-base" options="-parameters" />
      <module name="anytxn-transaction-batch" options="-parameters" />
      <module name="anytxn-transaction-client" options="-parameters" />
      <module name="anytxn-transaction-sdk" options="-parameters" />
      <module name="anytxn-transaction-server" options="-parameters" />
    </option>
  </component>
</project>