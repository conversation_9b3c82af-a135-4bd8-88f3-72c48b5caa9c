package com.anytech.anytxn.accounting.base.domain.dto;

import com.anytech.anytxn.common.core.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * @description:
 * @author: ZXL
 * @create: 2019-10-11 09:35
 */
@Getter
@Setter
public class LedgerDTO extends BaseEntity {

    private TAmsGlacgdDTO amsGlacgd;
    private TAmsGlacgmDTO amsGlacgm;
    private TAmsGlacgyDTO amsGlacgy;
    private TAmsGlacgpDTO tAmsGlacgp;
    private TAmsGlvcherSumDTO amsGlvcherSum;


    @Override
    public String toString() {
        return "LedgerDTO{" +
                "amsGlacgd=" + amsGlacgd +
                ", amsGlacgm=" + amsGlacgm +
                ", amsGlacgy=" + amsGlacgy +
                ", tAmsGlacgp=" + tAmsGlacgp +
                ", amsGlvcherSum=" + amsGlvcherSum +
                '}';
    }
}