package com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep;

import com.anytech.anytxn.accounting.base.domain.bo.GenerateVoucherBO;
import com.anytech.anytxn.accounting.base.service.IGlVoucherService;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 根据交易会计流水生成传票
 * <AUTHOR>
public class AmsToVoucherProcessor implements ItemProcessor<AccountantGlams, GenerateVoucherBO> {

    private OrganizationInfoResDTO organizationInfo;
    public AmsToVoucherProcessor(OrganizationInfoResDTO organizationInfo) {
        this.organizationInfo = organizationInfo;
    }

    @Autowired
    private IGlVoucherService glVoucherService;

    @Override
    public GenerateVoucherBO process(AccountantGlams tAmsGlams) {
        // 生成会计传票
        return glVoucherService.amsToVoucher(tAmsGlams.getAccountManagementId(),this.organizationInfo);
    }
}
