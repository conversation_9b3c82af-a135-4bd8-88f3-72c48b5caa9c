package com.anytech.anytxn.account.job.newautopay.config;

import com.anytech.anytxn.account.base.domain.bo.GiroAutoPayBO;
import com.anytech.anytxn.account.job.newautopay.step.AutoPaymentProcessor;
import com.anytech.anytxn.account.job.newautopay.step.AutoPaymentReader;
import com.anytech.anytxn.account.job.newautopay.step.AutoPaymentWriter;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * 自扣处理config
 *
 * <AUTHOR>
 * @date 2021/11/10
 */
@Configuration
public class AutoPaymentBatchConfig {

    @Autowired
    private JobBuilderFactory jobs;
    @Autowired
    private StepBuilderFactory steps;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Value("${anytxn.batch.stmtProcessJob.stmtStep.chunk-limit:1000}")
    private Integer chunkLimit;
    @Value("${anytxn.batch.stmtProcessJob.stmtStep.page-size:1000}")
    private Integer pageSize;

    /**
     * 自扣Job处理
     *
     * @param autoPaymentStep Step
     * @return Job
     */
    @Bean
    public Job newAutoPaymentJob(@Qualifier("autoPaymentStep") Step autoPaymentStep) {
        return jobs.get("newAutoPaymentJob")
                .start(autoPaymentStep)
                .build();
    }

    /**
     * 自扣处理Step
     *
     * @param autoPaymentReader    AutoPaymentReader
     * @param autoPaymentProcessor AutoPaymentProcessor
     * @param autoPaymentWriter    AutoPaymentWriter
     * @return Step
     */
    @Bean
    public Step autoPaymentStep(
            @Qualifier("autoPaymentReader") AutoPaymentReader autoPaymentReader,
            @Qualifier("autoPaymentProcessor") AutoPaymentProcessor autoPaymentProcessor,
            @Qualifier("autoPaymentWriter") AutoPaymentWriter autoPaymentWriter) {
        return steps.get("autoPaymentStep")
                .<AccountManagementInfo, GiroAutoPayBO>chunk(chunkLimit)
                .reader(autoPaymentReader).processor(autoPaymentProcessor)
                .writer(autoPaymentWriter).build();
    }

    @Bean
    @StepScope
    public AutoPaymentReader autoPaymentReader(@Qualifier("businessDataSource") DataSource dataSource) {
        AutoPaymentReader reader = new AutoPaymentReader(dataSource,organizationInfoService);
        reader.setDataSource(dataSource);
        reader.setPageSize(pageSize);
        //记录状态信息和断点
        reader.setSaveState(false);
        return reader;
    }

    @Bean
    @StepScope
    public AutoPaymentProcessor autoPaymentProcessor() {
        return new AutoPaymentProcessor();
    }

    @Bean
    @StepScope
    public AutoPaymentWriter autoPaymentWriter() {
        return new AutoPaymentWriter();
    }
}
