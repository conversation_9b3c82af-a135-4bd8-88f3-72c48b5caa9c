package com.anytech.anytxn.account.job.paymentfile.step.repay;

import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.account.job.paymentfile.model.RepaymentDTO;
import com.anytech.anytxn.business.base.settlement.enums.RejectCodeEnum;
import com.anytech.anytxn.business.dao.settlement.mapper.SettleRejectLogMapper;
import com.anytech.anytxn.business.dao.settlement.model.SettleRejectLog;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemStreamException;
import org.springframework.batch.item.ItemStreamReader;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 还款文件读取
 * <AUTHOR>
 * @date 2021/1/11
 */
@Slf4j
public class RepaymentFileReader implements ItemStreamReader<RepaymentDTO> {

    private String tenantId;

    private AtomicInteger lineNum = new AtomicInteger(0);
    private FlatFileItemReader<RepaymentDTO> fileItemReader;
    private ExecutionContext executionContext;

    @Autowired
    private SettleRejectLogMapper settleRejectLogMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    public RepaymentFileReader(FlatFileItemReader<RepaymentDTO> fileItemReader, String tenantId) {
        this.fileItemReader = fileItemReader;
        this.tenantId = tenantId;
    }

    @Override
    public RepaymentDTO read() throws Exception {

        RepaymentDTO read;
        try {
            fileItemReader.open(this.executionContext);
            lineNum.incrementAndGet();
            read = fileItemReader.read();

            if (read == null) {
                return null;
            }

            if (!"00".equals(read.getCode())) {
                // 写入拒绝表
                reject(JSONObject.toJSONString(read), getCodeByKey(read.getCode()), getDesByKey(read.getCode()));
                read = new RepaymentDTO(true);
            }
        }catch (Exception e){
            // 解析文件行内容
            String line = getLineByError(e.getMessage(), lineNum.get());
            log.error("解析还款文件失败，行数: {}, 内容：{}", lineNum.get(), line);

            // 写入拒绝表
            reject(line, RejectCodeEnum.FILE_FORMAT.getCoed(), RejectCodeEnum.FILE_FORMAT.getDes());
            read = new RepaymentDTO(true);
        }

        return read;
    }

    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        this.executionContext = executionContext;
    }

    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {

    }

    @Override
    public void close() throws ItemStreamException {

    }

    private void reject(String line, String rejectCode, String rejectDes){
        SettleRejectLog rejectLog = new SettleRejectLog();

        rejectLog.setId(IdGeneratorManager.gen19Num().generateId(tenantId));
        rejectLog.setLineContent(line);
        rejectLog.setCreateTime(LocalDateTime.now());
        rejectLog.setUpdateTime(LocalDateTime.now());
        rejectLog.setUpdateBy(LoginUserUtils.getLoginUserName());
        rejectLog.setVersionNumber(1L);
        // 传入一个默认异常转存机构号
        rejectLog.setOrganizationNumber(OrgNumberUtils.getOrg());
        rejectLog.setFileProcessStatus("2");
        rejectLog.setFileSourceName("repaymentLog");
        rejectLog.setRejectCode(rejectCode);
        rejectLog.setRejectDesc(rejectDes);
        rejectLog.setFileDate(new Date());

        settleRejectLogMapper.insert(rejectLog);
    }

    private String getLineByError(String message, Integer lineNumber){
        if (StringUtils.isNotEmpty(message) && message.contains("input")) {
            String result = message.split("input")[1];
            result = result.substring(result.indexOf("[") + 1, result.indexOf("]"));

            return result;
        }

        return String.valueOf(lineNumber);
    }

    public String getCodeByKey(String key){
        Optional<ResCode> resCode = Arrays.stream(ResCode.values()).filter(x -> x.getCode().equals(key)).findFirst();
        if (resCode.isPresent()) {
            return resCode.get().getCode();
        } else {
            return RejectCodeEnum.FILE_FORMAT.getCoed();
        }
    }

    public String getDesByKey(String key){
        Optional<ResCode> resCode = Arrays.stream(ResCode.values()).filter(x -> x.getCode().equals(key)).findFirst();
        if (resCode.isPresent()) {
            return resCode.get().getDes();
        } else {
            return RejectCodeEnum.FILE_FORMAT.getDes();
        }
    }

    public enum ResCode{
        R0("00", "借记卡扣款成功"),
        R1("01", "姓名不符"),
        R3("03", "还款账户资金不足（仅日间批次支持）"),
        R4("04", "还款账户终止（仅日间批次支持）"),
        R5("05", "取消当前操作（仅日间批次支持）"),
        R6("06", "入账金额大于扣款金额（仅日间批次支持）"),
        R8("08", "账户状态不正常，扣款失败"),
        R9("09", "账户不存在，扣款失败"),
        R14("14", "无效借记卡账号，扣款失败"),
        R98("98", "币种检验错误，扣款失败"),
        R99("99", "其它原因错误，扣款失败");

        private String code;
        private String des;

        ResCode(String code, String des) {
            this.code = code;
            this.des = des;
        }

        public String getCode() {
            return code;
        }

        public String getDes() {
            return des;
        }
    }
}
