package com.anytech.anytxn.account.base.enums;

import com.anytech.anytxn.common.core.constants.MessageHandle;

/**
 * 响应描述
 * <AUTHOR>
 * @date 2021/2/26
 */
public enum AccountingRepMessageEnum implements MessageHandle {
    SUCCESS("响应正常", "success"),
    PE("参数异常", "Parameter exception"),
    PCE("参数不能为空", "Parameter cannot be empty"),
    PTW("参数类型错误", "Wrong parameter type"),
    AP_VR("参数取值范围异常", "Abnormal parameter value range"),
    AD("数据异常", "Abnormal data"),
    VUC("违反唯一约束", "Violation of unique constraint"),
    DE("数据不存在", "The data does not exist"),
    DSE("数据状态错误", "Data status error"),
    D_UI_E("数据更新与预期不一致", "Data update is inconsistent with expectation"),
    DDU("数据使用中不能删除", "Data cannot be deleted in use"),
    FA_RR("字段赋值超出合理范围","The field assignment is beyond the reasonable range"),
    FA("字段没有赋值", "Field has no assignment"),
    Q_MR("查询结果存在多个", "There are multiple query results"),
    I0("数据库插入0条数据", "Insert 0 data into database"),
    DBE("数据库异常", "Database exception"),
    OLC("乐观锁冲突", "Optimistic lock conflict"),
    CM("请检查mapper配置，抛出了PersistenceException或子类异常", "Check the mapper configuration and throw a persistenceexception or subclass exception"),
    CE("配置不存在", "Configuration does not exist"),
    CSE("配置状态错误", "Configuration status error"),
    CUE("配置更新与预期不一致", "Configuration update not as expected"),
    CDU("配置使用中不能删除", "Configuration cannot be deleted in use"),
    ACF("配置字段赋值超出合理范围", "The assignment of configuration field is beyond the reasonable range"),
    CFA("配置字段没有赋值", "Configuration field is not assigned"),
    UE("未知错误", "unknown error"),
    RU_MA("未匹配到规则", "Rule not matched"),
    BLK_FAIL("封锁失败 账户已被封锁", "Blk fail,this account is blocked."),
    UN_BLK_FAIL("解封失败 账户没被封锁", "Un Blk fail,this account not blocked."),
   SOURCE("无对应请求资源", "No corresponding request resource");

    private String CnMsg;
    private String EnMsg;

    AccountingRepMessageEnum(String cnMsg, String enMsg) {
        CnMsg = cnMsg;
        EnMsg = enMsg;
    }

    @Override
    public String getCnMsg() {
        return CnMsg;
    }

    @Override
    public String getEnMsg() {
        return EnMsg;
    }
}
