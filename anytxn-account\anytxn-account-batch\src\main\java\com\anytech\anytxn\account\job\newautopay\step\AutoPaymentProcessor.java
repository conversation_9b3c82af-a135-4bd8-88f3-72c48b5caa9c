package com.anytech.anytxn.account.job.newautopay.step;

import com.anytech.anytxn.account.base.domain.bo.GiroAutoPayBO;
import com.anytech.anytxn.account.base.service.IAccountManageInfoService;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 自扣处理
 *
 * <AUTHOR>
 * @date 2021/11/10
 */
@Slf4j
public class AutoPaymentProcessor implements ItemProcessor<AccountManagementInfo, GiroAutoPayBO> {
    @Autowired
    private IAccountManageInfoService accountManageInfoService;

    @Override
    public GiroAutoPayBO process(AccountManagementInfo accountManagementInfo) {
        log.info("管理账户id：{}", accountManagementInfo.getAccountManagementId());
        return accountManageInfoService.batchProcessAutoPay(accountManagementInfo);
    }
}
