package com.anytech.anytxn.account.service;

import com.anytech.anytxn.account.base.constants.AccoountOptinalStates;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.base.service.IAccountOptinalInfoService;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountOptionalReqDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountOptionalResDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountOptionalInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountOptionalInfo;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 账户管理信息
 *
 * <AUTHOR>
 * @date 2018-09-29
 */
@Data
@Service
public class AccountOptinalInfoServiceImpl implements IAccountOptinalInfoService {

    private static final Logger log = LoggerFactory.getLogger(AccountOptinalInfoServiceImpl.class);


    @Autowired
    private AccountOptionalInfoSelfMapper accountOptionalInfoSelfMapper;


    @Autowired
    private Number16IdGen number16IdGen;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementSelfMapper;


    @Override
    public AccountOptionalResDTO findByAccountManagementId(String accountManagementId) {

        if (ObjectUtils.isEmpty(accountManagementId)) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL);
        }

        AccountOptionalInfo accountOptionalInfo = accountOptionalInfoSelfMapper.findByAccountManagementId(accountManagementId);
        AccountOptionalResDTO accountOptionalResDTO = new AccountOptionalResDTO();
        if (ObjectUtils.isEmpty(accountOptionalInfo)) {
            accountOptionalResDTO.setAccountManagementId(accountManagementId);
            accountOptionalResDTO.setStates(AccoountOptinalStates.NARMAL.getCode());
            return accountOptionalResDTO;
        }

        accountOptionalResDTO = BeanMapping.copy(accountOptionalInfo, AccountOptionalResDTO.class);
        boolean enrollmentDate = ObjectUtils.isEmpty(accountOptionalInfo.getEnrollmentDate());
        boolean cancelDate = ObjectUtils.isEmpty(accountOptionalInfo.getCancelDate());

        // 之前有过封锁已经被解掉了
        if (!enrollmentDate && !cancelDate) {
            accountOptionalResDTO.setAccountManagementId(accountOptionalInfo.getAccountManagementId());
            accountOptionalResDTO.setOpearator(accountOptionalInfo.getUpdateBy());
            accountOptionalResDTO.setStates(AccoountOptinalStates.NARMAL.getCode());
            return accountOptionalResDTO;
        }

        // 这种情况不太可能发生，如果有就是数据异常
        if (enrollmentDate && cancelDate) {
            log.info("enrollmentDate和cancelDate同时为空！");
            accountOptionalResDTO.setAccountManagementId(accountOptionalInfo.getAccountManagementId());
            accountOptionalResDTO.setOpearator(accountOptionalInfo.getUpdateBy());
            accountOptionalResDTO.setStates(AccoountOptinalStates.NARMAL.getCode());
            return accountOptionalResDTO;
        }
        //这种情况不太可能发生，如果有就是数据异常
        if (enrollmentDate) {
            log.info("enrollmentDate为空");
            accountOptionalResDTO.setAccountManagementId(accountOptionalInfo.getAccountManagementId());
            accountOptionalResDTO.setOpearator(accountOptionalInfo.getUpdateBy());
            accountOptionalResDTO.setStates(AccoountOptinalStates.NARMAL.getCode());
            return accountOptionalResDTO;
        }

        //默认返回锁定
        //if(cancelDate){
        accountOptionalResDTO.setAccountManagementId(accountOptionalInfo.getAccountManagementId());
        accountOptionalResDTO.setOpearator(accountOptionalInfo.getUpdateBy());
        accountOptionalResDTO.setStates(AccoountOptinalStates.LOCKED.getCode());
        //return accountOptionalResDTO;
        //}
        return accountOptionalResDTO;
    }


    @Override
    public boolean insertAccountOptinalInfo(AccountOptionalReqDTO accountOptionalReqDTO) {

        if (ObjectUtils.isEmpty(accountOptionalReqDTO)) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL);
        }

        if (StringUtils.isEmpty(accountOptionalReqDTO.getAccountManagementId())) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL);
        }

        AccountOptionalInfo accountOptional = accountOptionalInfoSelfMapper.findByAccountManagementId(accountOptionalReqDTO.getAccountManagementId());

        //数据库中没查询到该管理账户，第一次锁定
        if (ObjectUtils.isEmpty(accountOptional)) {
            AccountOptionalInfo accountOptionalInfo = BeanMapping.copy(accountOptionalReqDTO, AccountOptionalInfo.class);
            accountOptionalInfo.setId(number16IdGen.generateId(TenantUtils.getTenantId()) + "");
            accountOptionalInfo.setCreateTime(LocalDateTime.now());
            accountOptionalInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
            accountOptionalInfo.setVersionNumber(1L);
            accountOptionalInfo.setFetCode("BLKCBSDEFL");
            int res = accountOptionalInfoSelfMapper.insert(accountOptionalInfo);
            return res > 0;
        }


        // 数据库中已经有该管理账户id
        boolean enrollmentDate = ObjectUtils.isEmpty(accountOptional.getEnrollmentDate());
        boolean cancelDate = ObjectUtils.isEmpty(accountOptional.getCancelDate());

        // 之前被锁定并解锁之后，第二次被锁定在原记录上修改，将新enrollDate更新将cencel滞空
        if (!enrollmentDate && !cancelDate) {
            AccountOptionalInfo accountOptionalInfo = BeanMapping.copy(accountOptionalReqDTO, AccountOptionalInfo.class);

            accountOptionalInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
            accountOptionalInfo.setId(accountOptional.getId());
            accountOptionalInfo.setCancelDate(null);
            accountOptionalInfo.setFetCode("BLKCBSDEFL");
            int update = accountOptionalInfoSelfMapper.update(accountOptionalInfo);
            return update > 0;
        }else if (!enrollmentDate){
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_BLK_FAIL);
        }else {
            return true;
        }
    }

    @Override
    public boolean modifyAccountOptinalInfo(AccountOptionalReqDTO accountOptionalReqDTO) {

        if (ObjectUtils.isEmpty(accountOptionalReqDTO)) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL);
        }

        if (StringUtils.isEmpty(accountOptionalReqDTO.getAccountManagementId())) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL);
        }

        AccountOptionalInfo accountOptionalInfo = accountOptionalInfoSelfMapper.findByAccountManagementId(accountOptionalReqDTO.getAccountManagementId());

        // 数据库中没有该管理账户信息
        if (ObjectUtils.isEmpty(accountOptionalInfo)) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR);
        }


        boolean enrollmentDate = ObjectUtils.isEmpty(accountOptionalInfo.getEnrollmentDate());
        boolean cancelDate = ObjectUtils.isEmpty(accountOptionalInfo.getCancelDate());

        if (!enrollmentDate && cancelDate) {
            // enrollDate不为空、cancelDate为空，此时可以解锁、
            AccountOptionalInfo info = BeanMapping.copy(accountOptionalReqDTO, AccountOptionalInfo.class);
            info.setId(accountOptionalInfo.getId());
            info.setUpdateBy(LoginUserUtils.getLoginUserName());
            info.setUpdateTime(LocalDateTime.now());
            int rs = accountOptionalInfoSelfMapper.update(info);
            return rs > 0;
        }else if (!cancelDate){
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_UN_BLK_FAIL);
        }else {
            return true;
        }
    }

    @Override
    public List<AccountOptionalResDTO> findByCondition(String condition, String id) {

        //根据客户id查询
        if ("C".equals(condition)) {
            List<AccountOptionalResDTO> list = new ArrayList<>(1);
            List<AccountManagementInfo> accountManagementInfo = accountManagementSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),id);

            for (AccountManagementInfo info : accountManagementInfo) {
                String accountId = info.getAccountManagementId();
                AccountOptionalResDTO dto = findAccountOptionalByAccountManagementId(accountId);
                list.add(dto);
            }
            return list;
        }

        //根据管理账户id查询
        if ("A".equals(condition)) {
            List<AccountOptionalResDTO> list = new ArrayList<>(1);
            AccountOptionalResDTO dto = findAccountOptionalByAccountManagementId(id);
            list.add(dto);
            return list;
        }
        throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR);
    }

    public AccountOptionalResDTO findAccountOptionalByAccountManagementId(String accountManagementId) {

        if (ObjectUtils.isEmpty(accountManagementId)) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL);
        }

        AccountOptionalInfo accountOptionalInfo = accountOptionalInfoSelfMapper.findByAccountManagementId(accountManagementId);
        // 数据库中没有该管理账户信息
        if (ObjectUtils.isEmpty(accountOptionalInfo)) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR);
        }
        AccountOptionalResDTO accountOptionalResDTO = BeanMapping.copy(accountOptionalInfo, AccountOptionalResDTO.class) ;
        accountOptionalResDTO.setOpearator(accountOptionalInfo.getUpdateBy());
        return accountOptionalResDTO;
    }

}
