package com.anytech.anytxn.accounting.batch.job.glamsfromacquirer.step;


import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsMapper;
import com.anytech.anytxn.common.core.constants.PropsConstant;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class GlamsFromAcquirerTasklet implements Tasklet {

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.glams-from-acquirer.file-path}")
    private String filePath;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.glams-from-acquirer.date-flag:false}")
    private Boolean dateFlag;

    @Autowired
    AccountantGlamsMapper accountantGlamsMapper;

    @Autowired
    ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        if(dateFlag){
            ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(OrgNumberUtils.getOrg());
            LocalDate today = parmOrganizationInfo.getToday();
            String yyyyMMdd = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            filePath = filePath.concat(File.separator).concat(yyyyMMdd);
        }

        File file = new File(filePath);

        if(file.exists()){
            File[] files = file.listFiles();

            if(files.length!=0){

                for (File subFile:files
                     ) {
                    if(subFile.isFile() && subFile.getName().startsWith("GLTDCS") && subFile.getName().endsWith("TXT")){
                        processFile(subFile);
                    }
                }

            }
        }

        return RepeatStatus.FINISHED;
    }


    private void processFile(File file) throws IOException {
        //文件解析
        BufferedReader bufferedReader = new BufferedReader(new FileReader(file));

        String line = bufferedReader.readLine();

        while(line!=null){

            //头和尾跳过
            if(line.startsWith("H") || line.startsWith("T")){
                line = bufferedReader.readLine();
                continue;
            }

            //c(1)
            String recordType = line.substring(0,1).trim();
            //n(6,0)
            String sequenceNo=line.substring(1,7).trim();
            //c(19)
            String cardNumber=line.substring(7,26).trim();
            //c(4)
            String cardProductCode = line.substring(26,30).trim();
            //c(1)
            String cardType = line.substring(30,31).trim();
            //c(6)
            String servicingBranch = line.substring(31,37).trim();
            //c(6)
            String acquirerId = line.substring(37,43).trim();
            //c(2)
            String ageCodeBeforeTransactionIsPosted = line.substring(43,45).trim();
            //n(8,0)
            String postingDate = line.substring(45,53).trim();
            //n(3,0)
            String batchNo = line.substring(53,56).trim();
            //c(10)
            String transactionCode = line.substring(56,66).trim();
            //n(3,0)
            String postingCurrencyCode = line.substring(66,69).trim();
            //n(3,0)
            String transactionCurrencyCode = line.substring(69,72).trim();
            //n(3,0)
            String settlementCurrencyCode = line.substring(72,75).trim();
            //-n(14,3)
            String beforePostingOutstandingBalance = line.substring(75,90).trim();
            //-n(14,3)
            String postingAmount = line.substring(90,105).trim();
            //c(4)
            String sourceId = line.substring(105,109).trim();
            //c(4)
            String settlementParty = line.substring(109,113).trim();
            //c(1)
            String cardStatus = line.substring(113,114).trim();
            //c(4)
            String cardStatusReasonCode = line.substring(114,118).trim();
            //c(4)
            String mccCode = line.substring(118,122).trim();
            //c(4)
            String merchantBankPayeeCode = line.substring(122,126).trim();
            //c(6)
            String paymentProcessingBranchCode = line.substring(126,132).trim();
            //c(30)
            String merchantGstRegistrationNo = line.substring(132,162).trim();
            //c(15)
            String cardAcceptorId = line.substring(162,177).trim();
            //c(3)
            String usageCode = line.substring(177,180).trim();
            //c(40)
            String usageCodeDescription = line.substring(180,220).trim();
            //c(1)
            String debitCreditIndicator = line.substring(220,221).trim();
            //c(4)
            String organizationNumber = line.substring(221,225).trim();
            //c(8)
            String transactionDate = line.substring(225,233).trim();
            //c(32)
            String globalFlowNo = line.substring(233,265).trim();
            //c(60)
            String transactionCodeDesc = line.length() > 265 ? line.substring(265,325).trim(): null;
            //c(1)
            String cardScheme = line.length() > 325 ? line.substring(325,326).trim(): null;

            //glams
            AccountantGlams accountantGlams = new AccountantGlams();

            accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            accountantGlams.setOrganizationNumber(OrgNumberUtils.getOrg());
            accountantGlams.setBranchid(servicingBranch);
            accountantGlams.setAccountManagementId(cardNumber);
            accountantGlams.setGlobalFlowNo(globalFlowNo);
            accountantGlams.setModuleFlag("M");
            accountantGlams.setTxnCode(transactionCode);
            accountantGlams.setTxnCodeOrig(transactionCode);
            accountantGlams.setPostingDate(LocalDate.parse(postingDate, DateTimeFormatter.ofPattern("yyyyMMdd")));
            accountantGlams.setPostingCurrencyCode(postingCurrencyCode);
            accountantGlams.setPostingAmt(new BigDecimal(postingAmount).divide(new BigDecimal(1000)).abs());
            accountantGlams.setFinanceStatus("0");
            accountantGlams.setInterestInd("0");
            accountantGlams.setPriceTaxFlg("N");
            accountantGlams.setProcessInd("0");
            accountantGlams.setVersionNumber(1l);
            accountantGlams.setSubchannelId(sourceId);
            accountantGlams.setMerchantNumber(cardAcceptorId);
            accountantGlams.setDebitCreditIndicator(debitCreditIndicator);
            accountantGlams.setCrdNumber(cardNumber);
            accountantGlams.setCrdProductNumber(cardProductCode);
            accountantGlams.setCrdType("M");
            accountantGlams.setTxnAmt(new BigDecimal(beforePostingOutstandingBalance).divide(new BigDecimal(1000)).abs());
            accountantGlams.setTxnCurrency(transactionCurrencyCode);
            accountantGlams.setSettleAmt(accountantGlams.getTxnAmt());
            accountantGlams.setSettleCurrency(settlementCurrencyCode);
            accountantGlams.setTxnDate(LocalDateTime.parse(transactionDate+"000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            accountantGlams.setTxnSource(sourceId);
            accountantGlams.setCurrencyRate("1.0".equals(merchantGstRegistrationNo)?"1":merchantGstRegistrationNo);
            accountantGlams.setRrn(usageCodeDescription);
            accountantGlams.setAcqId(acquirerId);
            accountantGlams.setCreateTime(LocalDateTime.now());
            accountantGlams.setPartitionKey(0l);
            accountantGlams.setTxnDescription(transactionCodeDesc);
            accountantGlams.setPlatformId(cardScheme);


            accountantGlamsMapper.insert(accountantGlams);

            line = bufferedReader.readLine();
        }
    }


}
