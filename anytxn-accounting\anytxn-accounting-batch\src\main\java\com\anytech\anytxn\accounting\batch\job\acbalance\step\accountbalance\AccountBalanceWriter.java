package com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance;

import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlactbalSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlactbal;
import com.google.common.collect.Lists;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlactbalDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3
 */
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class AccountBalanceWriter implements ItemWriter<AccountantGlactbalDTO> {

    @Autowired
    private AccountantGlactbalSelfMapper tAmsGlactbalSelfMapper;

    @Override
    public void write(Chunk<? extends AccountantGlactbalDTO> chunk) throws Exception {
        List<? extends AccountantGlactbalDTO> items = chunk.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            tAmsGlactbalSelfMapper.insertBatch(BeanMapping.copyList(Lists.newArrayList(items), AccountantGlactbal.class));
        }
    }

}
